<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="DuplicatedCode" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <Languages>
        <language minSize="149" name="Python" />
      </Languages>
    </inspection_tool>
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="37">
            <item index="0" class="java.lang.String" itemvalue="tushare" />
            <item index="1" class="java.lang.String" itemvalue="gensim" />
            <item index="2" class="java.lang.String" itemvalue="pandas-ta" />
            <item index="3" class="java.lang.String" itemvalue="scikit-learn" />
            <item index="4" class="java.lang.String" itemvalue="nltk" />
            <item index="5" class="java.lang.String" itemvalue="requests" />
            <item index="6" class="java.lang.String" itemvalue="numpy" />
            <item index="7" class="java.lang.String" itemvalue="catboost" />
            <item index="8" class="java.lang.String" itemvalue="langchain-community" />
            <item index="9" class="java.lang.String" itemvalue="tensorflow" />
            <item index="10" class="java.lang.String" itemvalue="celery" />
            <item index="11" class="java.lang.String" itemvalue="langchain" />
            <item index="12" class="java.lang.String" itemvalue="apscheduler" />
            <item index="13" class="java.lang.String" itemvalue="uvicorn" />
            <item index="14" class="java.lang.String" itemvalue="xgboost" />
            <item index="15" class="java.lang.String" itemvalue="beautifulsoup4" />
            <item index="16" class="java.lang.String" itemvalue="scipy" />
            <item index="17" class="java.lang.String" itemvalue="baostock" />
            <item index="18" class="java.lang.String" itemvalue="transformers" />
            <item index="19" class="java.lang.String" itemvalue="sentence-transformers" />
            <item index="20" class="java.lang.String" itemvalue="torch" />
            <item index="21" class="java.lang.String" itemvalue="akshare" />
            <item index="22" class="java.lang.String" itemvalue="sqlalchemy" />
            <item index="23" class="java.lang.String" itemvalue="mlflow" />
            <item index="24" class="java.lang.String" itemvalue="pandas" />
            <item index="25" class="java.lang.String" itemvalue="fastapi" />
            <item index="26" class="java.lang.String" itemvalue="langchain-core" />
            <item index="27" class="java.lang.String" itemvalue="flask" />
            <item index="28" class="java.lang.String" itemvalue="websockets" />
            <item index="29" class="java.lang.String" itemvalue="rsa" />
            <item index="30" class="java.lang.String" itemvalue="docx2txt" />
            <item index="31" class="java.lang.String" itemvalue="google-ai-generativelanguage" />
            <item index="32" class="java.lang.String" itemvalue="faiss-cpu" />
            <item index="33" class="java.lang.String" itemvalue="python-dotenv" />
            <item index="34" class="java.lang.String" itemvalue="markdown" />
            <item index="35" class="java.lang.String" itemvalue="pillow" />
            <item index="36" class="java.lang.String" itemvalue="sympy" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8NamingInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="N802" />
          <option value="N806" />
          <option value="N803" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>