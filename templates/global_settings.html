<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI招聘客服管理系统 - 全局配置</title>
    <link rel="stylesheet" href="/static/css/all.min.css">
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="stylesheet" href="/static/css/bootstrap.min.css">
    <style>
        /* 现代化全局配置页面样式 */
        body {
            font-size: 14px;
            background-color: #f5f7fa;
        }

        .main-content {
            background-color: #f5f7fa;
        }

        .content {
            padding: 20px;
            max-height: calc(100vh - 80px);
            overflow-y: auto;
        }

        /* 页面标题样式 */
        .page-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e1e8ed;
        }

        .page-title {
            margin: 0 0 10px 0;
            font-size: 28px;
            font-weight: 700;
            color: #2d3748;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
        }

        .page-title i {
            color: #667eea;
            font-size: 24px;
        }

        .page-description {
            margin: 0;
            color: #718096;
            font-size: 16px;
            line-height: 1.5;
        }

        /* 设置容器 - 使用网格布局 */
        .settings-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        /* 设置面板现代化样式 */
        .settings-panel {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            border: 1px solid #e1e8ed;
        }

        .settings-panel:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .panel-header {
            padding: 20px 24px 16px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            position: relative;
            overflow: hidden;
        }

        .panel-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }

        .panel-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            position: relative;
            z-index: 1;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .panel-header h3::before {
            content: '';
            width: 4px;
            height: 20px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 2px;
        }

        .panel-body {
            padding: 24px;
        }

        /* 表单样式现代化 */
        .form-group {
            margin-bottom: 18px;
        }

        .form-group:last-child {
            margin-bottom: 0;
        }

        .form-label {
            font-weight: 600;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
            color: #2d3748;
            font-size: 14px;
        }

        .form-label i {
            color: #667eea;
            width: 16px;
            text-align: center;
        }

        .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.2s ease;
            background-color: #fff;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background-color: #fff;
        }

        .form-control:hover {
            border-color: #cbd5e0;
        }

        .text-muted {
            font-size: 12px;
            color: #718096;
            margin-top: 4px;
            line-height: 1.4;
        }

        /* 按钮现代化样式 */
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            margin-right: 12px;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #718096 0%, #4a5568 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(113, 128, 150, 0.3);
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(113, 128, 150, 0.4);
        }

        /* 复选框样式 */
        .form-check {
            display: flex;
            align-items: center;
            gap: 12px;
            margin: 16px 0;
        }

        .form-check-input {
            width: 18px;
            height: 18px;
            margin: 0;
            accent-color: #667eea;
        }

        .form-check-label {
            font-weight: 500;
            color: #2d3748;
            cursor: pointer;
        }

        /* 通知样式现代化 */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 16px 20px;
            border-radius: 12px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.15);
            z-index: 1050;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            opacity: 1;
            min-width: 300px;
            backdrop-filter: blur(10px);
        }

        .notification-success {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .notification-error {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .notification-hide {
            opacity: 0;
            transform: translateX(100px) scale(0.9);
        }

        /* 加载动画现代化 */
        .loader {
            display: none;
            margin-left: 12px;
        }

        .loader-spinner {
            display: inline-block;
            width: 18px;
            height: 18px;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 0.8s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .settings-container {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .content {
                padding: 16px;
            }

            .panel-body {
                padding: 20px;
            }

            .btn {
                width: 100%;
                justify-content: center;
                margin-bottom: 8px;
                margin-right: 0;
            }
        }

        /* 滚动条美化 */
        .content::-webkit-scrollbar {
            width: 6px;
        }

        .content::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .content::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .content::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* 页面加载动画 */
        .settings-panel {
            animation: slideInUp 0.6s ease-out;
        }

        .settings-panel:nth-child(2) {
            animation-delay: 0.1s;
        }

        .settings-panel:nth-child(3) {
            animation-delay: 0.2s;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 输入框聚焦动画 */
        .form-control {
            position: relative;
        }

        .form-control:focus {
            transform: translateY(-1px);
        }

        /* 按钮点击效果 */
        .btn:active {
            transform: translateY(1px);
        }

        /* 成功状态动画 */
        .form-control.success {
            border-color: #48bb78;
            box-shadow: 0 0 0 3px rgba(72, 187, 120, 0.1);
            animation: successPulse 0.6s ease-out;
        }

        @keyframes successPulse {
            0% {
                box-shadow: 0 0 0 0 rgba(72, 187, 120, 0.4);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(72, 187, 120, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(72, 187, 120, 0);
            }
        }
    </style>
</head>
<body>
    <div class="app-wrapper">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>AI招聘客服管理系统</h2>
            </div>
            <div class="sidebar-menu">
                <div class="sidebar-menu-item" data-link="index">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>仪表盘</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-briefcase"></i>
                    <span>职位管理</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="job_classification">
                        <i class="fas fa-tags"></i>
                        <span>职位分类</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="batch_management">
                        <i class="fas fa-layer-group"></i>
                        <span>批量管理</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="positions">
                        <i class="fas fa-list"></i>
                        <span>职位列表</span>
                    </div>
                </div>
                <div class="sidebar-menu-item" data-link="candidates">
                    <i class="fas fa-user-tie"></i>
                    <span>候选人管理</span>
                </div>
                <div class="sidebar-menu-item" data-link="virtual_hr">
                    <i class="fas fa-robot"></i>
                    <span>虚拟HR</span>
                </div>
                <div class="sidebar-menu-item" data-link="scenes">
                    <i class="fas fa-tasks"></i>
                    <span>面试场景</span>
                </div>
                <div class="sidebar-menu-item" data-link="interview">
                    <i class="fas fa-comments"></i>
                    <span>面试互动（仅供测试）</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-star"></i>
                    <span>胜任力模型</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="triggers">
                        <i class="fas fa-bolt"></i>
                        <span>触发器</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="classifier">
                        <i class="fas fa-filter"></i>
                        <span>分类器</span>
                    </div>
                </div>
                <div class="sidebar-menu-item" data-link="ai_brain">
                    <i class="fas fa-brain"></i>
                    <span>AI大脑</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-chart-line"></i>
                    <span>AI数据透视</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="scheduled_analysis">
                        <i class="fas fa-clock"></i>
                        <span>客情分析</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="qa_engine">
                        <i class="fas fa-question-circle"></i>
                        <span>QA引擎</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-key"></i>
                    <span>APIKey管理</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="apikey_create">
                        <i class="fas fa-plus"></i>
                        <span>创建APIKey</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="apikey_usage">
                        <i class="fas fa-chart-bar"></i>
                        <span>用量统计</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu active">
                    <i class="fas fa-cog"></i>
                    <span>用户设置</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu show">
                    <div class="sidebar-menu-item" data-link="channel_management">
                        <i class="fas fa-plug"></i>
                        <span>渠道接入管理</span>
                    </div>
                    <div class="sidebar-menu-item active" data-link="global_settings">
                        <i class="fas fa-sliders-h"></i>
                        <span>全局配置</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-cogs"></i>
                    <span>系统设置</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item admin-only" data-link="user_management07082">
                        <i class="fas fa-user-cog"></i>
                        <span>用户管理</span>
                    </div>
                    <div class="sidebar-menu-item admin-only" data-link="system_config">
                        <i class="fas fa-wrench"></i>
                        <span>系统配置</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部导航栏 -->
            <div class="topbar">
                <div class="topbar-toggle">
                    <i class="fas fa-bars"></i>
                </div>
                <div class="topbar-title">
                    全局配置
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content">
                <!-- 页面标题 -->
                <div class="page-header">
                    <h1 class="page-title">
                        <i class="fas fa-cog"></i>
                        全局配置
                    </h1>
                    <p class="page-description">
                        配置系统的全局设置和邮件服务器，确保系统正常运行
                    </p>
                </div>

                <div class="settings-container">
                    <div class="settings-panel">
                        <div class="panel-header">
                            <h3>系统全局设置</h3>
                        </div>
                        <div class="panel-body">
                            <form id="globalSettingsForm">
                                <div class="form-group">
                                    <label for="userTitle" class="form-label">
                                        <i class="fas fa-user-tag"></i> 用户称呼
                                    </label>
                                    <input type="text" class="form-control" id="userTitle" name="user_title" placeholder="请输入用户称呼，如：您、亲爱的候选人">
                                    <small class="text-muted">系统将使用此称呼来称呼候选人</small>
                                </div>

                                <div class="form-group">
                                    <label for="userActivationCommand" class="form-label">
                                        <i class="fas fa-comment-dots"></i> 绑定用户激活对话命令
                                    </label>
                                    <input type="text" class="form-control" id="userActivationCommand" name="user_activation_command" placeholder="请输入激活对话的命令，如：开始对话、激活">
                                    <small class="text-muted">用户发送此命令时将激活对话功能</small>
                                </div>

                                <div class="form-group">
                                    <button type="submit" class="btn btn-primary" id="saveSettings">
                                        <i class="fas fa-save"></i> 保存设置
                                        <span class="loader" id="saveLoader">
                                            <span class="loader-spinner"></span>
                                        </span>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- 邮件配置面板 -->
                    <div class="settings-panel">
                        <div class="panel-header">
                            <h3><i class="fas fa-envelope"></i> 邮件服务器配置</h3>
                        </div>
                        <div class="panel-body">
                            <form id="emailSettingsForm">
                                <div class="form-group">
                                    <label for="smtpServer" class="form-label">
                                        <i class="fas fa-server"></i> SMTP服务器
                                    </label>
                                    <input type="text" class="form-control" id="smtpServer" name="smtp_server" placeholder="smtp.gmail.com">
                                    <small class="text-muted">邮件服务器的SMTP地址</small>
                                </div>

                                <div class="form-group">
                                    <label for="smtpPort" class="form-label">
                                        <i class="fas fa-plug"></i> SMTP端口
                                    </label>
                                    <input type="number" class="form-control" id="smtpPort" name="smtp_port" value="587" placeholder="587">
                                    <small class="text-muted">通常为587（TLS）或465（SSL）</small>
                                </div>

                                <div class="form-group">
                                    <label for="smtpUsername" class="form-label">
                                        <i class="fas fa-at"></i> 邮箱地址
                                    </label>
                                    <input type="email" class="form-control" id="smtpUsername" name="smtp_username" placeholder="<EMAIL>">
                                    <small class="text-muted">用于发送邮件的邮箱地址</small>
                                </div>

                                <div class="form-group">
                                    <label for="smtpPassword" class="form-label">
                                        <i class="fas fa-key"></i> 邮箱密码
                                    </label>
                                    <input type="password" class="form-control" id="smtpPassword" name="smtp_password" placeholder="邮箱密码或应用专用密码">
                                    <small class="text-muted">建议使用应用专用密码</small>
                                </div>

                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="smtpUseTls" name="smtp_use_tls" checked>
                                    <label class="form-check-label" for="smtpUseTls">
                                        <i class="fas fa-shield-alt"></i> 启用TLS加密
                                    </label>
                                </div>
                                <small class="text-muted">推荐启用以确保邮件传输安全</small>

                                <div class="form-group" style="margin-top: 20px;">
                                    <button type="button" class="btn btn-secondary" id="testEmailBtn">
                                        <i class="fas fa-paper-plane"></i> 测试连接
                                        <span class="loader" id="testLoader">
                                            <span class="loader-spinner"></span>
                                        </span>
                                    </button>
                                    <button type="submit" class="btn btn-primary" id="saveEmailSettings">
                                        <i class="fas fa-save"></i> 保存配置
                                        <span class="loader" id="emailSaveLoader">
                                            <span class="loader-spinner"></span>
                                        </span>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 通知提示框 -->
    <div id="notification" class="notification notification-success notification-hide">
        <span id="notificationMessage">设置已保存成功！</span>
    </div>

    <script src="/static/js/jquery-3.6.0.min.js"></script>
    <script src="/static/js/main.js"></script>
    <script src="/static/js/auth07082.js"></script>
    <script src="/static/js/auth-check.js"></script>
    <script src="/static/js/user-logout.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('globalSettingsForm');
            const loader = document.getElementById('saveLoader');
            const notification = document.getElementById('notification');
            const notificationMessage = document.getElementById('notificationMessage');
            let tenantId = '';
            
            // 检查登录状态并获取租户ID
            async function checkLogin() {
                if (!isAuthenticated()) {
                    window.location.href = '/login07082';
                    return;
                }
                
                // 从JWT获取用户信息
                const currentUser = getCurrentUser();
                if (!currentUser || currentUser.userType !== 'tenant') {
                    showNotification('只有租户用户可以访问该页面', 'error');
                    setTimeout(() => {
                        window.location.href = '/index';
                    }, 2000);
                    return;
                }
                
                tenantId = currentUser.id;
                loadSettings();
                loadEmailSettings();
            }

            // 加载设置
            async function loadSettings() {
                try {
                    const response = await authenticatedFetch(`/api/tenant/${tenantId}/global-settings`);
                    const data = await response.json();

                    if (data && !data.message) {
                        document.getElementById('userTitle').value = data.user_title || '';
                        document.getElementById('userActivationCommand').value = data.user_activation_command || '';
                        // 加载其他配置项...
                    }
                } catch (error) {
                    console.error('加载设置失败:', error);
                    showNotification('加载设置失败，请刷新重试', 'error');
                }
            }

            // 加载邮件设置
            async function loadEmailSettings() {
                try {
                    const response = await authenticatedFetch(`/api/tenant/${tenantId}/email-settings`);
                    const result = await response.json();

                    if (response.ok && result.data) {
                        const data = result.data;
                        document.getElementById('smtpServer').value = data.smtp_server || '';
                        document.getElementById('smtpPort').value = data.smtp_port || 587;
                        document.getElementById('smtpUsername').value = data.smtp_username || '';
                        document.getElementById('smtpPassword').value = data.smtp_password === '******' ? '' : data.smtp_password;
                        document.getElementById('smtpUseTls').checked = data.smtp_use_tls !== false;
                    }
                } catch (error) {
                    console.error('加载邮件设置失败:', error);
                }
            }
            
            // 保存设置
            form.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const settings = {
                    user_title: document.getElementById('userTitle').value,
                    user_activation_command: document.getElementById('userActivationCommand').value
                    // 添加其他配置项...
                };
                
                loader.style.display = 'inline-block';
                
                try {
                    const response = await authenticatedFetch(`/api/tenant/${tenantId}/global-settings`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(settings)
                    });
                    
                    const result = await response.json();
                    
                    if (response.ok) {
                        showNotification('设置保存成功！', 'success');
                        // 添加成功动画效果
                        document.getElementById('userTitle').classList.add('success');
                        document.getElementById('userActivationCommand').classList.add('success');
                        setTimeout(() => {
                            document.getElementById('userTitle').classList.remove('success');
                            document.getElementById('userActivationCommand').classList.remove('success');
                        }, 2000);
                    } else {
                        showNotification('设置保存失败: ' + (result.detail || '未知错误'), 'error');
                    }
                } catch (error) {
                    console.error('设置保存失败:', error);
                    showNotification('设置保存失败，请稍后重试', 'error');
                } finally {
                    loader.style.display = 'none';
                }
            });

            // 邮件设置表单处理
            const emailForm = document.getElementById('emailSettingsForm');
            const emailLoader = document.getElementById('emailSaveLoader');
            const testLoader = document.getElementById('testLoader');

            emailForm.addEventListener('submit', async function(e) {
                e.preventDefault();

                const emailSettings = {
                    smtp_server: document.getElementById('smtpServer').value,
                    smtp_port: parseInt(document.getElementById('smtpPort').value),
                    smtp_username: document.getElementById('smtpUsername').value,
                    smtp_password: document.getElementById('smtpPassword').value,
                    smtp_use_tls: document.getElementById('smtpUseTls').checked
                };

                emailLoader.style.display = 'inline-block';

                try {
                    const response = await authenticatedFetch(`/api/tenant/${tenantId}/email-settings`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(emailSettings)
                    });

                    const result = await response.json();

                    if (response.ok) {
                        showNotification('邮件配置保存成功！', 'success');
                        // 添加成功动画效果
                        ['smtpServer', 'smtpPort', 'smtpUsername', 'smtpPassword'].forEach(id => {
                            const element = document.getElementById(id);
                            element.classList.add('success');
                            setTimeout(() => {
                                element.classList.remove('success');
                            }, 2000);
                        });
                    } else {
                        showNotification('邮件配置保存失败: ' + (result.detail || '未知错误'), 'error');
                    }
                } catch (error) {
                    console.error('邮件配置保存失败:', error);
                    showNotification('邮件配置保存失败，请稍后重试', 'error');
                } finally {
                    emailLoader.style.display = 'none';
                }
            });

            // 测试邮件连接
            document.getElementById('testEmailBtn').addEventListener('click', async function() {
                testLoader.style.display = 'inline-block';

                try {
                    const response = await authenticatedFetch(`/api/tenant/${tenantId}/email-test`, {
                        method: 'POST'
                    });

                    const result = await response.json();

                    if (result.success) {
                        showNotification('邮件连接测试成功！', 'success');
                    } else {
                        showNotification('邮件连接测试失败: ' + result.message, 'error');
                    }
                } catch (error) {
                    console.error('邮件连接测试失败:', error);
                    showNotification('邮件连接测试失败，请检查配置', 'error');
                } finally {
                    testLoader.style.display = 'none';
                }
            });

            // 显示通知
            function showNotification(message, type) {
                notificationMessage.textContent = message;
                notification.className = 'notification notification-' + (type || 'success');
                
                // 显示通知
                setTimeout(() => {
                    notification.classList.remove('notification-hide');
                }, 100);
                
                // 3秒后隐藏
                setTimeout(() => {
                    notification.classList.add('notification-hide');
                }, 3000);
            }
            
            // 初始化页面
            checkLogin();
        });
    </script>
</body>
</html> 