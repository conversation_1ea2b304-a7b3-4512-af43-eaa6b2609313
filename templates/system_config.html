<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI招聘客服管理系统 - 系统配置</title>
    <link rel="stylesheet" href="/static/css/all.min.css">
    <link rel="stylesheet" href="/static/css/main.css">
    <style>
        /* 系统配置页面特定样式 */
        .content {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .config-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: all 0.3s;
        }

        .config-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        .config-card-header {
            display: flex;
            align-items: center;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-bottom: 1px solid var(--border-color);
        }

        .config-card-icon {
            width: 50px;
            height: 50px;
            background: var(--primary-color);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            margin-right: 15px;
        }

        .config-card-title h3 {
            margin: 0 0 5px 0;
            font-size: 18px;
            font-weight: 600;
            color: var(--text-dark);
        }

        .config-card-title p {
            margin: 0;
            font-size: 14px;
            color: var(--text-muted);
        }

        .config-card-body {
            padding: 20px;
        }

        .upload-area-compact {
            border: 2px dashed var(--border-color);
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s;
            cursor: pointer;
            margin-bottom: 15px;
        }

        .upload-area-compact:hover {
            border-color: var(--primary-color);
            background: rgba(52, 152, 219, 0.05);
        }

        .upload-area-compact.dragover {
            border-color: var(--primary-color);
            background: rgba(52, 152, 219, 0.1);
        }

        .upload-content {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            color: var(--text-muted);
        }

        .upload-content i {
            font-size: 20px;
        }

        .current-qr-section {
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid var(--border-color);
        }

        .current-qr-section h4 {
            margin-bottom: 15px;
            color: var(--text-dark);
            font-weight: 600;
        }

        .upload-section h4 {
            margin-bottom: 15px;
            color: var(--text-dark);
            font-weight: 600;
        }

        .current-qr-display {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .current-qr-image {
            max-width: 200px;
            max-height: 200px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .current-qr-info {
            margin-top: 15px;
            color: var(--text-muted);
        }

        .no-qr-message {
            color: var(--text-muted);
            font-style: italic;
        }

        .qr-actions-current {
            margin-top: 15px;
        }

        .btn-replace {
            background: #ffc107;
            color: #212529;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            margin-right: 10px;
        }

        .btn-replace:hover {
            background: #e0a800;
        }

        .btn-delete {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-delete:hover {
            background: #c0392b;
        }



        .progress-bar {
            width: 100%;
            height: 4px;
            background: var(--border-color);
            border-radius: 2px;
            overflow: hidden;
            margin-top: 10px;
            display: none;
        }

        .progress-fill {
            height: 100%;
            background: var(--primary-color);
            width: 0%;
            transition: width 0.3s;
        }

        .upload-status {
            margin-top: 10px;
            padding: 8px 12px;
            border-radius: 4px;
            display: none;
            font-size: 14px;
        }

        .upload-status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .upload-status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="app-wrapper">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>AI招聘客服管理系统</h2>
            </div>
            <div class="sidebar-menu">
                <div class="sidebar-menu-item" data-link="index">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>仪表盘</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-briefcase"></i>
                    <span>职位管理</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="job_classification">
                        <i class="fas fa-tags"></i>
                        <span>职位分类</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="batch_management">
                        <i class="fas fa-layer-group"></i>
                        <span>批量管理</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="positions">
                        <i class="fas fa-list"></i>
                        <span>职位列表</span>
                    </div>
                </div>
                <div class="sidebar-menu-item" data-link="candidates">
                    <i class="fas fa-user-tie"></i>
                    <span>候选人管理</span>
                </div>
                <div class="sidebar-menu-item" data-link="virtual_hr">
                    <i class="fas fa-robot"></i>
                    <span>虚拟HR</span>
                </div>
                <div class="sidebar-menu-item" data-link="scenes">
                    <i class="fas fa-tasks"></i>
                    <span>面试场景</span>
                </div>
                <div class="sidebar-menu-item" data-link="interview">
                    <i class="fas fa-comments"></i>
                    <span>面试互动（仅供测试）</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-star"></i>
                    <span>胜任力模型</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="triggers">
                        <i class="fas fa-bolt"></i>
                        <span>触发器</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="classifier">
                        <i class="fas fa-filter"></i>
                        <span>分类器</span>
                    </div>
                </div>
                <div class="sidebar-menu-item" data-link="ai_brain">
                    <i class="fas fa-brain"></i>
                    <span>AI大脑</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-chart-line"></i>
                    <span>AI数据透视</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="scheduled_analysis">
                        <i class="fas fa-clock"></i>
                        <span>客情分析</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="qa_engine">
                        <i class="fas fa-question-circle"></i>
                        <span>QA引擎</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-key"></i>
                    <span>APIKey管理</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="apikey_create">
                        <i class="fas fa-plus"></i>
                        <span>创建APIKey</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="apikey_usage">
                        <i class="fas fa-chart-bar"></i>
                        <span>用量统计</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-cog"></i>
                    <span>用户设置</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="channel_management">
                        <i class="fas fa-plug"></i>
                        <span>渠道接入管理</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="global_settings">
                        <i class="fas fa-sliders-h"></i>
                        <span>全局配置</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu open">
                    <i class="fas fa-cogs"></i>
                    <span>系统设置</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu show">
                    <div class="sidebar-menu-item admin-only" data-link="user_management07082">
                        <i class="fas fa-user-cog"></i>
                        <span>用户管理</span>
                    </div>
                    <div class="sidebar-menu-item admin-only active" data-link="system_config">
                        <i class="fas fa-wrench"></i>
                        <span>系统配置</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部导航栏 -->
            <div class="topbar">
                <div class="topbar-toggle">
                    <i class="fas fa-bars"></i>
                </div>
                <div class="topbar-title">
                    系统配置
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content">
                <!-- 群二维码管理 -->
                <div class="config-card">
                    <div class="config-card-header">
                        <div class="config-card-icon">
                            <i class="fas fa-qrcode"></i>
                        </div>
                        <div class="config-card-title">
                            <h3>群二维码管理</h3>
                            <p>上传和管理系统中的群二维码图片</p>
                        </div>
                    </div>
                    <div class="config-card-body">
                        <!-- 当前二维码显示 -->
                        <div class="current-qr-section" id="currentQrSection">
                            <h4>当前群二维码</h4>
                            <div class="current-qr-display" id="currentQrDisplay">
                                <!-- 动态加载当前二维码 -->
                            </div>
                        </div>

                        <!-- 上传区域 -->
                        <div class="upload-section">
                            <h4>上传新的群二维码</h4>
                            <div class="upload-area-compact" id="uploadArea">
                                <div class="upload-content">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                    <span>点击上传二维码（将覆盖当前二维码）</span>
                                </div>
                                <input type="file" id="fileInput" accept="image/*" style="display: none;">
                            </div>

                            <!-- 进度条 -->
                            <div class="progress-bar" id="progressBar">
                                <div class="progress-fill" id="progressFill"></div>
                            </div>

                            <!-- 上传状态 -->
                            <div class="upload-status" id="uploadStatus"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 脚本引用 -->
    <script src="/static/js/main.js"></script>
    <script src="/static/js/api.js"></script>
    <script src="/static/js/sweetalert2@11"></script>
    <script src="/static/js/auth07082.js"></script>
    <script src="/static/js/auth-check.js"></script>
    <script src="/static/js/user-logout.js"></script>
    <script>
        // 系统配置页面脚本
        document.addEventListener('DOMContentLoaded', function() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');
            const progressBar = document.getElementById('progressBar');
            const progressFill = document.getElementById('progressFill');
            const uploadStatus = document.getElementById('uploadStatus');
            const currentQrDisplay = document.getElementById('currentQrDisplay');

            // 加载当前二维码
            loadCurrentQR();

            // 点击上传区域
            uploadArea.addEventListener('click', () => {
                fileInput.click();
            });

            // 文件选择
            fileInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    uploadFile(e.target.files[0]);
                }
            });

            // 拖拽上传
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    uploadFile(files[0]);
                }
            });

            // 上传文件
            function uploadFile(file) {
                // 检查文件类型
                if (!file.type.startsWith('image/')) {
                    showUploadStatus('请选择图片文件', 'error');
                    return;
                }

                // 检查文件大小 (5MB)
                if (file.size > 5 * 1024 * 1024) {
                    showUploadStatus('文件大小不能超过5MB', 'error');
                    return;
                }

                const formData = new FormData();
                formData.append('file', file);

                // 显示进度条
                progressBar.style.display = 'block';
                uploadStatus.style.display = 'none';

                fetch('/api/system/upload-qr-code', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
                    },
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    progressBar.style.display = 'none';

                    if (data.code === 200) {
                        showUploadStatus('群二维码更新成功！', 'success');
                        loadCurrentQR(); // 重新加载当前二维码
                        fileInput.value = ''; // 清空文件选择
                    } else {
                        showUploadStatus(data.message || '上传失败', 'error');
                    }
                })
                .catch(error => {
                    progressBar.style.display = 'none';
                    showUploadStatus('上传失败：' + error.message, 'error');
                });
            }

            // 显示上传状态
            function showUploadStatus(message, type) {
                uploadStatus.textContent = message;
                uploadStatus.className = `upload-status ${type}`;
                uploadStatus.style.display = 'block';

                setTimeout(() => {
                    uploadStatus.style.display = 'none';
                }, 3000);
            }

            // 加载当前二维码
            function loadCurrentQR() {
                fetch('/api/system/qr-codes', {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        renderCurrentQR(data.data);
                    } else {
                        console.error('加载二维码失败:', data.message);
                        renderCurrentQR([]);
                    }
                })
                .catch(error => {
                    console.error('加载二维码失败:', error);
                    renderCurrentQR([]);
                });
            }

            // 渲染当前二维码
            function renderCurrentQR(qrCodes) {
                if (qrCodes.length === 0) {
                    currentQrDisplay.innerHTML = `
                        <div class="no-qr-message">
                            <i class="fas fa-qrcode" style="font-size: 48px; color: #ddd; margin-bottom: 15px;"></i>
                            <p>暂无群二维码，请上传一个二维码图片</p>
                        </div>
                    `;
                    return;
                }

                // 显示最新的二维码（第一个）
                const currentQR = qrCodes[0];
                currentQrDisplay.innerHTML = `
                    <img src="/${currentQR.file_path}" alt="群二维码" class="current-qr-image" onerror="this.src='/static/img/placeholder.png'">
                    <div class="current-qr-info">
                        <p><strong>文件名:</strong> ${currentQR.filename}</p>
                        <p><strong>大小:</strong> ${formatFileSize(currentQR.file_size)}</p>
                        <p><strong>上传时间:</strong> ${formatDateTime(currentQR.upload_time)}</p>
                    </div>
                    <div class="qr-actions-current">
                        <button class="btn-replace" onclick="replaceQRCode()">
                            <i class="fas fa-sync-alt"></i> 更换二维码
                        </button>
                        <button class="btn-delete" onclick="deleteCurrentQRCode('${currentQR.filename}')">
                            <i class="fas fa-trash"></i> 删除二维码
                        </button>
                    </div>
                `;
            }

            // 更换二维码（触发文件选择）
            window.replaceQRCode = function() {
                fileInput.click();
            };

            // 删除当前二维码
            window.deleteCurrentQRCode = function(filename) {
                if (!confirm('确定要删除当前的群二维码吗？删除后将无法恢复。')) {
                    return;
                }

                fetch(`/api/system/qr-codes/${filename}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        showUploadStatus('二维码删除成功', 'success');
                        loadCurrentQR();
                    } else {
                        showUploadStatus(data.message || '删除失败', 'error');
                    }
                })
                .catch(error => {
                    showUploadStatus('删除失败：' + error.message, 'error');
                });
            };

            // 格式化文件大小
            function formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            // 格式化日期时间
            function formatDateTime(dateString) {
                const date = new Date(dateString);
                return date.toLocaleString('zh-CN');
            }
        });
    </script>
</body>
</html>
