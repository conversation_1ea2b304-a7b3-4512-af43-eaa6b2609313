<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI招聘客服管理系统 - QA引擎</title>
    <link rel="stylesheet" href="/static/css/bootstrap.min.css">
    <link rel="stylesheet" href="/static/css/all.min.css">
    <link rel="stylesheet" href="/static/css/main.css">
</head>
<body>
    <div class="app-wrapper">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>AI招聘客服管理系统</h2>
            </div>
            <div class="sidebar-menu">
                <div class="sidebar-menu-item" data-link="index">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>仪表盘</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-briefcase"></i>
                    <span>职位管理</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="job_classification">
                        <i class="fas fa-tags"></i>
                        <span>职位分类</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="batch_management">
                        <i class="fas fa-layer-group"></i>
                        <span>批量管理</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="positions">
                        <i class="fas fa-list"></i>
                        <span>职位列表</span>
                    </div>
                </div>
                <div class="sidebar-menu-item" data-link="candidates">
                    <i class="fas fa-user-tie"></i>
                    <span>候选人管理</span>
                </div>
                <div class="sidebar-menu-item" data-link="virtual_hr">
                    <i class="fas fa-robot"></i>
                    <span>虚拟HR</span>
                </div>
                <div class="sidebar-menu-item" data-link="scenes">
                    <i class="fas fa-tasks"></i>
                    <span>面试场景</span>
                </div>
                <div class="sidebar-menu-item" data-link="interview">
                    <i class="fas fa-comments"></i>
                    <span>面试互动（仅供测试）</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-star"></i>
                    <span>胜任力模型</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="triggers">
                        <i class="fas fa-bolt"></i>
                        <span>触发器</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="classifier">
                        <i class="fas fa-filter"></i>
                        <span>分类器</span>
                    </div>
                </div>
                <div class="sidebar-menu-item" data-link="ai_brain">
                    <i class="fas fa-brain"></i>
                    <span>AI大脑</span>
                </div>
                <div class="sidebar-menu-item has-submenu open">
                    <i class="fas fa-chart-line"></i>
                    <span>AI数据透视</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu show">
                    <div class="sidebar-menu-item" data-link="scheduled_analysis">
                        <i class="fas fa-clock"></i>
                        <span>客情分析</span>
                    </div>
                    <div class="sidebar-menu-item active" data-link="qa_engine">
                        <i class="fas fa-question-circle"></i>
                        <span>QA引擎</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-key"></i>
                    <span>APIKey管理</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="apikey_create">
                        <i class="fas fa-plus"></i>
                        <span>创建APIKey</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="apikey_usage">
                        <i class="fas fa-chart-bar"></i>
                        <span>用量统计</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-cog"></i>
                    <span>用户设置</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="channel_management">
                        <i class="fas fa-plug"></i>
                        <span>渠道接入管理</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="global_settings">
                        <i class="fas fa-sliders-h"></i>
                        <span>全局配置</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-cogs"></i>
                    <span>系统设置</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item admin-only" data-link="user_management07082">
                        <i class="fas fa-user-cog"></i>
                        <span>用户管理</span>
                    </div>
                    <div class="sidebar-menu-item admin-only" data-link="system_config">
                        <i class="fas fa-wrench"></i>
                        <span>系统配置</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部导航栏 -->
            <div class="topbar">
                <div class="topbar-toggle">
                    <i class="fas fa-bars"></i>
                </div>
                <div class="topbar-title">
                    QA引擎
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content">
                <div class="qa-engine-container">
                    <!-- 左侧：岗位分类选择 -->
                    <div class="left-panel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title">
                                    <i class="fas fa-tags"></i>
                                    岗位分类
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="classification-list" id="classificationList">
                                    <!-- 岗位分类列表将在这里动态加载 -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧：QA管理区域 -->
                    <div class="right-panel">
                        <!-- 默认提示 -->
                        <div id="defaultPrompt" class="default-prompt">
                            <div class="prompt-content">
                                <i class="fas fa-arrow-left fa-2x text-muted mb-3"></i>
                                <h4>请选择岗位分类</h4>
                                <p class="text-muted">选择左侧的岗位分类来查看和管理对应的QA数据</p>
                            </div>
                        </div>

                        <!-- QA管理区域 -->
                        <div id="qaManagementArea" style="display: none;">
                            <!-- 操作按钮区 -->
                            <div class="card mb-4">
                                <div class="card-body">
                                    <div class="row align-items-center">
                                        <div class="col-md-6">
                                            <h5 class="mb-0">
                                                <i class="fas fa-question-circle"></i>
                                                <span id="currentClassificationName">QA管理</span>
                                            </h5>
                                        </div>
                                        <div class="col-md-6 text-right">
                                            <button id="addQABtn" class="btn btn-success btn-sm">
                                                <i class="fas fa-plus"></i>
                                                添加QA
                                            </button>
                                            <button id="historyBtn" class="btn btn-secondary btn-sm ml-2">
                                                <i class="fas fa-history"></i>
                                                历史拆分
                                            </button>
                                            <button id="aiSplitBtn" class="btn btn-info btn-sm ml-2">
                                                <i class="fas fa-robot"></i>
                                                AI拆分
                                            </button>
                                        </div>
                                    </div>
                                    <div class="row mt-2">
                                        <div class="col-12">
                                            <span id="qaCount" class="text-muted small">共 0 个QA</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- QA表格 -->
                            <div class="card">
                                <div class="card-body p-0">
                                    <div class="table-responsive">
                                        <table id="qaTable" class="table table-striped table-hover mb-0">
                                            <thead>
                                                <tr>
                                                    <th width="25%">问题 (Q)</th>
                                                    <th width="35%">答案 (A)</th>
                                                    <th width="10%">频次</th>
                                                    <th width="10%">来源</th>
                                                    <th width="15%">添加时间</th>
                                                    <th width="5%">操作</th>
                                                </tr>
                                            </thead>
                                            <tbody id="qaTableBody">
                                                <!-- QA数据将在这里动态加载 -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加QA模态框 -->
    <div class="modal fade" id="addQAModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加QA</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addQAForm">
                        <div class="form-group">
                            <label for="questionInput">问题 (Q)</label>
                            <textarea id="questionInput" class="form-control" rows="3" placeholder="请输入问题..." required></textarea>
                        </div>
                        <div class="form-group">
                            <label for="answerInput">答案 (A)</label>
                            <textarea id="answerInput" class="form-control" rows="5" placeholder="请输入答案..." required></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" id="saveQABtn" class="btn btn-primary">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- AI拆分QA模态框 -->
    <div class="modal fade" id="aiSplitModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">AI自动拆分QA</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="aiSplitForm">
                        <div class="form-group">
                            <label for="knowledgeContent">知识库内容</label>
                            <textarea id="knowledgeContent" class="form-control" rows="10"
                                placeholder="请粘贴您的知识库内容，AI将自动拆分为QA对..." required></textarea>
                            <small class="form-text text-muted">
                                提示：请输入完整的知识库文本，AI会自动识别并拆分为多个问答对。
                            </small>
                        </div>
                        <div id="aiSplitProgress" style="display: none;">
                            <div class="progress mb-3">
                                <div class="progress-bar progress-bar-striped progress-bar-animated"
                                     role="progressbar" style="width: 100%">
                                    AI正在处理中...
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" id="startAISplitBtn" class="btn btn-info">
                        <i class="fas fa-robot"></i>
                        开始AI拆分
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 历史拆分记录模态框 -->
    <div class="modal fade" id="historyModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-history"></i>
                        历史AI拆分记录
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="historyContent">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="sr-only">加载中...</span>
                            </div>
                            <p class="mt-2">正在加载历史记录...</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <style>
        /* 全局动画和现代化样式 */
        * {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* QA引擎容器布局 */
        .qa-engine-container {
            display: flex;
            gap: 24px;
            height: calc(100vh - 200px);
            min-height: 600px;
            padding: 0;
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .left-panel {
            width: 320px;
            flex-shrink: 0;
            animation: slideInLeft 0.8s ease-out;
        }

        .right-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            animation: slideInRight 0.8s ease-out;
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* 岗位分类列表样式 */
        .classification-list {
            max-height: calc(100vh - 300px);
            overflow-y: auto;
            padding: 8px;
        }

        .classification-list::-webkit-scrollbar {
            width: 6px;
        }

        .classification-list::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .classification-list::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 3px;
        }

        .classification-item {
            padding: 16px 20px;
            border: none;
            border-radius: 12px;
            margin-bottom: 12px;
            cursor: pointer;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            position: relative;
            overflow: hidden;
            transform: translateY(0);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .classification-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 0;
        }

        .classification-item > * {
            position: relative;
            z-index: 1;
        }

        .classification-item:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.25);
        }

        .classification-item:hover::before {
            opacity: 0.1;
        }

        .classification-item.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .classification-item.active::before {
            opacity: 0;
        }

        .classification-item.active:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 12px 30px rgba(102, 126, 234, 0.5);
        }

        .classification-name {
            font-weight: 700;
            font-size: 15px;
            margin-bottom: 6px;
            letter-spacing: 0.3px;
        }

        .classification-desc {
            font-size: 13px;
            color: #64748b;
            line-height: 1.5;
            opacity: 0.9;
        }

        .classification-item.active .classification-desc {
            color: rgba(255, 255, 255, 0.85);
        }

        /* 默认提示样式 */
        .default-prompt {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            min-height: 400px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 16px;
            position: relative;
            overflow: hidden;
        }

        .default-prompt::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23000" opacity="0.02"/><circle cx="75" cy="75" r="1" fill="%23000" opacity="0.02"/><circle cx="50" cy="10" r="1" fill="%23000" opacity="0.02"/><circle cx="10" cy="50" r="1" fill="%23000" opacity="0.02"/><circle cx="90" cy="30" r="1" fill="%23000" opacity="0.02"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .prompt-content {
            text-align: center;
            padding: 60px 40px;
            position: relative;
            z-index: 1;
            animation: pulse 2s ease-in-out infinite;
        }

        .prompt-content i {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 20px;
            animation: float 3s ease-in-out infinite;
        }

        .prompt-content h4 {
            color: #1e293b;
            font-weight: 700;
            margin-bottom: 12px;
            letter-spacing: 0.5px;
        }

        .prompt-content p {
            color: #64748b;
            font-size: 16px;
            line-height: 1.6;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        /* 现代化卡片样式 */
        .card {
            border: none;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .card:hover::before {
            opacity: 1;
        }

        .card-header {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-bottom: 1px solid rgba(226, 232, 240, 0.6);
            padding: 20px 24px;
            position: relative;
        }

        .card-title {
            margin: 0;
            font-size: 18px;
            font-weight: 700;
            color: #1e293b;
            letter-spacing: 0.3px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card-title i {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 20px;
        }

        .card-body {
            padding: 24px;
        }

        /* 现代化表格样式 */
        .table {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
        }

        .table th {
            border-top: none;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: 700;
            font-size: 14px;
            padding: 18px 16px;
            letter-spacing: 0.3px;
            text-transform: uppercase;
            position: relative;
        }

        .table th::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: rgba(255, 255, 255, 0.2);
        }

        .table td {
            font-size: 14px;
            vertical-align: middle;
            padding: 16px;
            border-bottom: 1px solid #f1f5f9;
            background: white;
            transition: all 0.3s ease;
        }

        .table tbody tr {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .table tbody tr:hover {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            transform: scale(1.01);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }

        .editable-cell {
            cursor: pointer;
            position: relative;
            max-width: 200px;
            word-wrap: break-word;
            border-radius: 8px;
            padding: 12px;
            transition: all 0.3s ease;
        }

        .editable-cell:hover {
            background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
            transform: scale(1.02);
        }

        .editable-cell::before {
            content: '✏️';
            position: absolute;
            top: 4px;
            right: 4px;
            font-size: 10px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .editable-cell:hover::before {
            opacity: 0.6;
        }

        .editable-input {
            width: 100%;
            border: 2px solid #667eea;
            background: white;
            resize: none;
            min-height: 60px;
            font-size: 14px;
            border-radius: 8px;
            padding: 12px;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
            transition: all 0.3s ease;
        }

        .editable-input:focus {
            outline: none;
            border-color: #764ba2;
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.25);
            transform: scale(1.02);
        }

        /* 现代化徽章样式 */
        .badge {
            font-size: 12px;
            padding: 6px 12px;
            border-radius: 20px;
            font-weight: 600;
            letter-spacing: 0.3px;
            text-transform: uppercase;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .badge:hover {
            transform: scale(1.1);
        }

        .badge.bg-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        }

        .badge.bg-info {
            background: linear-gradient(135deg, #36d1dc 0%, #5b86e5 100%) !important;
        }

        .frequency-badge {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
            padding: 8px 14px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 700;
            box-shadow: 0 3px 12px rgba(17, 153, 142, 0.3);
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .frequency-badge::before {
            content: '🔥';
            font-size: 10px;
        }

        .frequency-badge:hover {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 5px 20px rgba(17, 153, 142, 0.4);
        }

        /* 现代化按钮样式 */
        .btn {
            border-radius: 12px;
            font-weight: 600;
            letter-spacing: 0.3px;
            padding: 12px 24px;
            border: none;
            position: relative;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            text-transform: uppercase;
            font-size: 13px;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-success {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            box-shadow: 0 4px 15px rgba(17, 153, 142, 0.3);
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(17, 153, 142, 0.4);
        }

        .btn-info {
            background: linear-gradient(135deg, #36d1dc 0%, #5b86e5 100%);
            box-shadow: 0 4px 15px rgba(54, 209, 220, 0.3);
        }

        .btn-info:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(54, 209, 220, 0.4);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .btn-danger:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
        }

        .btn-sm {
            padding: 8px 16px;
            font-size: 11px;
            border-radius: 8px;
        }

        /* 工具类 */
        .mb-4 {
            margin-bottom: 1.5rem;
        }

        .ml-2, .ms-2 {
            margin-left: 0.75rem;
        }

        /* 加载动画 */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* 成功/错误提示动画 */
        .alert {
            border-radius: 12px;
            border: none;
            padding: 16px 20px;
            font-weight: 600;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            animation: slideInDown 0.5s ease-out;
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .alert-success {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
        }

        .alert-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
        }

        .alert-warning {
            background: linear-gradient(135deg, #ffa726 0%, #fb8c00 100%);
            color: white;
        }

        /* 模态框美化 */
        .modal-content {
            border-radius: 16px;
            border: none;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }

        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-bottom: none;
            padding: 20px 24px;
        }

        .modal-title {
            font-weight: 700;
            letter-spacing: 0.3px;
        }

        .modal-body {
            padding: 24px;
            background: #f8fafc;
        }

        .modal-footer {
            background: white;
            border-top: 1px solid #e2e8f0;
            padding: 20px 24px;
        }

        .form-control {
            border-radius: 12px;
            border: 2px solid #e2e8f0;
            padding: 12px 16px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: scale(1.02);
        }

        /* 进度条美化 */
        .progress {
            height: 8px;
            border-radius: 4px;
            background: #e2e8f0;
            overflow: hidden;
        }

        .progress-bar {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            border-radius: 4px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .qa-engine-container {
                flex-direction: column;
                height: auto;
                gap: 16px;
            }

            .left-panel {
                width: 100%;
                margin-bottom: 16px;
                animation: slideInDown 0.6s ease-out;
            }

            .right-panel {
                animation: slideInUp 0.6s ease-out;
            }

            .classification-list {
                max-height: 200px;
            }

            .classification-item {
                padding: 12px 16px;
                margin-bottom: 8px;
            }

            .card-body {
                padding: 16px;
            }

            .table th, .table td {
                padding: 12px 8px;
                font-size: 12px;
            }

            .btn {
                padding: 10px 16px;
                font-size: 12px;
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        @keyframes bounceIn {
            0% {
                opacity: 0;
                transform: scale(0.3);
            }
            50% {
                opacity: 1;
                transform: scale(1.05);
            }
            70% {
                transform: scale(0.9);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        /* 特殊效果 */
        .qa-engine-container::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent 30%, rgba(102, 126, 234, 0.02) 50%, transparent 70%);
            pointer-events: none;
            z-index: -1;
            animation: shimmer 3s ease-in-out infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* 深色模式支持 */
        @media (prefers-color-scheme: dark) {
            .card {
                background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
                color: #e2e8f0;
            }

            .card-header {
                background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            }

            .table td {
                background: #1e293b;
                color: #e2e8f0;
                border-bottom-color: #334155;
            }

            .default-prompt {
                background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            }

            .prompt-content h4 {
                color: #e2e8f0;
            }
        }

        /* 历史拆分记录样式 */
        .history-list {
            max-height: 600px;
            overflow-y: auto;
            padding-right: 8px;
        }

        .history-list::-webkit-scrollbar {
            width: 6px;
        }

        .history-list::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .history-list::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 3px;
        }

        .history-item {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .history-item .card-header {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-bottom: 1px solid rgba(226, 232, 240, 0.6);
        }

        .history-item .card-body {
            background: white;
        }

        .content-preview {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 10px;
        }

        .full-content {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 12px;
            margin-top: 10px;
        }

        .badge-success {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%) !important;
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            border: none;
            color: white;
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(108, 117, 125, 0.4);
        }
    </style>

    <!-- 脚本引用 -->
    <script src="/static/js/jquery-3.6.0.min.js"></script>
    <script src="/static/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/main.js"></script>
    <script src="/static/js/auth07082.js"></script>
    <script src="/static/js/auth-check.js"></script>
    <script src="/static/js/user-logout.js"></script>

    <script>
        let currentJobClassificationId = '';
        let qaData = [];
        let jobClassifications = [];

        // 页面加载完成后初始化
        $(document).ready(function() {
            loadJobClassifications();
            initEventHandlers();
        });

        // 获取认证头
        function getAuthHeaders() {
            const token = localStorage.getItem('accessToken');
            return {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            };
        }

        // 加载岗位分类列表
        function loadJobClassifications() {
            $.ajax({
                url: '/api/job_classifications',
                type: 'GET',
                headers: getAuthHeaders(),
                success: function(response) {
                    if (response.code === 200) {
                        jobClassifications = response.data;
                        renderJobClassificationsList();
                    }
                },
                error: function() {
                    showAlert('加载岗位分类失败', 'error');
                }
            });
        }

        // 渲染岗位分类列表
        function renderJobClassificationsList() {
            const container = $('#classificationList');
            container.empty();

            if (jobClassifications.length === 0) {
                container.html(`
                    <div class="text-center text-muted py-4" style="animation: fadeIn 0.5s ease-out;">
                        <i class="fas fa-tags fa-2x mb-2" style="color: #667eea;"></i>
                        <p>暂无岗位分类</p>
                    </div>
                `);
                return;
            }

            jobClassifications.forEach(function(classification, index) {
                const item = $(`
                    <div class="classification-item" data-id="${classification.id}" style="animation-delay: ${index * 0.1}s; opacity: 0;">
                        <div class="classification-name">${escapeHtml(classification.class_name)}</div>
                        <div class="classification-desc">${escapeHtml(classification.class_description || '暂无描述')}</div>
                        <div class="classification-tenant">${escapeHtml(classification.tenant_display || '未知租户')}</div>
                    </div>
                `);

                container.append(item);

                // 添加进入动画
                setTimeout(() => {
                    item.css({
                        'animation': 'slideInLeft 0.6s ease-out forwards',
                        'opacity': '1'
                    });
                }, index * 100);
            });
        }

        // 初始化事件处理器
        function initEventHandlers() {
            // 岗位分类点击事件
            $(document).on('click', '.classification-item', function() {
                const classificationId = $(this).data('id');
                selectJobClassification(classificationId);
            });

            // 添加QA按钮
            $('#addQABtn').click(function() {
                const modal = new bootstrap.Modal(document.getElementById('addQAModal'));
                modal.show();
            });

            // AI拆分按钮
            $('#aiSplitBtn').click(function() {
                const modal = new bootstrap.Modal(document.getElementById('aiSplitModal'));
                modal.show();
            });

            // 历史拆分按钮
            $('#historyBtn').click(function() {
                if (!currentJobClassificationId) {
                    showAlert('请先选择岗位分类', 'warning');
                    return;
                }
                loadHistoryData();
                const modal = new bootstrap.Modal(document.getElementById('historyModal'));
                modal.show();
            });

            // 保存QA
            $('#saveQABtn').click(function() {
                saveQA();
            });

            // 开始AI拆分
            $('#startAISplitBtn').click(function() {
                startAISplit();
            });

            // 表格双击编辑
            $(document).on('dblclick', '.editable-cell', function() {
                makeEditable($(this));
            });
        }

        // 选择岗位分类
        function selectJobClassification(classificationId) {
            // 添加点击动画效果
            const clickedItem = $(`.classification-item[data-id="${classificationId}"]`);
            clickedItem.css('transform', 'scale(0.95)');

            setTimeout(() => {
                // 更新选中状态
                $('.classification-item').removeClass('active');
                clickedItem.addClass('active');
                clickedItem.css('transform', '');

                // 设置当前分类ID
                currentJobClassificationId = classificationId;

                // 获取分类信息
                const classification = jobClassifications.find(c => c.id === classificationId);
                if (classification) {
                    const nameElement = $('#currentClassificationName');
                    nameElement.fadeOut(200, function() {
                        $(this).text(classification.class_name + ' - QA管理').fadeIn(200);
                    });
                }

                // 显示加载状态
                showLoadingState();

                // 加载QA数据
                loadQAData();
            }, 150);
        }

        // 显示加载状态
        function showLoadingState() {
            $('#defaultPrompt').hide();
            $('#qaManagementArea').show();

            const tbody = $('#qaTableBody');
            tbody.html(`
                <tr>
                    <td colspan="6" class="text-center py-5">
                        <div class="loading-spinner"></div>
                        <p class="mt-3 text-muted">正在加载QA数据...</p>
                    </td>
                </tr>
            `);
        }

        // 加载QA数据
        function loadQAData() {
            if (!currentJobClassificationId) return;

            $.ajax({
                url: `/api/qa-engine/${currentJobClassificationId}`,
                type: 'GET',
                headers: getAuthHeaders(),
                success: function(response) {
                    if (response.code === 200) {
                        qaData = response.data;
                        renderQATable();
                        $('#defaultPrompt').hide();
                        $('#qaManagementArea').show();
                        updateQACount();
                    } else {
                        showAlert('加载QA数据失败: ' + response.message, 'error');
                    }
                },
                error: function() {
                    showAlert('加载QA数据失败', 'error');
                }
            });
        }

        // 渲染QA表格
        function renderQATable() {
            const tbody = $('#qaTableBody');
            tbody.empty();

            if (qaData.length === 0) {
                tbody.html(`
                    <tr>
                        <td colspan="6" class="text-center py-5">
                            <div style="animation: fadeIn 0.5s ease-out;">
                                <i class="fas fa-question-circle fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">暂无QA数据</h5>
                                <p class="text-muted">点击"添加QA"或"AI拆分"来创建第一个问答对</p>
                            </div>
                        </td>
                    </tr>
                `);
                return;
            }

            qaData.forEach(function(qa, index) {
                const row = $(`
                    <tr data-qa-id="${qa.id}" style="opacity: 0; transform: translateY(20px);">
                        <td class="editable-cell" data-field="question">${escapeHtml(qa.question)}</td>
                        <td class="editable-cell" data-field="answer">${escapeHtml(qa.answer)}</td>
                        <td><span class="frequency-badge">${qa.frequency}</span></td>
                        <td><span class="badge bg-${getSourceBadgeClass(qa.source)}">${getSourceText(qa.source)}</span></td>
                        <td>${formatDateTime(qa.createdAt)}</td>
                        <td>
                            <button class="btn btn-danger btn-sm" onclick="deleteQA('${qa.id}')" title="删除QA">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `);

                tbody.append(row);

                // 添加进入动画
                setTimeout(() => {
                    row.css({
                        'opacity': '1',
                        'transform': 'translateY(0)',
                        'transition': 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)'
                    });
                }, index * 50);
            });
        }

        // 更新QA数量显示
        function updateQACount() {
            $('#qaCount').text(`共 ${qaData.length} 个QA`);
        }

        // 保存QA
        function saveQA() {
            const question = $('#questionInput').val().trim();
            const answer = $('#answerInput').val().trim();

            if (!question || !answer) {
                showAlert('请填写完整的问题和答案', 'warning');
                return;
            }

            const data = {
                job_classification_id: currentJobClassificationId,
                question: question,
                answer: answer,
                source: 'manual'
            };

            $.ajax({
                url: '/api/qa-engine',
                type: 'POST',
                headers: getAuthHeaders(),
                data: JSON.stringify(data),
                success: function(response) {
                    if (response.code === 200) {
                        showAlert('添加QA成功', 'success');
                        const modal = bootstrap.Modal.getInstance(document.getElementById('addQAModal'));
                        modal.hide();
                        $('#addQAForm')[0].reset();
                        loadQAData(); // 重新加载数据
                    } else {
                        showAlert('添加QA失败: ' + response.message, 'error');
                    }
                },
                error: function() {
                    showAlert('添加QA失败', 'error');
                }
            });
        }

        // 开始AI拆分
        function startAISplit() {
            const content = $('#knowledgeContent').val().trim();

            if (!content) {
                showAlert('请输入知识库内容', 'warning');
                return;
            }

            const data = {
                job_classification_id: currentJobClassificationId,
                content: content
            };

            // 显示进度条
            $('#aiSplitProgress').show();
            $('#startAISplitBtn').prop('disabled', true);

            $.ajax({
                url: '/api/qa-engine/ai-split',
                type: 'POST',
                headers: getAuthHeaders(),
                data: JSON.stringify(data),
                success: function(response) {
                    if (response.code === 200) {
                        const qaCount = response.data.created_qa_count;
                        showAlert(`AI拆分成功，生成了 ${qaCount} 个QA对`, 'success');
                        const modal = bootstrap.Modal.getInstance(document.getElementById('aiSplitModal'));
                        modal.hide();
                        $('#aiSplitForm')[0].reset();
                        loadQAData(); // 重新加载数据
                    } else {
                        showAlert('AI拆分失败: ' + response.message, 'error');
                    }
                },
                error: function() {
                    showAlert('AI拆分失败', 'error');
                },
                complete: function() {
                    $('#aiSplitProgress').hide();
                    $('#startAISplitBtn').prop('disabled', false);
                }
            });
        }

        // 加载历史拆分数据
        function loadHistoryData() {
            // 显示加载状态
            $('#historyContent').html(`
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">加载中...</span>
                    </div>
                    <p class="mt-2">正在加载历史记录...</p>
                </div>
            `);

            $.ajax({
                url: `/api/knowledge-base/${currentJobClassificationId}`,
                type: 'GET',
                headers: getAuthHeaders(),
                success: function(response) {
                    if (response.code === 200) {
                        renderHistoryData(response.data);
                    } else {
                        $('#historyContent').html(`
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                获取历史记录失败: ${response.message}
                            </div>
                        `);
                    }
                },
                error: function() {
                    $('#historyContent').html(`
                        <div class="alert alert-danger">
                            <i class="fas fa-times-circle"></i>
                            获取历史记录失败，请稍后重试
                        </div>
                    `);
                }
            });
        }

        // 渲染历史拆分数据
        function renderHistoryData(historyList) {
            if (historyList.length === 0) {
                $('#historyContent').html(`
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-history fa-3x mb-3"></i>
                        <h5>暂无历史拆分记录</h5>
                        <p>该岗位分类还没有进行过AI拆分操作</p>
                    </div>
                `);
                return;
            }

            let html = '<div class="history-list">';
            historyList.forEach(function(item, index) {
                const createdAt = new Date(item.createdAt).toLocaleString('zh-CN');
                const contentPreview = item.content.length > 100 ?
                    item.content.substring(0, 100) + '...' : item.content;

                html += `
                    <div class="history-item card mb-3" style="animation-delay: ${index * 0.1}s;">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <i class="fas fa-clock text-muted"></i>
                                拆分时间: ${createdAt}
                            </h6>
                            <span class="badge badge-success">
                                生成 ${item.processed_qa_count} 个QA
                            </span>
                        </div>
                        <div class="card-body">
                            <h6 class="card-title">原文内容:</h6>
                            <div class="content-preview">
                                <p class="text-muted">${escapeHtml(contentPreview)}</p>
                                ${item.content.length > 100 ?
                                    `<button class="btn btn-link btn-sm p-0" onclick="toggleFullContent('${item.id}')">
                                        <span id="toggle-text-${item.id}">展开全文</span>
                                    </button>` : ''
                                }
                            </div>
                            <div class="full-content" id="full-content-${item.id}" style="display: none;">
                                <p>${escapeHtml(item.content)}</p>
                            </div>
                        </div>
                    </div>
                `;
            });
            html += '</div>';

            $('#historyContent').html(html);

            // 添加进入动画
            $('.history-item').each(function(index) {
                const item = $(this);
                setTimeout(() => {
                    item.css({
                        'animation': 'fadeInUp 0.6s ease-out forwards',
                        'opacity': '1'
                    });
                }, index * 100);
            });
        }

        // 切换全文显示
        function toggleFullContent(itemId) {
            const fullContent = $(`#full-content-${itemId}`);
            const toggleText = $(`#toggle-text-${itemId}`);
            const preview = fullContent.siblings('.content-preview');

            if (fullContent.is(':visible')) {
                fullContent.slideUp();
                preview.show();
                toggleText.text('展开全文');
            } else {
                fullContent.slideDown();
                preview.hide();
                toggleText.text('收起');
            }
        }

        // 删除QA
        function deleteQA(qaId) {
            if (!confirm('确定要删除这个QA吗？')) {
                return;
            }

            $.ajax({
                url: `/api/qa-engine/${qaId}`,
                type: 'DELETE',
                headers: getAuthHeaders(),
                success: function(response) {
                    if (response.code === 200) {
                        showAlert('删除成功', 'success');
                        loadQAData(); // 重新加载数据
                    } else {
                        showAlert('删除失败: ' + response.message, 'error');
                    }
                },
                error: function() {
                    showAlert('删除失败', 'error');
                }
            });
        }

        // 使单元格可编辑
        function makeEditable(cell) {
            const currentText = cell.text();
            const field = cell.data('field');
            const qaId = cell.closest('tr').data('qa-id');

            const textarea = $(`<textarea class="editable-input">${escapeHtml(currentText)}</textarea>`);
            cell.html(textarea);
            textarea.focus();

            // 失去焦点时保存
            textarea.blur(function() {
                const newText = $(this).val().trim();
                if (newText !== currentText && newText !== '') {
                    updateQA(qaId, field, newText, cell);
                } else {
                    cell.text(currentText);
                }
            });

            // 按Esc取消编辑
            textarea.keydown(function(e) {
                if (e.key === 'Escape') {
                    cell.text(currentText);
                }
            });
        }

        // 更新QA
        function updateQA(qaId, field, value, cell) {
            const data = {};
            data[field] = value;

            $.ajax({
                url: `/api/qa-engine/${qaId}`,
                type: 'PUT',
                headers: getAuthHeaders(),
                data: JSON.stringify(data),
                success: function(response) {
                    if (response.code === 200) {
                        cell.text(value);
                        showAlert('更新成功', 'success');
                        // 更新本地数据
                        const qa = qaData.find(q => q.id === qaId);
                        if (qa) {
                            qa[field] = value;
                        }
                    } else {
                        cell.text(cell.data('original-text') || '');
                        showAlert('更新失败: ' + response.message, 'error');
                    }
                },
                error: function() {
                    cell.text(cell.data('original-text') || '');
                    showAlert('更新失败', 'error');
                }
            });
        }

        // 工具函数
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function getSourceBadgeClass(source) {
            switch (source) {
                case 'manual': return 'primary';
                case 'ai_split': return 'info';
                default: return 'secondary';
            }
        }

        function getSourceText(source) {
            switch (source) {
                case 'manual': return '手动';
                case 'ai_split': return 'AI拆分';
                default: return '未知';
            }
        }

        function formatDateTime(dateString) {
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN');
        }

        function showAlert(message, type) {
            const alertClass = type === 'success' ? 'alert-success' :
                              type === 'warning' ? 'alert-warning' : 'alert-danger';

            const icon = type === 'success' ? 'fas fa-check-circle' :
                        type === 'warning' ? 'fas fa-exclamation-triangle' : 'fas fa-times-circle';

            const alert = $(`
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px; animation: slideInRight 0.5s ease-out;">
                    <i class="${icon} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `);

            $('body').append(alert);

            // 添加进入动画
            alert.css({
                'transform': 'translateX(100%)',
                'opacity': '0'
            }).animate({
                'transform': 'translateX(0)',
                'opacity': '1'
            }, 500);

            // 4秒后自动消失
            setTimeout(function() {
                alert.animate({
                    'transform': 'translateX(100%)',
                    'opacity': '0'
                }, 300, function() {
                    alert.remove();
                });
            }, 4000);
        }
    </script>
</body>
</html>
