<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI招聘客服管理系统 - 职位分类</title>
    <link rel="stylesheet" href="/static/css/all.min.css">
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="stylesheet" href="/static/css/job_classification.css">
</head>
<body>
    <div class="app-wrapper">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>AI招聘客服管理系统</h2>
            </div>
            <div class="sidebar-menu">
                <div class="sidebar-menu-item" data-link="index">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>仪表盘</span>
                </div>
                <div class="sidebar-menu-item active has-submenu">
                    <i class="fas fa-briefcase"></i>
                    <span>职位管理</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu show">
                    <div class="sidebar-menu-item active" data-link="job_classification">
                        <i class="fas fa-tags"></i>
                        <span>职位分类</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="batch_management">
                        <i class="fas fa-layer-group"></i>
                        <span>批量管理</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="positions">
                        <i class="fas fa-list"></i>
                        <span>职位列表</span>
                    </div>
                </div>
                <div class="sidebar-menu-item" data-link="candidates">
                    <i class="fas fa-user-tie"></i>
                    <span>候选人管理</span>
                </div>
                <div class="sidebar-menu-item" data-link="virtual_hr">
                    <i class="fas fa-robot"></i>
                    <span>虚拟HR</span>
                </div>
                <div class="sidebar-menu-item" data-link="scenes">
                    <i class="fas fa-tasks"></i>
                    <span>面试场景</span>
                </div>
                <div class="sidebar-menu-item" data-link="interview">
                    <i class="fas fa-comments"></i>
                    <span>面试互动（仅供测试）</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-star"></i>
                    <span>胜任力模型</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="triggers">
                        <i class="fas fa-bolt"></i>
                        <span>触发器</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="classifier">
                        <i class="fas fa-filter"></i>
                        <span>分类器</span>
                    </div>
                </div>
                <div class="sidebar-menu-item" data-link="ai_brain">
                    <i class="fas fa-brain"></i>
                    <span>AI大脑</span>
                </div>
                <div class="sidebar-menu-item has-submenu open">
                    <i class="fas fa-chart-line"></i>
                    <span>AI数据透视</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu show">
                    <div class="sidebar-menu-item active" data-link="scheduled_analysis">
                        <i class="fas fa-clock"></i>
                        <span>客情分析</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="qa_engine">
                        <i class="fas fa-question-circle"></i>
                        <span>QA引擎</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-key"></i>
                    <span>APIKey管理</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="apikey_create">
                        <i class="fas fa-plus"></i>
                        <span>创建APIKey</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="apikey_usage">
                        <i class="fas fa-chart-bar"></i>
                        <span>用量统计</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-cog"></i>
                    <span>用户设置</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="channel_management">
                        <i class="fas fa-plug"></i>
                        <span>渠道接入管理</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="global_settings">
                        <i class="fas fa-sliders-h"></i>
                        <span>全局配置</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-cogs"></i>
                    <span>系统设置</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item admin-only" data-link="user_management07082">
                        <i class="fas fa-user-cog"></i>
                        <span>用户管理</span>
                    </div>
                    <div class="sidebar-menu-item admin-only" data-link="system_config">
                        <i class="fas fa-wrench"></i>
                        <span>系统配置</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部导航栏 -->
            <div class="topbar">
                <div class="topbar-toggle">
                    <i class="fas fa-bars"></i>
                </div>
                <div class="topbar-title">
                    职位分类
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content">
                <!-- 页面标题和操作 -->
                <div class="page-header">
                    <h2>职位分类列表</h2>
                    <button class="btn btn-primary" id="addClassificationBtn">
                        <i class="fas fa-plus"></i> 新增分类
                    </button>
                </div>

                <!-- 搜索和筛选 -->
                <div class="card mb-20">
                    <div class="card-body">
                        <div class="filter-container">
                            <div class="search-box">
                                <input type="text" id="searchInput" class="form-control" placeholder="搜索分类名称...">
                                <button class="btn btn-primary" id="searchBtn">
                                    <i class="fas fa-search"></i> 搜索
                                </button>
                            </div>
                            <div class="filter-options">
                                <div class="form-group">
                                    <label for="sortBy" class="form-label">排序方式:</label>
                                    <select id="sortBy" class="form-control">
                                        <option value="newest">最新添加</option>
                                        <option value="name">分类名称</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 职位分类列表 -->
                <div class="card">
                    <div class="card-body">
                        <div class="classification-list" id="classificationList">
                            <!-- 职位分类列表将通过JS动态生成 -->
                            <div class="classification-list-loading">
                                <p>加载中...</p>
                            </div>
                        </div>
                        <!-- 分页 -->
                        <div class="pagination-container" id="pagination">
                            <!-- 分页将通过JS动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增/编辑职位分类模态框 -->
    <div class="modal" id="classificationModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">新增职位分类</h3>
                <span class="modal-close">&times;</span>
            </div>
            <div class="modal-body">
                <form id="classificationForm" data-validate="true">
                    <input type="hidden" id="classificationId" name="id">
                    
                    <div class="form-group">
                        <label for="className" class="form-label">分类名称:</label>
                        <input type="text" id="className" name="class_name" class="form-control" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="classDescription" class="form-label">分类描述:</label>
                        <textarea id="classDescription" name="class_description" class="form-control" rows="3" style="resize: vertical;"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="virtualHrId" class="form-label">选择虚拟HR:</label>
                        <select id="virtualHrId" name="virtual_hr_id" class="form-control">
                            <option value="">请选择虚拟HR</option>
                            <!-- 虚拟HR选项将通过JS动态添加 -->
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-light" id="cancelBtn">取消</button>
                <button class="btn btn-primary" id="saveClassificationBtn">保存</button>
            </div>
        </div>
    </div>

    <!-- 职位分类详情模态框 -->
    <div class="modal" id="classificationDetailModal">
        <div class="modal-content modal-content-large">
            <div class="modal-header">
                <h3 id="detailModalTitle">职位分类详情</h3>
                <span class="modal-close">&times;</span>
            </div>
            <div class="modal-body">
                <div id="classificationDetails">
                    <!-- 基本信息 -->
                    <div class="detail-section">
                        <div class="detail-section-header" data-section="basic">
                            <i class="fas fa-info-circle"></i>
                            <span>基本信息</span>
                            <i class="fas fa-chevron-down toggle-icon"></i>
                        </div>
                        <div class="detail-section-content" id="basicInfoContent">
                            <!-- 基本信息内容将通过JS动态生成 -->
                        </div>
                    </div>

                    <!-- 职位列表 -->
                    <div class="detail-section">
                        <div class="detail-section-header" data-section="positions">
                            <i class="fas fa-briefcase"></i>
                            <span>职位列表</span>
                            <i class="fas fa-chevron-down toggle-icon"></i>
                        </div>
                        <div class="detail-section-content collapsed" id="positionsContent">
                            <!-- 职位列表内容将通过JS动态生成 -->
                        </div>
                    </div>

                    <!-- 场景详情 -->
                    <div class="detail-section">
                        <div class="detail-section-header" data-section="scenes">
                            <i class="fas fa-tasks"></i>
                            <span>面试场景</span>
                            <i class="fas fa-chevron-down toggle-icon"></i>
                        </div>
                        <div class="detail-section-content collapsed" id="scenesContent">
                            <!-- 场景详情内容将通过JS动态生成 -->
                        </div>
                    </div>

                    <!-- 用户信息 -->
                    <div class="detail-section">
                        <div class="detail-section-header" data-section="users">
                            <i class="fas fa-users"></i>
                            <span>候选人列表</span>
                            <i class="fas fa-chevron-down toggle-icon"></i>
                        </div>
                        <div class="detail-section-content collapsed" id="usersContent">
                            <!-- 用户信息内容将通过JS动态生成 -->
                        </div>
                    </div>

                    <!-- 虚拟HR信息 -->
                    <div class="detail-section">
                        <div class="detail-section-header" data-section="virtualhr">
                            <i class="fas fa-robot"></i>
                            <span>虚拟HR</span>
                            <i class="fas fa-chevron-down toggle-icon"></i>
                        </div>
                        <div class="detail-section-content collapsed" id="virtualhrContent">
                            <!-- 虚拟HR信息内容将通过JS动态生成 -->
                        </div>
                    </div>

                    <!-- 定时分析配置 -->
                    <div class="detail-section">
                        <div class="detail-section-header" data-section="analysis">
                            <i class="fas fa-chart-line"></i>
                            <span>定时分析配置</span>
                            <i class="fas fa-chevron-down toggle-icon"></i>
                        </div>
                        <div class="detail-section-content collapsed" id="analysisContent">
                            <!-- 定时分析配置内容将通过JS动态生成 -->
                        </div>
                    </div>

                    <!-- QA问题对 -->
                    <div class="detail-section">
                        <div class="detail-section-header" data-section="qa">
                            <i class="fas fa-question-circle"></i>
                            <span>QA问题对</span>
                            <i class="fas fa-chevron-down toggle-icon"></i>
                        </div>
                        <div class="detail-section-content collapsed" id="qaContent">
                            <!-- QA问题对内容将通过JS动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-light" id="closeDetailBtn">关闭</button>
                <button class="btn btn-primary" id="editClassificationBtn">编辑</button>
            </div>
        </div>
    </div>

    <!-- 脚本引用 -->
    <script src="/static/js/main.js"></script>
    <script src="/static/js/api.js"></script>
    <script src="/static/js/job_classification.js"></script>
    <script src="/static/js/sweetalert2@11"></script>
    <script src="/static/js/auth07082.js"></script>
    <script src="/static/js/auth-check.js"></script>
    <script src="/static/js/user-logout.js"></script>
</body>
</html> 