<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>X6库测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        #container {
            width: 800px;
            height: 600px;
            border: 1px solid #ddd;
            margin: 20px 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>X6库测试页面</h1>
    
    <div id="status" class="status">正在检测X6库...</div>
    
    <div id="container"></div>
    
    <button onclick="testBasicFeatures()">测试基本功能</button>
    
    <script src="https://unpkg.com/@antv/x6@2.18.1/dist/index.js"></script>
    <script>
        function updateStatus(message, isError = false) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = 'status ' + (isError ? 'error' : 'success');
        }
        
        function checkX6() {
            if (typeof X6 === 'undefined') {
                updateStatus('❌ X6库未加载', true);
                return false;
            }
            
            if (!X6.Graph) {
                updateStatus('❌ X6.Graph不存在', true);
                return false;
            }
            
            updateStatus('✅ X6库加载成功');
            return true;
        }
        
        function testBasicFeatures() {
            if (!checkX6()) return;
            
            try {
                // 创建图形实例
                const graph = new X6.Graph({
                    container: document.getElementById('container'),
                    width: 800,
                    height: 600,
                    background: {
                        color: '#f5f5f5',
                    },
                    grid: {
                        size: 20,
                        visible: true,
                    },
                });
                
                // 添加一个简单的节点
                const node = graph.addNode({
                    x: 100,
                    y: 100,
                    width: 100,
                    height: 60,
                    label: '测试节点',
                    attrs: {
                        body: {
                            fill: '#ffffff',
                            stroke: '#333333',
                        },
                        text: {
                            fill: '#333333',
                        },
                    },
                });
                
                // 添加另一个节点
                const node2 = graph.addNode({
                    x: 300,
                    y: 200,
                    width: 100,
                    height: 60,
                    label: '目标节点',
                    attrs: {
                        body: {
                            fill: '#ffffff',
                            stroke: '#333333',
                        },
                        text: {
                            fill: '#333333',
                        },
                    },
                });
                
                // 添加连线
                graph.addEdge({
                    source: node,
                    target: node2,
                    attrs: {
                        line: {
                            stroke: '#333333',
                            strokeWidth: 2,
                        },
                    },
                });
                
                updateStatus('✅ X6基本功能测试成功！图形已创建');
                
            } catch (error) {
                updateStatus('❌ X6功能测试失败: ' + error.message, true);
                console.error('X6测试错误:', error);
            }
        }
        
        // 页面加载完成后检查X6
        window.addEventListener('load', function() {
            setTimeout(checkX6, 100);
        });
    </script>
</body>
</html>
