<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI招聘客服管理系统 - 用户管理</title>
    <link rel="stylesheet" href="/static/css/all.min.css">
    <link rel="stylesheet" href="/static/css/main.css">
    <style>
        /* 用户管理页面特定样式 */
        .user-tabs {
            display: flex;
            margin-bottom: 20px;
            background: white;
            border-radius: 5px;
            box-shadow: var(--shadow);
            overflow: hidden;
        }
        
        .tab-btn {
            flex: 1;
            padding: 15px;
            text-align: center;
            font-weight: 600;
            cursor: pointer;
            border: none;
            background: none;
            transition: all 0.3s;
            border-bottom: 3px solid transparent;
        }
        
        .tab-btn.active {
            background-color: rgba(52, 152, 219, 0.1);
            border-bottom: 3px solid var(--primary-color);
            color: var(--primary-color);
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .action-cell {
            display: flex;
            gap: 8px;
        }
        
        .btn-edit, .btn-delete {
            padding: 4px 8px;
            font-size: 12px;
        }
        
        .user-form-container {
            background-color: rgba(0, 0, 0, 0.5);
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        
        .user-form {
            background: white;
            padding: 20px;
            border-radius: 5px;
            width: 100%;
            max-width: 500px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            position: relative;
        }
        
        .close-form {
            position: absolute;
            top: 15px;
            right: 15px;
            font-size: 18px;
            cursor: pointer;
        }
        
        .form-title {
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .form-footer {
            margin-top: 20px;
            padding-top: 10px;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
    </style>
</head>
<body>
    <div class="app-wrapper">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>AI招聘客服管理系统</h2>
            </div>
            <div class="sidebar-menu">
                <div class="sidebar-menu-item" data-link="index">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>仪表盘</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-briefcase"></i>
                    <span>职位管理</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="job_classification">
                        <i class="fas fa-tags"></i>
                        <span>职位分类</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="batch_management">
                        <i class="fas fa-layer-group"></i>
                        <span>批量管理</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="positions">
                        <i class="fas fa-list"></i>
                        <span>职位列表</span>
                    </div>
                </div>
                <div class="sidebar-menu-item" data-link="candidates">
                    <i class="fas fa-user-tie"></i>
                    <span>候选人管理</span>
                </div>
                <div class="sidebar-menu-item" data-link="virtual_hr">
                    <i class="fas fa-robot"></i>
                    <span>虚拟HR</span>
                </div>
                <div class="sidebar-menu-item" data-link="scenes">
                    <i class="fas fa-tasks"></i>
                    <span>面试场景</span>
                </div>
                <div class="sidebar-menu-item" data-link="interview">
                    <i class="fas fa-comments"></i>
                    <span>面试互动（仅供测试）</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-star"></i>
                    <span>胜任力模型</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="triggers">
                        <i class="fas fa-bolt"></i>
                        <span>触发器</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="classifier">
                        <i class="fas fa-filter"></i>
                        <span>分类器</span>
                    </div>
                </div>
                <div class="sidebar-menu-item" data-link="ai_brain">
                    <i class="fas fa-brain"></i>
                    <span>AI大脑</span>
                </div>
                <div class="sidebar-menu-item has-submenu open">
                    <i class="fas fa-chart-line"></i>
                    <span>AI数据透视</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu show">
                    <div class="sidebar-menu-item active" data-link="scheduled_analysis">
                        <i class="fas fa-clock"></i>
                        <span>客情分析</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="qa_engine">
                        <i class="fas fa-question-circle"></i>
                        <span>QA引擎</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-key"></i>
                    <span>APIKey管理</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="apikey_create">
                        <i class="fas fa-plus"></i>
                        <span>创建APIKey</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="apikey_usage">
                        <i class="fas fa-chart-bar"></i>
                        <span>用量统计</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-cog"></i>
                    <span>用户设置</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="channel_management">
                        <i class="fas fa-plug"></i>
                        <span>渠道接入管理</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="global_settings">
                        <i class="fas fa-sliders-h"></i>
                        <span>全局配置</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu open">
                    <i class="fas fa-cogs"></i>
                    <span>系统设置</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu show">
                    <div class="sidebar-menu-item admin-only active" data-link="user_management07082">
                        <i class="fas fa-user-cog"></i>
                        <span>用户管理</span>
                    </div>
                    <div class="sidebar-menu-item admin-only" data-link="system_config">
                        <i class="fas fa-wrench"></i>
                        <span>系统配置</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部导航栏 -->
            <div class="topbar">
                <div class="topbar-toggle">
                    <i class="fas fa-bars"></i>
                </div>
                <div class="topbar-title">
                    用户管理
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content">
                <!-- 选项卡 -->
                <div class="user-tabs">
                    <button class="tab-btn active" data-tab="admin-tab">管理员用户</button>
                    <button class="tab-btn" data-tab="tenant-tab">租户用户</button>
                </div>

                <!-- 管理员选项卡内容 -->
                <div id="admin-tab" class="tab-content active">
                    <div class="card">
                        <div class="card-header">
                            <h3>管理员用户列表</h3>
                            <button id="addAdminBtn" class="btn btn-primary">
                                <i class="fas fa-plus"></i> 添加管理员
                            </button>
                        </div>
                        <div class="card-body">
                            <table class="table" id="adminTable">
                                <thead>
                                    <tr>
                                        <th width="20%">账号</th>
                                        <th width="20%">昵称</th>
                                        <th width="30%">创建时间</th>
                                        <th width="30%">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 管理员数据将通过JS动态加载 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 租户选项卡内容 -->
                <div id="tenant-tab" class="tab-content">
                    <div class="card">
                        <div class="card-header">
                            <h3>租户用户列表</h3>
                            <button id="addTenantBtn" class="btn btn-primary">
                                <i class="fas fa-plus"></i> 添加租户
                            </button>
                        </div>
                        <div class="card-body">
                            <table class="table" id="tenantTable">
                                <thead>
                                    <tr>
                                        <th width="15%">账号</th>
                                        <th width="15%">昵称</th>
                                        <th width="15%">手机</th>
                                        <th width="15%">邮箱</th>
                                        <th width="20%">创建时间</th>
                                        <th width="20%">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 租户数据将通过JS动态加载 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 管理员表单弹窗 -->
    <div class="user-form-container" id="adminFormContainer">
        <div class="user-form">
            <div class="close-form" id="closeAdminForm">
                <i class="fas fa-times"></i>
            </div>
            <h3 class="form-title" id="adminFormTitle">添加管理员</h3>
            <form id="adminForm">
                <input type="hidden" id="adminId">
                <div class="form-group">
                    <label for="adminAccount">账号</label>
                    <input type="text" id="adminAccount" class="form-control" required>
                </div>
                <div class="form-group">
                    <label for="adminPassword">密码</label>
                    <input type="password" id="adminPassword" class="form-control" placeholder="不修改请留空">
                </div>
                <div class="form-group">
                    <label for="adminNickname">昵称</label>
                    <input type="text" id="adminNickname" class="form-control">
                </div>
                <div class="form-footer">
                    <button type="button" class="btn btn-secondary" id="cancelAdminForm">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 租户表单弹窗 -->
    <div class="user-form-container" id="tenantFormContainer">
        <div class="user-form">
            <div class="close-form" id="closeTenantForm">
                <i class="fas fa-times"></i>
            </div>
            <h3 class="form-title" id="tenantFormTitle">添加租户</h3>
            <form id="tenantForm">
                <input type="hidden" id="tenantId">
                <div class="form-group">
                    <label for="tenantAccount">账号</label>
                    <input type="text" id="tenantAccount" class="form-control" required>
                </div>
                <div class="form-group">
                    <label for="tenantPassword">密码</label>
                    <input type="password" id="tenantPassword" class="form-control" placeholder="不修改请留空">
                </div>
                <div class="form-group">
                    <label for="tenantNickname">昵称</label>
                    <input type="text" id="tenantNickname" class="form-control">
                </div>
                <div class="form-group">
                    <label for="tenantPhone">手机</label>
                    <input type="text" id="tenantPhone" class="form-control">
                </div>
                <div class="form-group">
                    <label for="tenantEmail">邮箱</label>
                    <input type="email" id="tenantEmail" class="form-control">
                </div>
                <div class="form-group">
                    <label for="tenantWechat">微信</label>
                    <input type="text" id="tenantWechat" class="form-control">
                </div>
                <div class="form-footer">
                    <button type="button" class="btn btn-secondary" id="cancelTenantForm">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 删除确认弹窗 -->
    <div class="user-form-container" id="deleteConfirmContainer">
        <div class="user-form" style="max-width: 400px;">
            <div class="close-form" id="closeDeleteConfirm">
                <i class="fas fa-times"></i>
            </div>
            <h3 class="form-title">删除确认</h3>
            <p>您确定要删除此用户吗？此操作无法撤销。</p>
            <input type="hidden" id="deleteUserId">
            <input type="hidden" id="deleteUserType">
            <div class="form-footer">
                <button type="button" class="btn btn-secondary" id="cancelDeleteConfirm">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">确认删除</button>
            </div>
        </div>
    </div>

    <!-- 必要的脚本 -->
    <script src="/static/js/main.js"></script>
    <script src="/static/js/sweetalert2@11"></script>
    <script src="/static/js/user-logout.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 检查权限
            const userType = localStorage.getItem('userType');
            if (userType !== 'admin') {
                window.location.href = '/index';
                return;
            }
            
            // 获取访问令牌
            const token = localStorage.getItem('accessToken');
            if (!token) {
                window.location.href = '/login07082';
                return;
            }
            
            // Tab切换功能
            const tabBtns = document.querySelectorAll('.tab-btn');
            const tabContents = document.querySelectorAll('.tab-content');
            
            tabBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    const tabId = btn.getAttribute('data-tab');
                    
                    // 取消所有选项卡的活动状态
                    tabBtns.forEach(b => b.classList.remove('active'));
                    tabContents.forEach(c => c.classList.remove('active'));
                    
                    // 激活当前选项卡
                    btn.classList.add('active');
                    document.getElementById(tabId).classList.add('active');
                });
            });
            
            // 加载管理员数据
            loadAdmins();
            
            // 加载租户数据
            loadTenants();
            
            // 表单弹窗开关
            document.getElementById('addAdminBtn').addEventListener('click', showAdminForm);
            document.getElementById('closeAdminForm').addEventListener('click', hideAdminForm);
            document.getElementById('cancelAdminForm').addEventListener('click', hideAdminForm);
            
            document.getElementById('addTenantBtn').addEventListener('click', showTenantForm);
            document.getElementById('closeTenantForm').addEventListener('click', hideTenantForm);
            document.getElementById('cancelTenantForm').addEventListener('click', hideTenantForm);
            
            document.getElementById('closeDeleteConfirm').addEventListener('click', hideDeleteConfirm);
            document.getElementById('cancelDeleteConfirm').addEventListener('click', hideDeleteConfirm);
            
            // 表单提交事件
            document.getElementById('adminForm').addEventListener('submit', handleAdminFormSubmit);
            document.getElementById('tenantForm').addEventListener('submit', handleTenantFormSubmit);
            document.getElementById('confirmDelete').addEventListener('click', handleDelete);
        });
        
        // API请求基础函数
        async function apiRequest(url, method, data = null) {
            const token = localStorage.getItem('accessToken');
            const headers = {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            };
            
            const options = {
                method,
                headers
            };
            
            if (data && (method === 'POST' || method === 'PUT')) {
                options.body = JSON.stringify(data);
            }
            
            try {
                const response = await fetch(url, options);
                return await response.json();
            } catch (error) {
                console.error('API请求错误:', error);
                Swal.fire({
                    icon: 'error',
                    title: '请求失败',
                    text: '发生网络错误，请稍后重试'
                });
                return null;
            }
        }
        
        // 加载管理员列表
        async function loadAdmins() {
            const admins = await apiRequest('/api/admins07082', 'GET');
            const tableBody = document.querySelector('#adminTable tbody');
            tableBody.innerHTML = '';
            
            if (Array.isArray(admins) && admins.length > 0) {
                admins.forEach(admin => {
                    // 防止删除当前登录的管理员
                    const currentUserId = localStorage.getItem('userId');
                    const isCurrentUser = admin.id === currentUserId;
                    
                    const tr = document.createElement('tr');
                    tr.innerHTML = `
                        <td>${admin.account}</td>
                        <td>${admin.nickname || '-'}</td>
                        <td>${admin.createdAt || '-'}</td>
                        <td class="action-cell">
                            <button class="btn btn-secondary btn-edit" data-id="${admin.id}" data-type="admin">
                                <i class="fas fa-edit"></i> 编辑
                            </button>
                            <button class="btn btn-danger btn-delete" data-id="${admin.id}" data-type="admin" ${isCurrentUser ? 'disabled' : ''}>
                                <i class="fas fa-trash"></i> 删除
                            </button>
                        </td>
                    `;
                    tableBody.appendChild(tr);
                });
                
                // 添加事件监听器
                document.querySelectorAll('#adminTable .btn-edit').forEach(btn => {
                    btn.addEventListener('click', () => editAdmin(btn.getAttribute('data-id')));
                });
                
                document.querySelectorAll('#adminTable .btn-delete').forEach(btn => {
                    if (!btn.disabled) {
                        btn.addEventListener('click', () => showDeleteConfirm(btn.getAttribute('data-id'), 'admin'));
                    }
                });
            } else {
                tableBody.innerHTML = '<tr><td colspan="4" class="text-center">没有管理员数据</td></tr>';
            }
        }
        
        // 加载租户列表
        async function loadTenants() {
            const tenants = await apiRequest('/api/tenants07082', 'GET');
            const tableBody = document.querySelector('#tenantTable tbody');
            tableBody.innerHTML = '';
            
            if (Array.isArray(tenants) && tenants.length > 0) {
                tenants.forEach(tenant => {
                    const tr = document.createElement('tr');
                    tr.innerHTML = `
                        <td>${tenant.account}</td>
                        <td>${tenant.nickname || '-'}</td>
                        <td>${tenant.phone || '-'}</td>
                        <td>${tenant.email || '-'}</td>
                        <td>${tenant.createdAt || '-'}</td>
                        <td class="action-cell">
                            <button class="btn btn-secondary btn-edit" data-id="${tenant.id}" data-type="tenant">
                                <i class="fas fa-edit"></i> 编辑
                            </button>
                            <button class="btn btn-danger btn-delete" data-id="${tenant.id}" data-type="tenant">
                                <i class="fas fa-trash"></i> 删除
                            </button>
                        </td>
                    `;
                    tableBody.appendChild(tr);
                });
                
                // 添加事件监听器
                document.querySelectorAll('#tenantTable .btn-edit').forEach(btn => {
                    btn.addEventListener('click', () => editTenant(btn.getAttribute('data-id')));
                });
                
                document.querySelectorAll('#tenantTable .btn-delete').forEach(btn => {
                    btn.addEventListener('click', () => showDeleteConfirm(btn.getAttribute('data-id'), 'tenant'));
                });
            } else {
                tableBody.innerHTML = '<tr><td colspan="6" class="text-center">没有租户数据</td></tr>';
            }
        }
        
        // 显示管理员表单
        function showAdminForm() {
            document.getElementById('adminFormTitle').textContent = '添加管理员';
            document.getElementById('adminId').value = '';
            document.getElementById('adminAccount').value = '';
            document.getElementById('adminPassword').value = '';
            document.getElementById('adminNickname').value = '';
            document.getElementById('adminAccount').disabled = false; // 新增时可以编辑账号
            document.getElementById('adminFormContainer').style.display = 'flex';
        }
        
        // 隐藏管理员表单
        function hideAdminForm() {
            document.getElementById('adminFormContainer').style.display = 'none';
        }
        
        // 显示租户表单
        function showTenantForm() {
            document.getElementById('tenantFormTitle').textContent = '添加租户';
            document.getElementById('tenantId').value = '';
            document.getElementById('tenantAccount').value = '';
            document.getElementById('tenantPassword').value = '';
            document.getElementById('tenantNickname').value = '';
            document.getElementById('tenantPhone').value = '';
            document.getElementById('tenantEmail').value = '';
            document.getElementById('tenantWechat').value = '';
            document.getElementById('tenantAccount').disabled = false; // 新增时可以编辑账号
            document.getElementById('tenantFormContainer').style.display = 'flex';
        }
        
        // 隐藏租户表单
        function hideTenantForm() {
            document.getElementById('tenantFormContainer').style.display = 'none';
        }
        
        // 编辑管理员
        async function editAdmin(adminId) {
            const response = await apiRequest(`/api/admin07082/${adminId}`, 'GET');
            if (response && response.code === 200) {
                const admin = response.data;
                document.getElementById('adminFormTitle').textContent = '编辑管理员';
                document.getElementById('adminId').value = admin.id;
                document.getElementById('adminAccount').value = admin.account;
                document.getElementById('adminPassword').value = ''; // 密码不显示
                document.getElementById('adminNickname').value = admin.nickname || '';
                document.getElementById('adminAccount').disabled = true; // 编辑时不可修改账号
                document.getElementById('adminFormContainer').style.display = 'flex';
            } else {
                Swal.fire({
                    icon: 'error',
                    title: '获取管理员信息失败',
                    text: response ? response.message : '未知错误'
                });
            }
        }
        
        // 编辑租户
        async function editTenant(tenantId) {
            const response = await apiRequest(`/api/tenant07082/${tenantId}`, 'GET');
            if (response && response.code === 200) {
                const tenant = response.data;
                document.getElementById('tenantFormTitle').textContent = '编辑租户';
                document.getElementById('tenantId').value = tenant.id;
                document.getElementById('tenantAccount').value = tenant.account;
                document.getElementById('tenantPassword').value = ''; // 密码不显示
                document.getElementById('tenantNickname').value = tenant.nickname || '';
                document.getElementById('tenantPhone').value = tenant.phone || '';
                document.getElementById('tenantEmail').value = tenant.email || '';
                document.getElementById('tenantWechat').value = tenant.wechat || '';
                document.getElementById('tenantAccount').disabled = true; // 编辑时不可修改账号
                document.getElementById('tenantFormContainer').style.display = 'flex';
            } else {
                Swal.fire({
                    icon: 'error',
                    title: '获取租户信息失败',
                    text: response ? response.message : '未知错误'
                });
            }
        }
        
        // 管理员表单提交
        async function handleAdminFormSubmit(event) {
            event.preventDefault();
            
            const adminId = document.getElementById('adminId').value;
            const account = document.getElementById('adminAccount').value;
            const password = document.getElementById('adminPassword').value;
            const nickname = document.getElementById('adminNickname').value;
            
            if (!account) {
                Swal.fire({
                    icon: 'error',
                    title: '表单验证失败',
                    text: '账号不能为空'
                });
                return;
            }
            
            // 构建请求数据
            const data = {
                account,
                nickname
            };
            
            // 只有当密码不为空时才添加到请求数据中
            if (password) {
                data.password = password;
            }
            
            let response;
            if (adminId) {
                // 更新管理员
                response = await apiRequest(`/api/admin07082/${adminId}`, 'PUT', data);
            } else {
                // 新增管理员 (新增时密码必填)
                if (!password) {
                    Swal.fire({
                        icon: 'error',
                        title: '表单验证失败',
                        text: '新增管理员时密码不能为空'
                    });
                    return;
                }
                response = await apiRequest('/api/admin07082', 'POST', data);
            }
            
            if (response && response.code === 200) {
                Swal.fire({
                    icon: 'success',
                    title: '操作成功',
                    text: adminId ? '管理员信息已更新' : '管理员已添加'
                });
                hideAdminForm();
                loadAdmins();
            } else {
                Swal.fire({
                    icon: 'error',
                    title: '操作失败',
                    text: response ? response.message : '未知错误'
                });
            }
        }
        
        // 租户表单提交
        async function handleTenantFormSubmit(event) {
            event.preventDefault();
            
            const tenantId = document.getElementById('tenantId').value;
            const account = document.getElementById('tenantAccount').value;
            const password = document.getElementById('tenantPassword').value;
            const nickname = document.getElementById('tenantNickname').value;
            const phone = document.getElementById('tenantPhone').value;
            const email = document.getElementById('tenantEmail').value;
            const wechat = document.getElementById('tenantWechat').value;
            
            if (!account) {
                Swal.fire({
                    icon: 'error',
                    title: '表单验证失败',
                    text: '账号不能为空'
                });
                return;
            }
            
            // 构建请求数据
            const data = {
                account,
                nickname,
                phone,
                email,
                wechat
            };
            
            // 只有当密码不为空时才添加到请求数据中
            if (password) {
                data.password = password;
            }
            
            let response;
            if (tenantId) {
                // 更新租户
                response = await apiRequest(`/api/tenant07082/${tenantId}`, 'PUT', data);
            } else {
                // 新增租户 (新增时密码必填)
                if (!password) {
                    Swal.fire({
                        icon: 'error',
                        title: '表单验证失败',
                        text: '新增租户时密码不能为空'
                    });
                    return;
                }
                response = await apiRequest('/api/tenant07082', 'POST', data);
            }
            
            if (response && response.code === 200) {
                Swal.fire({
                    icon: 'success',
                    title: '操作成功',
                    text: tenantId ? '租户信息已更新' : '租户已添加'
                });
                hideTenantForm();
                loadTenants();
            } else {
                Swal.fire({
                    icon: 'error',
                    title: '操作失败',
                    text: response ? response.message : '未知错误'
                });
            }
        }
        
        // 显示删除确认框
        function showDeleteConfirm(userId, userType) {
            document.getElementById('deleteUserId').value = userId;
            document.getElementById('deleteUserType').value = userType;
            document.getElementById('deleteConfirmContainer').style.display = 'flex';
        }
        
        // 隐藏删除确认框
        function hideDeleteConfirm() {
            document.getElementById('deleteConfirmContainer').style.display = 'none';
        }
        
        // 处理删除
        async function handleDelete() {
            const userId = document.getElementById('deleteUserId').value;
            const userType = document.getElementById('deleteUserType').value;
            
            if (!userId || !userType) {
                return;
            }
            
            const endpoint = userType === 'admin' ? `/api/admin07082/${userId}` : `/api/tenant07082/${userId}`;
            const response = await apiRequest(endpoint, 'DELETE');
            
            if (response && response.code === 200) {
                Swal.fire({
                    icon: 'success',
                    title: '删除成功',
                    text: userType === 'admin' ? '管理员已删除' : '租户已删除'
                });
                
                hideDeleteConfirm();
                
                if (userType === 'admin') {
                    loadAdmins();
                } else {
                    loadTenants();
                }
            } else {
                Swal.fire({
                    icon: 'error',
                    title: '删除失败',
                    text: response ? response.message : '未知错误'
                });
            }
        }
    </script>
</body>
</html>
