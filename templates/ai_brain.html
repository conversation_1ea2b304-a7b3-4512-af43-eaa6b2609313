<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI招聘客服管理系统 - AI大脑</title>
    <link rel="stylesheet" href="/static/css/all.min.css">
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="stylesheet" href="/static/css/ai_brain.css">
</head>
<body>
    <div class="app-wrapper">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>AI招聘客服管理系统</h2>
            </div>
            <div class="sidebar-menu">
                <div class="sidebar-menu-item" data-link="index">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>仪表盘</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-briefcase"></i>
                    <span>职位管理</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="job_classification">
                        <i class="fas fa-tags"></i>
                        <span>职位分类</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="batch_management">
                        <i class="fas fa-layer-group"></i>
                        <span>批量管理</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="positions">
                        <i class="fas fa-list"></i>
                        <span>职位列表</span>
                    </div>
                </div>
                <div class="sidebar-menu-item" data-link="candidates">
                    <i class="fas fa-user-tie"></i>
                    <span>候选人管理</span>
                </div>
                <div class="sidebar-menu-item" data-link="virtual_hr">
                    <i class="fas fa-robot"></i>
                    <span>虚拟HR</span>
                </div>
                <div class="sidebar-menu-item" data-link="scenes">
                    <i class="fas fa-tasks"></i>
                    <span>面试场景</span>
                </div>
                <div class="sidebar-menu-item" data-link="interview">
                    <i class="fas fa-comments"></i>
                    <span>面试互动（仅供测试）</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-star"></i>
                    <span>胜任力模型</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="triggers">
                        <i class="fas fa-bolt"></i>
                        <span>触发器</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="classifier">
                        <i class="fas fa-filter"></i>
                        <span>分类器</span>
                    </div>
                </div>
                <div class="sidebar-menu-item active" data-link="ai_brain">
                    <i class="fas fa-brain"></i>
                    <span>AI大脑</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-chart-line"></i>
                    <span>AI数据透视</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="scheduled_analysis">
                        <i class="fas fa-clock"></i>
                        <span>客情分析</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="qa_engine">
                        <i class="fas fa-question-circle"></i>
                        <span>QA引擎</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-key"></i>
                    <span>APIKey管理</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="apikey_create">
                        <i class="fas fa-plus"></i>
                        <span>创建APIKey</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="apikey_usage">
                        <i class="fas fa-chart-bar"></i>
                        <span>用量统计</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-cog"></i>
                    <span>用户设置</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="channel_management">
                        <i class="fas fa-plug"></i>
                        <span>渠道接入管理</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="global_settings">
                        <i class="fas fa-sliders-h"></i>
                        <span>全局配置</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-cogs"></i>
                    <span>系统设置</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item admin-only" data-link="user_management07082">
                        <i class="fas fa-user-cog"></i>
                        <span>用户管理</span>
                    </div>
                    <div class="sidebar-menu-item admin-only" data-link="system_config">
                        <i class="fas fa-wrench"></i>
                        <span>系统配置</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部导航栏 -->
            <div class="topbar">
                <div class="topbar-toggle">
                    <i class="fas fa-bars"></i>
                </div>
                <div class="topbar-title">
                    AI大脑
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content">
                <!-- AI配置卡片 -->
                <div class="card mb-20">
                    <div class="card-header">
                        <h3>AI模型配置</h3>
                    </div>
                    <div class="card-body">
                        <form id="aiConfigForm">
                            <div class="form-group">
                                <label for="baseUrl">API 基础 URL</label>
                                <input type="text" id="baseUrl" name="base_url" class="form-control" placeholder="例如: https://api.openai.com/v1">
                            </div>
                            <div class="form-group">
                                <label for="apiKey">API 密钥</label>
                                <div class="input-with-button">
                                    <input type="password" id="apiKey" name="api_key" class="form-control" placeholder="您的 API 密钥">
                                    <button type="button" id="showApiKeyBtn" class="btn btn-light btn-sm">
                                        <i class="fas fa-eye"></i> 显示密钥
                                    </button>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="modelName">模型名称</label>
                                <input type="text" id="modelName" name="model_name" class="form-control" placeholder="例如: gpt-4">
                            </div>
                            <button type="submit" class="btn btn-primary">保存配置</button>
                        </form>
                    </div>
                </div>

                <!-- 提示词编辑卡片 -->
                <div class="card">
                    <div class="card-header">
                        <h3>提示词编辑器</h3>
                    </div>
                    <div class="card-body">
                        <div class="prompt-editor-container">
                            <div class="prompt-editor-section">
                                <h4>总体提示词</h4>
                                <div class="prompt-editor-wrapper">
                                    <textarea id="defaultPromptEditor" class="prompt-editor"></textarea>
                                </div>
                                <button type="button" id="saveDefaultPromptBtn" class="btn btn-primary mt-10">保存总体提示词</button>
                            </div>
                            <div class="prompt-preview-section">
                                <h4>预览</h4>
                                <div id="defaultPromptPreview" class="prompt-preview"></div>
                            </div>
                        </div>
                        
                        <hr class="my-20">
                        
                        <div class="prompt-editor-container">
                            <div class="prompt-editor-section">
                                <h4>场景提示词{scene_prompt}</h4>
                                <br>
                                <span>可用变量及含义：{scene_name}场景名称，{scene_description}场景描述，{scene_questions}场景问题</span>
                                <div class="prompt-editor-wrapper">
                                    <textarea id="scenePromptEditor" class="prompt-editor"></textarea>
                                </div>
                                <button type="button" id="saveScenePromptBtn" class="btn btn-primary mt-10">保存场景提示词</button>
                            </div>
                            <div class="prompt-preview-section">
                                <h4>预览</h4>
                                <div id="scenePromptPreview" class="prompt-preview"></div>
                            </div>
                        </div>
                        
                        <hr class="my-20">
                        
                        <div class="prompt-editor-container">
                            <div class="prompt-editor-section">
                                <h4>用户信息提示词{user_info_prompt}</h4>
                                <br>
                                <span>可用变量及含义：{name}姓名，{phone}联系方式，{wechat}微信，{city}城市，{address}地址，{gender}性别，{age}年龄，{score}分数</span>
                                <div class="prompt-editor-wrapper">
                                    <textarea id="userinfoPromptEditor" class="prompt-editor"></textarea>
                                </div>
                                <button type="button" id="saveUserinfoPromptBtn" class="btn btn-primary mt-10">保存用户信息提示词</button>
                            </div>
                            <div class="prompt-preview-section">
                                <h4>预览</h4>
                                <div id="userinfoPromptPreview" class="prompt-preview"></div>
                            </div>
                        </div>
                        
                        <hr class="my-20">
                        
                        <div class="prompt-editor-container">
                            <div class="prompt-editor-section">
                                <h4>职位提示词{position_prompt}</h4>
                                <br>
                                <span>可用变量及含义：{positionName}职位名称，{jobCity}工作地点，{age}年龄要求，{gender}性别要求，{educationRequirements}学历要求，{experienceRequirements}岗位要求，{salaryRange}薪资范围</span>
                                <div class="prompt-editor-wrapper">
                                    <textarea id="positionPromptEditor" class="prompt-editor"></textarea>
                                </div>
                                <button type="button" id="savePositionPromptBtn" class="btn btn-primary mt-10">保存职位提示词</button>
                            </div>
                            <div class="prompt-preview-section">
                                <h4>预览</h4>
                                <div id="positionPromptPreview" class="prompt-preview"></div>
                            </div>
                        </div>
                        
                        <hr class="my-20">
                        
                        <div class="prompt-editor-container">
                            <div class="prompt-editor-section">
                                <h4>评分规则提示词{scoring_rules_prompt}</h4>
                                <br>
                                <span>可用变量及含义：{score_trigger}分数触发器，{semantic_trigger}语意触发器,{semantic_classifier}语意分类器,{answer_classifier}答案分类器</span>
                                <div class="prompt-editor-wrapper">
                                    <textarea id="scoringRulesPromptEditor" class="prompt-editor"></textarea>
                                </div>
                                <button type="button" id="saveScoringRulesPromptBtn" class="btn btn-primary mt-10">保存评分规则提示词</button>
                            </div>
                            <div class="prompt-preview-section">
                                <h4>预览</h4>
                                <div id="scoringRulesPromptPreview" class="prompt-preview"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 脚本引用 -->
    <script src="/static/js/main.js"></script>
    <script src="/static/js/api.js"></script>
    <script src="/static/js/marked.min.js"></script>
    <script src="/static/js/ai_brain.js"></script>
    <script src="/static/js/sweetalert2@11"></script>
    <script src="/static/js/auth07082.js"></script>
    <script src="/static/js/auth-check.js"></script>
    <script src="/static/js/user-logout.js"></script>
</body>
</html> 