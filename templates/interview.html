<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI招聘客服管理系统 - 面试互动</title>
    <link rel="stylesheet" href="/static/css/all.min.css">
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="stylesheet" href="/static/css/interview.css">
    <style>
        .apikey-input-container {
            display: flex;
            align-items: center;
            margin-right: 10px;
        }
        .apikey-input-container label {
            margin-right: 5px;
            white-space: nowrap;
        }
        .apikey-input-container input {
            padding: 4px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 12px;
            width: 180px;
        }
    </style>
</head>
<body>
    <div class="app-wrapper">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>AI招聘客服管理系统</h2>
            </div>
            <div class="sidebar-menu">
                <div class="sidebar-menu-item" data-link="index">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>仪表盘</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-briefcase"></i>
                    <span>职位管理</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="job_classification">
                        <i class="fas fa-tags"></i>
                        <span>职位分类</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="batch_management">
                        <i class="fas fa-layer-group"></i>
                        <span>批量管理</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="positions">
                        <i class="fas fa-list"></i>
                        <span>职位列表</span>
                    </div>
                </div>
                <div class="sidebar-menu-item" data-link="candidates">
                    <i class="fas fa-user-tie"></i>
                    <span>候选人管理</span>
                </div>
                <div class="sidebar-menu-item" data-link="virtual_hr">
                    <i class="fas fa-robot"></i>
                    <span>虚拟HR</span>
                </div>
                <div class="sidebar-menu-item" data-link="scenes">
                    <i class="fas fa-tasks"></i>
                    <span>面试场景</span>
                </div>
                <div class="sidebar-menu-item active" data-link="interview">
                    <i class="fas fa-comments"></i>
                    <span>面试互动（仅供测试）</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-star"></i>
                    <span>胜任力模型</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="triggers">
                        <i class="fas fa-bolt"></i>
                        <span>触发器</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="classifier">
                        <i class="fas fa-filter"></i>
                        <span>分类器</span>
                    </div>
                </div>
                <div class="sidebar-menu-item" data-link="ai_brain">
                    <i class="fas fa-brain"></i>
                    <span>AI大脑</span>
                </div>
                <div class="sidebar-menu-item has-submenu open">
                    <i class="fas fa-chart-line"></i>
                    <span>AI数据透视</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu show">
                    <div class="sidebar-menu-item active" data-link="scheduled_analysis">
                        <i class="fas fa-clock"></i>
                        <span>客情分析</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="qa_engine">
                        <i class="fas fa-question-circle"></i>
                        <span>QA引擎</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-key"></i>
                    <span>APIKey管理</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="apikey_create">
                        <i class="fas fa-plus"></i>
                        <span>创建APIKey</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="apikey_usage">
                        <i class="fas fa-chart-bar"></i>
                        <span>用量统计</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-cog"></i>
                    <span>用户设置</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="channel_management">
                        <i class="fas fa-plug"></i>
                        <span>渠道接入管理</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="global_settings">
                        <i class="fas fa-sliders-h"></i>
                        <span>全局配置</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-cogs"></i>
                    <span>系统设置</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item admin-only" data-link="user_management07082">
                        <i class="fas fa-user-cog"></i>
                        <span>用户管理</span>
                    </div>
                    <div class="sidebar-menu-item admin-only" data-link="system_config">
                        <i class="fas fa-wrench"></i>
                        <span>系统配置</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部导航栏 -->
            <div class="topbar">
                <div class="topbar-toggle">
                    <i class="fas fa-bars"></i>
                </div>
                <div class="topbar-title">
                    面试互动
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content">
                <div class="interview-container">
                    <!-- 左侧用户列表 -->
                    <div class="users-panel">
                        <div class="panel-header">
                            <h3>候选人列表</h3>
                            <div class="search-box">
                                <input type="text" id="userSearch" placeholder="搜索候选人...">
                                <i class="fas fa-search"></i>
                            </div>
                        </div>
                        <div class="panel-body">
                            <div class="users-list" id="usersList">
                                <!-- 用户列表将通过JS动态生成 -->
                                <div class="users-list-loading">
                                    <p>加载中...</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧聊天窗口 -->
                    <div class="chat-panel">
                        <div class="panel-header">
                            <div class="chat-user-info">
                                <img id="chatUserAvatar" src="/static/img/default-avatar.png" alt="用户头像" class="chat-avatar">
                                <div class="chat-user-details">
                                    <h3 id="chatUserName">请选择候选人</h3>
                                    <p id="chatUserPosition">-</p>
                                </div>
                            </div>
                            <div class="chat-actions">
                                <div class="apikey-input-container">
                                    <label for="apiKeyInput">APIKey:</label>
                                    <input type="text" id="apiKeyInput" placeholder="输入您的APIKey" title="API请求将使用此APIKey">
                                </div>
                                <div class="chat-scene">
                                    当前场景: <span id="userScene">-</span>
                                </div>
                                <div class="chat-score">
                                    当前分数: <span id="userScore">0</span>
                                </div>
                                <button id="clearChatBtn" class="btn btn-light btn-sm" disabled>
                                    <i class="fas fa-eraser"></i> 清空聊天
                                </button>
                                <button id="viewPromptBtn" class="btn btn-light btn-sm" disabled>
                                    <i class="fas fa-file-alt"></i> 查看提示词
                                </button>
                            </div>
                        </div>
                        <div class="panel-body">
                            <div id="chatWindow" class="chat-window">
                                <div class="no-user-selected">
                                    <div class="no-user-content">
                                        <i class="fas fa-comments"></i>
                                        <p>请从左侧选择一位候选人开始面试互动</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="chat-input-area">
                            <textarea id="messageInput" placeholder="按Enter发送消息，Shift+Enter换行..." disabled></textarea>
                            <div class="chat-input-actions">
                                <button id="sendMessageBtn" class="btn btn-primary" disabled>
                                    <i class="fas fa-paper-plane"></i> 发送
                                </button>
                                <div class="chat-input-hint">按Enter发送，Shift+Enter换行</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 提示词模态框 -->
    <div class="modal" id="promptModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>当前用户提示词</h3>
                <span class="modal-close">&times;</span>
            </div>
            <div class="modal-body">
                <div class="prompt-editor-container">
                    <div class="prompt-editor-section">
                        <h4>原始提示词</h4>
                        <div class="prompt-editor-wrapper">
                            <textarea id="promptTextEditor" class="prompt-editor" readonly></textarea>
                        </div>
                    </div>
                    <div class="prompt-preview-section">
                        <h4>预览</h4>
                        <div id="promptPreview" class="prompt-preview"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/jquery-3.6.0.min.js"></script>
    <script src="/static/js/marked.min.js"></script>
    <script src="/static/js/main.js"></script>
    <script src="/static/js/interview.js"></script>
    <script src="/static/js/sweetalert2@11"></script>
    <script src="/static/js/auth07082.js"></script>
    <script src="/static/js/auth-check.js"></script>
    <script src="/static/js/user-logout.js"></script>
</body>
</html> 