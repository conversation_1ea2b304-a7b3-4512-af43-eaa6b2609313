<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统登录</title>
    <link href="/static/css/bootstrap.min.css" rel="stylesheet">
    <link href="/static/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #3498db;
            --accent-color: #2ecc71;
            --dark-color: #2c3e50;
            --light-color: #ecf0f1;
        }
        
        body {
            margin: 0;
            padding: 0;
            font-family: 'Arial', sans-serif;
            background-color: #0f172a;
            overflow: hidden;
            height: 100vh;
            position: relative;
        }
        
        .particles-container07082 {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }
        
        .login-container07082 {
            position: relative;
            z-index: 10;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .login-box07082 {
            width: 400px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: white;
            overflow: hidden;
            position: relative;
            z-index: 10; /* 确保登录框在粒子效果之上 */
        }
        
        .login-box07082::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(60deg, transparent, rgba(46, 204, 113, 0.1), transparent);
            transform: rotate(45deg);
            animation: shimmer 6s infinite linear;
        }
        
        @keyframes shimmer {
            0% { transform: translateX(-100%) rotate(45deg); }
            100% { transform: translateX(100%) rotate(45deg); }
        }
        
        .login-header07082 {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-header07082 h2 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 5px;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            letter-spacing: 1px;
        }
        
        .login-header07082 p {
            color: rgba(255, 255, 255, 0.6);
            font-size: 14px;
        }
        
        .input-group07082 {
            position: relative;
            margin-bottom: 25px;
        }
        
        .input-group07082 input {
            width: 100%;
            padding: 12px 15px 12px 45px;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            border-radius: 10px;
            color: white;
            font-size: 16px;
            transition: all 0.3s;
            outline: none;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .input-group07082 input:focus {
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.2);
        }
        
        .input-group07082 input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }
        
        .input-group07082 i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255, 255, 255, 0.5);
            font-size: 20px;
        }
        
        .btn-login07082 {
            width: 100%;
            padding: 12px;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            border: none;
            border-radius: 10px;
            color: white;
            font-weight: 700;
            font-size: 18px;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
            position: relative;
            z-index: 100; /* 增加z-index确保按钮在顶层 */
        }
        
        .btn-login07082:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(52, 152, 219, 0.5);
            cursor: pointer; /* 确保鼠标悬停时显示指针 */
        }
        
        .btn-login07082:active {
            transform: translateY(0);
        }
        
        .login-type07082 {
            display: flex;
            justify-content: center;
            margin-bottom: 25px;
            position: relative;
        }
        
        .login-type07082 input[type="radio"] {
            display: none;
        }
        
        .login-type07082 label {
            padding: 10px 20px;
            margin: 0 5px;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.3s;
            color: rgba(255, 255, 255, 0.7);
            position: relative;
            z-index: 1;
        }
        
        .login-type07082 .radio-slider07082 {
            position: absolute;
            top: 0;
            height: 100%;
            width: 50%;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            transition: all 0.3s ease;
            z-index: 0;
        }
        
        #admin07082:checked ~ .radio-slider07082 {
            left: 0;
        }
        
        #tenant07082:checked ~ .radio-slider07082 {
            left: 50%;
        }
        
        .login-type07082 input:checked + label {
            color: white;
            font-weight: 600;
        }
        
        .digital-wave07082 {
            position: absolute;
            bottom: -50px;
            left: 0;
            width: 100%;
            height: 100px;
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="%233498db" fill-opacity="0.2" d="M0,160L48,165.3C96,171,192,181,288,186.7C384,192,480,192,576,186.7C672,181,768,171,864,165.3C960,160,1056,160,1152,154.7C1248,149,1344,139,1392,133.3L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>');
            background-size: cover;
            animation: wave 10s linear infinite;
        }
        
        @keyframes wave {
            0% { background-position: 0 0; }
            100% { background-position: 1440px 0; }
        }
        
        .alert07082 {
            padding: 10px 15px;
            margin-bottom: 20px;
            border-radius: 10px;
            background: rgba(231, 76, 60, 0.2);
            color: #e74c3c;
            border: 1px solid rgba(231, 76, 60, 0.3);
            display: none;
        }
        
        @keyframes float-up {
            0% { transform: translateY(50px); opacity: 0; }
            100% { transform: translateY(0); opacity: 1; }
        }
        
        .login-box07082 {
            animation: float-up 1s ease-out forwards;
        }
        
        .floating-tech07082 {
            position: absolute;
            font-size: 60px;
            color: rgba(255, 255, 255, 0.03);
            z-index: 0;
            pointer-events: none;
            animation: float-around 20s linear infinite;
        }
        
        @keyframes float-around {
            0% { transform: translateY(0) translateX(0) rotate(0deg); }
            25% { transform: translateY(-20px) translateX(10px) rotate(5deg); }
            50% { transform: translateY(0) translateX(20px) rotate(0deg); }
            75% { transform: translateY(20px) translateX(10px) rotate(-5deg); }
            100% { transform: translateY(0) translateX(0) rotate(0deg); }
        }
        
        .system-name07082 {
            position: absolute;
            top: 40px;
            text-align: center;
            width: 100%;
            font-size: 28px;
            font-weight: 700;
            color: white;
            text-shadow: 0 0 10px rgba(52, 152, 219, 0.7);
            letter-spacing: 2px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 0.7; }
            50% { opacity: 1; }
            100% { opacity: 0.7; }
        }
    </style>
</head>
<body>
    <div class="particles-container07082" id="particles-js07082"></div>
    
    <div class="system-name07082">人工智能招聘助手系统</div>
    
    <div class="login-container07082">
        <div class="login-box07082">
            <div class="login-header07082">
                <h2>系统登录</h2>
                <p>请输入账号和密码进行登录</p>
            </div>
            
            <div class="alert07082" id="login-alert07082">
                <i class="fas fa-exclamation-circle"></i> 
                <span id="alert-message07082">登录失败，请检查账号和密码</span>
            </div>
            
            <div class="login-type07082">
                <input type="radio" id="admin07082" name="user_type" value="admin" checked>
                <label for="admin07082">管理员</label>
                
                <input type="radio" id="tenant07082" name="user_type" value="tenant">
                <label for="tenant07082">租户</label>
                
                <div class="radio-slider07082"></div>
            </div>
            
            <div class="input-group07082">
                <i class="fas fa-user"></i>
                <input type="text" id="account07082" placeholder="请输入账号" required>
            </div>
            
            <div class="input-group07082">
                <i class="fas fa-lock"></i>
                <input type="password" id="password07082" placeholder="请输入密码" required>
            </div>
            
            <button class="btn-login07082" id="login-btn07082">
                <i class="fas fa-sign-in-alt"></i> 登录
            </button>
            
            <div class="digital-wave07082"></div>
        </div>
    </div>
    
    <!-- 添加浮动科技元素 -->
    <div class="floating-tech07082" style="top: 15%; left: 15%;">
        <i class="fas fa-microchip"></i>
    </div>
    <div class="floating-tech07082" style="top: 25%; right: 20%;">
        <i class="fas fa-brain"></i>
    </div>
    <div class="floating-tech07082" style="bottom: 20%; left: 25%;">
        <i class="fas fa-robot"></i>
    </div>
    <div class="floating-tech07082" style="bottom: 30%; right: 15%;">
        <i class="fas fa-network-wired"></i>
    </div>
    
    <script src="/static/js/particles.min.js"></script>
    <script src="/static/js/sweetalert2@11"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // 初始化粒子效果
            particlesJS('particles-js07082', {
                "particles": {
                    "number": {
                        "value": 80,
                        "density": {
                            "enable": true,
                            "value_area": 800
                        }
                    },
                    "color": {
                        "value": "#3498db"
                    },
                    "shape": {
                        "type": "circle",
                        "stroke": {
                            "width": 0,
                            "color": "#000000"
                        },
                        "polygon": {
                            "nb_sides": 5
                        }
                    },
                    "opacity": {
                        "value": 0.5,
                        "random": true,
                        "anim": {
                            "enable": true,
                            "speed": 1,
                            "opacity_min": 0.1,
                            "sync": false
                        }
                    },
                    "size": {
                        "value": 3,
                        "random": true,
                        "anim": {
                            "enable": true,
                            "speed": 2,
                            "size_min": 0.1,
                            "sync": false
                        }
                    },
                    "line_linked": {
                        "enable": true,
                        "distance": 150,
                        "color": "#2ecc71",
                        "opacity": 0.4,
                        "width": 1
                    },
                    "move": {
                        "enable": true,
                        "speed": 1,
                        "direction": "none",
                        "random": true,
                        "straight": false,
                        "out_mode": "out",
                        "bounce": false,
                        "attract": {
                            "enable": false,
                            "rotateX": 600,
                            "rotateY": 1200
                        }
                    }
                },
                "interactivity": {
                    "detect_on": "canvas",
                    "events": {
                        "onhover": {
                            "enable": true,
                            "mode": "grab"
                        },
                        "onclick": {
                            "enable": false, /* 禁用粒子点击事件，防止干扰按钮点击 */
                            "mode": "push"
                        },
                        "resize": true
                    },
                    "modes": {
                        "grab": {
                            "distance": 140,
                            "line_linked": {
                                "opacity": 1
                            }
                        },
                        "bubble": {
                            "distance": 400,
                            "size": 40,
                            "duration": 2,
                            "opacity": 8,
                            "speed": 3
                        },
                        "repulse": {
                            "distance": 200,
                            "duration": 0.4
                        },
                        "push": {
                            "particles_nb": 4
                        },
                        "remove": {
                            "particles_nb": 2
                        }
                    }
                },
                "retina_detect": true
            });
            
            // 登录处理
            const loginBtn = document.getElementById('login-btn07082');
            const accountInput = document.getElementById('account07082');
            const passwordInput = document.getElementById('password07082');
            const loginAlert = document.getElementById('login-alert07082');
            const alertMessage = document.getElementById('alert-message07082');
            
            // 使用多种事件绑定方式确保按钮可点击
            function handleLogin() {
                const account = accountInput.value.trim();
                const password = passwordInput.value.trim();
                const userType = document.querySelector('input[name="user_type"]:checked').value;
                
                if (!account || !password) {
                    showAlert('账号和密码不能为空');
                    return;
                }
                
                // 添加按钮加载效果
                loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 登录中...';
                loginBtn.disabled = true;
                
                // 使用异步IIFE执行登录逻辑
                (async () => {
                    try {
                        const response = await fetch('/api/login07082', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({ account, password, user_type: userType })
                        });
                        
                        const data = await response.json();
                        
                        if (data.code === 200) {
                            // 登录成功，保存JWT令牌到localStorage
                            localStorage.setItem('accessToken', data.access_token);
                            localStorage.setItem('userType', data.user_type);
                            localStorage.setItem('userId', data.data.id);
                            localStorage.setItem('userAccount', data.data.account);
                            localStorage.setItem('userNickname', data.data.nickname || '');
                            
                            // 显示成功消息并跳转
                            Swal.fire({
                                title: '登录成功',
                                text: '正在跳转到系统主页...',
                                icon: 'success',
                                timer: 1500,
                                showConfirmButton: false
                            }).then(() => {
                                window.location.href = '/index';
                            });
                        } else {
                            // 登录失败
                            showAlert(data.message || '登录失败，请检查账号和密码');
                        }
                    } catch (error) {
                        console.error('登录请求失败:', error);
                        showAlert('网络错误，请稍后重试');
                    } finally {
                        loginBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> 登录';
                        loginBtn.disabled = false;
                    }
                })();
            }
            
            // 使用多种事件绑定方式确保按钮点击正常工作
            loginBtn.addEventListener('click', handleLogin);
            loginBtn.addEventListener('touchend', handleLogin); // 添加触摸事件支持
            
            // 添加鼠标按下效果增强视觉反馈
            loginBtn.addEventListener('mousedown', function() {
                this.style.transform = 'scale(0.98)';
            });
            
            loginBtn.addEventListener('mouseup', function() {
                this.style.transform = '';
            });
            
            function showAlert(message) {
                alertMessage.textContent = message;
                loginAlert.style.display = 'block';
                
                // 添加抖动效果
                loginAlert.classList.add('shake');
                setTimeout(() => {
                    loginAlert.classList.remove('shake');
                }, 500);
                
                // 3秒后自动隐藏
                setTimeout(() => {
                    loginAlert.style.display = 'none';
                }, 3000);
            }
            
            // 回车键登录
            passwordInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    loginBtn.click();
                }
            });
        });
    </script>
</body>
</html> 