#!/usr/bin/env python3
"""
清理重复的执行日志记录
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from DadabaseControl.DatabaseControl import _connect_db

def cleanup_duplicate_execution_logs():
    """清理重复的执行日志，保留最新的记录"""
    print("=== 清理重复的执行日志 ===")
    
    conn = _connect_db()
    if not conn:
        print("数据库连接失败")
        return
    
    try:
        cursor = conn.cursor()
        
        # 1. 找出所有有重复记录的分析ID
        cursor.execute("""
            SELECT analysis_id, COUNT(*) as count
            FROM analysis_execution_log 
            GROUP BY analysis_id
            HAVING COUNT(*) > 1
            ORDER BY count DESC
        """)
        
        duplicates = cursor.fetchall()
        if not duplicates:
            print("✅ 没有发现重复的执行记录")
            return
        
        print(f"发现 {len(duplicates)} 个分析ID有重复记录:")
        for analysis_id, count in duplicates:
            print(f"  - {analysis_id}: {count} 条记录")
        
        # 2. 对每个有重复记录的分析ID，保留最新的记录
        total_deleted = 0
        for analysis_id, count in duplicates:
            print(f"\n处理分析ID: {analysis_id}")
            
            # 获取该分析ID的所有记录，按创建时间排序
            cursor.execute("""
                SELECT id, status, total_users, successful_analyses, failed_analyses, createdAt
                FROM analysis_execution_log 
                WHERE analysis_id = ?
                ORDER BY createdAt DESC
            """, (analysis_id,))
            
            records = cursor.fetchall()
            print(f"  找到 {len(records)} 条记录:")
            
            for i, record in enumerate(records):
                log_id, status, total_users, successful, failed, created_at = record
                print(f"    {i+1}. ID: {log_id[:8]}... | 状态: {status} | "
                      f"总用户: {total_users} | 时间: {created_at}")
            
            if len(records) > 1:
                # 保留第一条（最新的），删除其余的
                keep_record = records[0]
                delete_records = records[1:]
                
                print(f"  保留记录: {keep_record[0][:8]}... (状态: {keep_record[1]})")
                print(f"  删除 {len(delete_records)} 条旧记录:")
                
                for record in delete_records:
                    log_id = record[0]
                    status = record[1]
                    created_at = record[5]
                    
                    cursor.execute("DELETE FROM analysis_execution_log WHERE id = ?", (log_id,))
                    print(f"    ❌ 删除: {log_id[:8]}... (状态: {status}, 时间: {created_at})")
                    total_deleted += 1
        
        # 3. 提交更改
        conn.commit()
        print(f"\n✅ 清理完成，共删除 {total_deleted} 条重复记录")
        
        # 4. 验证清理结果
        cursor.execute("""
            SELECT analysis_id, COUNT(*) as count
            FROM analysis_execution_log 
            GROUP BY analysis_id
            HAVING COUNT(*) > 1
        """)
        
        remaining_duplicates = cursor.fetchall()
        if remaining_duplicates:
            print(f"⚠️  仍有 {len(remaining_duplicates)} 个分析ID有重复记录")
        else:
            print("✅ 所有重复记录已清理完成")
        
    except Exception as e:
        print(f"清理失败: {e}")
        conn.rollback()
    finally:
        conn.close()

def show_current_logs():
    """显示当前的执行日志状态"""
    print("\n=== 当前执行日志状态 ===")
    
    conn = _connect_db()
    if not conn:
        print("数据库连接失败")
        return
    
    try:
        cursor = conn.cursor()
        
        # 统计信息
        cursor.execute("SELECT COUNT(*) FROM analysis_execution_log")
        total_logs = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(DISTINCT analysis_id) FROM analysis_execution_log")
        unique_analyses = cursor.fetchone()[0]
        
        print(f"总执行日志数: {total_logs}")
        print(f"唯一分析ID数: {unique_analyses}")
        
        if total_logs > unique_analyses:
            print(f"⚠️  存在 {total_logs - unique_analyses} 条重复记录")
        else:
            print("✅ 没有重复记录")
        
        # 显示最近的记录
        cursor.execute("""
            SELECT analysis_id, status, total_users, successful_analyses, failed_analyses, execution_time
            FROM analysis_execution_log 
            ORDER BY createdAt DESC 
            LIMIT 5
        """)
        
        recent_logs = cursor.fetchall()
        if recent_logs:
            print(f"\n最近5条执行日志:")
            for i, log in enumerate(recent_logs):
                analysis_id, status, total_users, successful, failed, exec_time = log
                print(f"  {i+1}. {analysis_id[:8]}... | {status} | "
                      f"用户:{total_users} | 成功:{successful} | 失败:{failed} | {exec_time}")
        
    except Exception as e:
        print(f"查询失败: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    print("开始清理重复的执行日志...")
    
    # 显示当前状态
    show_current_logs()
    
    # 执行清理
    cleanup_duplicate_execution_logs()
    
    # 显示清理后状态
    show_current_logs()
    
    print("\n清理完成!")
