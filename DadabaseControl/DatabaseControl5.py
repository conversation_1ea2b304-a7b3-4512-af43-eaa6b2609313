import sqlite3
from datetime import datetime
import uuid
from DadabaseControl.DatabaseControl import DB_NAME,_connect_db,_serialize_value,_deserialize_value,_deserialize_id_list,_serialize_id_list

DB_NAME = "sqlite3.db"

def _connect_db():
    """建立数据库连接"""
    try:
        conn = sqlite3.connect(DB_NAME)
        return conn
    except sqlite3.Error as e:
        print(f"数据库连接失败: {e}")
        return None


# Scene表操作
def insert_scene(data: dict):
    """
    插入一条新的场景记录。
    如果 sceneId 已存在，则不插入。
    """
    conn = _connect_db()
    if not conn:
        return False

    data["createdAt"] = data.get("createdAt", datetime.now().isoformat())
    data["sceneId"] = str(uuid.uuid4()).replace("-", "")
    
    # 处理多选字段，序列化为JSON字符串
    if "semantic_classifier_ids" in data:
        data["semantic_classifier_ids"] = _serialize_id_list(data["semantic_classifier_ids"])
    if "score_trigger_ids" in data:
        data["score_trigger_ids"] = _serialize_id_list(data["score_trigger_ids"])
    if "semantic_trigger_ids" in data:
        data["semantic_trigger_ids"] = _serialize_id_list(data["semantic_trigger_ids"])
        
    serialized_data = {k: _serialize_value(v) for k, v in data.items()}

    columns = ', '.join(serialized_data.keys())
    placeholders = ', '.join(['?' for _ in serialized_data.keys()])
    insert_sql = f"INSERT OR IGNORE INTO scene ({columns}) VALUES ({placeholders})"

    try:
        cursor = conn.cursor()
        cursor.execute(insert_sql, list(serialized_data.values()))
        conn.commit()
        if cursor.rowcount > 0:
            print(f"场景 '{data.get('sceneName', '未知')}' (sceneId: {data.get('sceneId', '未知')}) 插入成功。")
            return True
        else:
            print(f"场景 '{data.get('sceneName', '未知')}' (sceneId: {data.get('sceneId', '未知')}) 已存在，未插入。")
            return False
    except sqlite3.Error as e:
        print(f"插入数据失败: {e}")
        return False
    finally:
        conn.close()

def get_scene(sceneId: int):
    """
    根据 sceneId 查询场景记录。
    返回一个字典，如果未找到则返回 None。
    """
    conn = _connect_db()
    if not conn:
        return None

    select_sql = "SELECT * FROM scene WHERE sceneId = ?"
    try:
        cursor = conn.cursor()
        cursor.execute(select_sql, (sceneId,))
        row = cursor.fetchone()
        if row:
            col_names = [description[0] for description in cursor.description]
            scene_data = dict(zip(col_names, row))
            for key, value in scene_data.items():
                # 特殊处理多选ID字段
                if key in ["semantic_classifier_ids", "score_trigger_ids", "semantic_trigger_ids"]:
                    scene_data[key] = _deserialize_id_list(value)
                else:
                    scene_data[key] = _deserialize_value(value)
            return scene_data
        else:
            return None
    except sqlite3.Error as e:
        print(f"查询数据失败: {e}")
        return None
    finally:
        conn.close()

def get_scene_id_by_name(scene_name: str,job_classification_id: str):
    conn = _connect_db()
    if not conn:
        return None
    select_sql = "SELECT * FROM scene WHERE sceneName = ? AND job_classification_id = ?"
    try:
        cursor = conn.cursor()
        cursor.execute(select_sql, (scene_name,job_classification_id))
        row = cursor.fetchone()
        if row:
            return row[0]
        else:
            return None
    except sqlite3.Error as e:
        print(f"查询数据失败: {e}")

def get_default_scene(job_classification_id):
    conn = _connect_db()
    if not conn:
        return None
    select_sql = "SELECT * FROM scene WHERE job_classification_id = ? AND isDefault = 1"
    try:
        cursor = conn.cursor()
        cursor.execute(select_sql, (job_classification_id,))
        row = cursor.fetchone()
        if row:
            col_names = [description[0] for description in cursor.description]
            scene_data = dict(zip(col_names, row))
            for key, value in scene_data.items():
                # 特殊处理多选ID字段
                if key in ["semantic_classifier_ids", "score_trigger_ids", "semantic_trigger_ids"]:
                    scene_data[key] = _deserialize_id_list(value)
                else:
                    scene_data[key] = _deserialize_value(value)
            return scene_data
        else:
            return None
    except sqlite3.Error as e:
        print(f"查询数据失败: {e}")
        return None
    finally:
        conn.close()

def get_scenes_by_position(job_classification_id):
    conn = _connect_db()
    if not conn:
        return []
    select_sql = "SELECT * FROM scene WHERE job_classification_id = ?"
    try:
        cursor = conn.cursor()
        cursor.execute(select_sql, (job_classification_id,))
        rows = cursor.fetchall()
        if rows:
            col_names = [description[0] for description in cursor.description]
            all_scenes = []
            for row in rows:
                scene_data = dict(zip(col_names, row))
                for key, value in scene_data.items():
                    # 特殊处理多选ID字段
                    if key in ["semantic_classifier_ids", "score_trigger_ids", "semantic_trigger_ids"]:
                        scene_data[key] = _deserialize_id_list(value)
                    else:
                        scene_data[key] = _deserialize_value(value)
                all_scenes.append(scene_data)
            return all_scenes
        else:
            return []
    except sqlite3.Error as e:
        print(f"查询数据失败: {e}")
        return []
    finally:
        conn.close()

def get_scenes_by_job_classification(job_classification_id):
    """
    根据职位分类ID查询场景记录。
    返回一个包含字典的列表。
    """
    conn = _connect_db()
    if not conn:
        return []
    select_sql = "SELECT * FROM scene WHERE job_classification_id = ?"
    try:
        cursor = conn.cursor()
        cursor.execute(select_sql, (job_classification_id,))
        rows = cursor.fetchall()
        if rows:
            col_names = [description[0] for description in cursor.description]
            all_scenes = []
            for row in rows:
                scene_data = dict(zip(col_names, row))
                for key, value in scene_data.items():
                    # 特殊处理多选ID字段
                    if key in ["semantic_classifier_ids", "score_trigger_ids", "semantic_trigger_ids"]:
                        scene_data[key] = _deserialize_id_list(value)
                    else:
                        scene_data[key] = _deserialize_value(value)
                all_scenes.append(scene_data)
            return all_scenes
        else:
            return []
    except sqlite3.Error as e:
        print(f"查询数据失败: {e}")
        return []
    finally:
        conn.close()

def get_all_scenes():
    """
    查询所有场景记录。
    返回一个包含字典的列表。
    """
    conn = _connect_db()
    if not conn:
        return []

    select_all_sql = "SELECT * FROM scene"
    try:
        cursor = conn.cursor()
        cursor.execute(select_all_sql)
        rows = cursor.fetchall()
        if rows:
            col_names = [description[0] for description in cursor.description]
            all_scenes = []
            for row in rows:
                scene_data = dict(zip(col_names, row))
                for key, value in scene_data.items():
                    # 特殊处理多选ID字段
                    if key in ["semantic_classifier_ids", "score_trigger_ids", "semantic_trigger_ids"]:
                        scene_data[key] = _deserialize_id_list(value)
                    else:
                        scene_data[key] = _deserialize_value(value)
                all_scenes.append(scene_data)
            return all_scenes
        else:
            return []
    except sqlite3.Error as e:
        print(f"查询所有数据失败: {e}")
        return []
    finally:
        conn.close()

def update_scene(sceneId: int, updates: dict):
    """
    根据 sceneId 更新场景记录。
    updates 字典包含要更新的字段及其新值。
    """
    conn = _connect_db()
    if not conn:
        return False

    # 处理多选字段，序列化为JSON字符串
    if "semantic_classifier_ids" in updates:
        updates["semantic_classifier_ids"] = _serialize_id_list(updates["semantic_classifier_ids"])
    if "score_trigger_ids" in updates:
        updates["score_trigger_ids"] = _serialize_id_list(updates["score_trigger_ids"])
    if "semantic_trigger_ids" in updates:
        updates["semantic_trigger_ids"] = _serialize_id_list(updates["semantic_trigger_ids"])

    serialized_updates = {k: _serialize_value(v) for k, v in updates.items()}

    set_clauses = ', '.join([f"{key} = ?" for key in serialized_updates.keys()])
    update_sql = f"UPDATE scene SET {set_clauses} WHERE sceneId = ?"

    values = list(serialized_updates.values())
    values.append(sceneId)

    try:
        cursor = conn.cursor()
        cursor.execute(update_sql, values)
        conn.commit()
        if cursor.rowcount > 0:
            print(f"场景 sceneId: {sceneId} 更新成功。")
            return True
        else:
            print(f"未找到 sceneId 为 {sceneId} 的场景，未能更新。")
            return False
    except sqlite3.Error as e:
        print(f"更新数据失败: {e}")
        return False
    finally:
        conn.close()

def delete_scene(sceneId: int):
    """
    根据 sceneId 删除场景记录。
    """
    conn = _connect_db()
    if not conn:
        return False

    delete_sql = "DELETE FROM scene WHERE sceneId = ?"
    try:
        cursor = conn.cursor()
        cursor.execute(delete_sql, (sceneId,))
        conn.commit()
        if cursor.rowcount > 0:
            print(f"场景 sceneId: {sceneId} 删除成功。")
            return True
        else:
            print(f"未找到 sceneId 为 {sceneId} 的场景，未能删除。")
            return False
    except sqlite3.Error as e:
        print(f"删除数据失败: {e}")
        return False
    finally:
        conn.close()

# Scene Question表操作
def insert_scene_question(data: dict):
    """
    插入一条新的场景问题记录。
    如果 questionId 已存在，则不插入。
    如果没有指定seq，则设置为该场景已有问题数量+1。
    """
    conn = _connect_db()
    if not conn:
        return False

    data["createdAt"] = data.get("createdAt", datetime.now().isoformat())
    data["questionId"] = str(uuid.uuid4()).replace("-", "")
    
    # 如果没有指定seq，则查询当前场景的问题数量，设置seq为最大值+1
    if "seq" not in data and "sceneId" in data:
        try:
            cursor = conn.cursor()
            cursor.execute("SELECT MAX(seq) FROM scene_question WHERE sceneId = ?", (data["sceneId"],))
            max_seq = cursor.fetchone()[0]
            data["seq"] = 1 if max_seq is None else max_seq + 1
        except sqlite3.Error as e:
            print(f"查询最大序号失败: {e}")
            data["seq"] = 1  # 如果查询失败，默认为1
    elif "seq" not in data:
        data["seq"] = 1  # 如果没有sceneId，默认为1
    
    serialized_data = {k: _serialize_value(v) for k, v in data.items()}

    columns = ', '.join(serialized_data.keys())
    placeholders = ', '.join(['?' for _ in serialized_data.keys()])
    insert_sql = f"INSERT OR IGNORE INTO scene_question ({columns}) VALUES ({placeholders})"

    try:
        cursor = conn.cursor()
        cursor.execute(insert_sql, list(serialized_data.values()))
        conn.commit()
        if cursor.rowcount > 0:
            print(f"场景问题 (questionId: {data.get('questionId', '未知')}) 插入成功。")
            return True
        else:
            print(f"场景问题 (questionId: {data.get('questionId', '未知')}) 已存在，未插入。")
            return False
    except sqlite3.Error as e:
        print(f"插入数据失败: {e}")
        return False
    finally:
        conn.close()

def get_scene_question(questionId: int):
    """
    根据 questionId 查询场景问题记录。
    返回一个字典，如果未找到则返回 None。
    """
    conn = _connect_db()
    if not conn:
        return None

    select_sql = "SELECT * FROM scene_question WHERE questionId = ?"
    try:
        cursor = conn.cursor()
        cursor.execute(select_sql, (questionId,))
        row = cursor.fetchone()
        if row:
            col_names = [description[0] for description in cursor.description]
            question_data = dict(zip(col_names, row))
            for key, value in question_data.items():
                question_data[key] = _deserialize_value(value)
            return question_data
        else:
            return None
    except sqlite3.Error as e:
        print(f"查询数据失败: {e}")
        return None
    finally:
        conn.close()

def get_questions_by_scene(sceneId: str):
    """
    根据 sceneId 查询场景问题记录。
    返回一个包含字典的列表，按seq字段排序。
    """
    conn = _connect_db()
    if not conn:
        return []

    select_sql = "SELECT * FROM scene_question WHERE sceneId = ? ORDER BY seq ASC"
    try:
        cursor = conn.cursor()
        cursor.execute(select_sql, (sceneId,))
        rows = cursor.fetchall()
        if rows:
            col_names = [description[0] for description in cursor.description]
            all_questions = []
            for row in rows:
                question_data = dict(zip(col_names, row))
                for key, value in question_data.items():
                    question_data[key] = _deserialize_value(value)
                all_questions.append(question_data)
            return all_questions
        else:
            return []
    except sqlite3.Error as e:
        print(f"查询数据失败: {e}")
        return []
    finally:
        conn.close()

def update_scene_question(questionId: int, updates: dict):
    """
    根据 questionId 更新场景问题记录。
    updates 字典包含要更新的字段及其新值。
    """
    conn = _connect_db()
    if not conn:
        return False

    serialized_updates = {k: _serialize_value(v) for k, v in updates.items()}

    set_clauses = ', '.join([f"{key} = ?" for key in serialized_updates.keys()])
    update_sql = f"UPDATE scene_question SET {set_clauses} WHERE questionId = ?"

    values = list(serialized_updates.values())
    values.append(questionId)

    try:
        cursor = conn.cursor()
        cursor.execute(update_sql, values)
        conn.commit()
        if cursor.rowcount > 0:
            print(f"场景问题 questionId: {questionId} 更新成功。")
            return True
        else:
            print(f"未找到 questionId 为 {questionId} 的场景问题，未能更新。")
            return False
    except sqlite3.Error as e:
        print(f"更新数据失败: {e}")
        return False
    finally:
        conn.close()

def delete_scene_question(questionId: int):
    """
    根据 questionId 删除场景问题记录。
    """
    conn = _connect_db()
    if not conn:
        return False

    delete_sql = "DELETE FROM scene_question WHERE questionId = ?"
    try:
        cursor = conn.cursor()
        cursor.execute(delete_sql, (questionId,))
        conn.commit()
        if cursor.rowcount > 0:
            print(f"场景问题 questionId: {questionId} 删除成功。")
            return True
        else:
            print(f"未找到 questionId 为 {questionId} 的场景问题，未能删除。")
            return False
    except sqlite3.Error as e:
        print(f"删除数据失败: {e}")
        return False
    finally:
        conn.close()

# Score表操作
def insert_score(data: dict):
    """
    插入一条新的评分记录。
    """
    conn = _connect_db()
    if not conn:
        return False

    data["createdAt"] = data.get("createdAt", datetime.now().isoformat())
    serialized_data = {k: _serialize_value(v) for k, v in data.items()}

    columns = ', '.join(serialized_data.keys())
    placeholders = ', '.join(['?' for _ in serialized_data.keys()])
    insert_sql = f"INSERT INTO score ({columns}) VALUES ({placeholders})"

    try:
        cursor = conn.cursor()
        cursor.execute(insert_sql, list(serialized_data.values()))
        conn.commit()
        print(f"评分记录插入成功。")
        return True
    except sqlite3.Error as e:
        print(f"插入数据失败: {e}")
        return False
    finally:
        conn.close()

def get_scores_by_user(userid: str):
    """
    根据 userid 查询评分记录。
    返回一个包含字典的列表。
    """
    conn = _connect_db()
    if not conn:
        return []

    select_sql = "SELECT * FROM score WHERE userid = ?"
    try:
        cursor = conn.cursor()
        cursor.execute(select_sql, (userid,))
        rows = cursor.fetchall()
        if rows:
            col_names = [description[0] for description in cursor.description]
            all_scores = []
            for row in rows:
                score_data = dict(zip(col_names, row))
                for key, value in score_data.items():
                    score_data[key] = _deserialize_value(value)
                all_scores.append(score_data)
            return all_scores
        else:
            return []
    except sqlite3.Error as e:
        print(f"查询数据失败: {e}")
        return []
    finally:
        conn.close()

def get_scores_by_scene(sceneId: int):
    """
    根据 sceneId 查询评分记录。
    返回一个包含字典的列表。
    """
    conn = _connect_db()
    if not conn:
        return []

    select_sql = "SELECT * FROM score WHERE sceneId = ?"
    try:
        cursor = conn.cursor()
        cursor.execute(select_sql, (sceneId,))
        rows = cursor.fetchall()
        if rows:
            col_names = [description[0] for description in cursor.description]
            all_scores = []
            for row in rows:
                score_data = dict(zip(col_names, row))
                for key, value in score_data.items():
                    score_data[key] = _deserialize_value(value)
                all_scores.append(score_data)
            return all_scores
        else:
            return []
    except sqlite3.Error as e:
        print(f"查询数据失败: {e}")
        return []
    finally:
        conn.close()

def update_score(id: int, updates: dict):
    """
    根据 id 更新评分记录。
    updates 字典包含要更新的字段及其新值。
    """
    conn = _connect_db()
    if not conn:
        return False

    serialized_updates = {k: _serialize_value(v) for k, v in updates.items()}

    set_clauses = ', '.join([f"{key} = ?" for key in serialized_updates.keys()])
    update_sql = f"UPDATE score SET {set_clauses} WHERE id = ?"

    values = list(serialized_updates.values())
    values.append(id)

    try:
        cursor = conn.cursor()
        cursor.execute(update_sql, values)
        conn.commit()
        if cursor.rowcount > 0:
            print(f"评分记录 id: {id} 更新成功。")
            return True
        else:
            print(f"未找到 id 为 {id} 的评分记录，未能更新。")
            return False
    except sqlite3.Error as e:
        print(f"更新数据失败: {e}")
        return False
    finally:
        conn.close()

def delete_score(id: int):
    """
    根据 id 删除评分记录。
    """
    conn = _connect_db()
    if not conn:
        return False

    delete_sql = "DELETE FROM score WHERE id = ?"
    try:
        cursor = conn.cursor()
        cursor.execute(delete_sql, (id,))
        conn.commit()
        if cursor.rowcount > 0:
            print(f"评分 id: {id} 删除成功。")
            return True
        else:
            print(f"评分 id: {id} 不存在，无法删除。")
            return False
    except sqlite3.Error as e:
        print(f"删除数据失败: {e}")
        return False
    finally:
        conn.close()

# 批量管理表操作
def insert_batch_record(data: dict):
    """
    插入一条新的批量管理记录。
    """
    conn = _connect_db()
    if not conn:
        return False

    data["createdAt"] = data.get("createdAt", datetime.now().isoformat())
    data["updatedAt"] = data.get("updatedAt", datetime.now().isoformat())
    data["id"] = str(uuid.uuid4()).replace("-", "")

    serialized_data = {k: _serialize_value(v) for k, v in data.items()}

    columns = ', '.join(serialized_data.keys())
    placeholders = ', '.join(['?' for _ in serialized_data.keys()])
    insert_sql = f"INSERT INTO batch_management ({columns}) VALUES ({placeholders})"

    try:
        cursor = conn.cursor()
        cursor.execute(insert_sql, list(serialized_data.values()))
        conn.commit()
        print(f"批量管理记录 (id: {data.get('id', '未知')}) 插入成功。")
        return True, data["id"]
    except sqlite3.Error as e:
        print(f"插入数据失败: {e}")
        return False, str(e)
    finally:
        conn.close()

def get_batch_record(record_id: str, tenant_id: str):
    """
    根据 id 查询批量管理记录。
    返回一个字典，如果未找到则返回 None。
    """
    conn = _connect_db()
    if not conn:
        return None

    if tenant_id == "":
        select_sql = "SELECT * FROM batch_management WHERE id = ?"
    else:
        select_sql = "SELECT * FROM batch_management WHERE id = ? AND tenant_id = ?"

    try:
        cursor = conn.cursor()
        if tenant_id == "":
            cursor.execute(select_sql, (record_id,))
        else:
            cursor.execute(select_sql, (record_id, tenant_id))
        row = cursor.fetchone()
        if row:
            col_names = [description[0] for description in cursor.description]
            record_data = dict(zip(col_names, row))
            for key, value in record_data.items():
                record_data[key] = _deserialize_value(value)
            return record_data
        else:
            return None
    except sqlite3.Error as e:
        print(f"查询数据失败: {e}")
        return None
    finally:
        conn.close()

def get_all_batch_records(tenant_id: str):
    """
    获取所有批量管理记录。
    返回记录列表。
    """
    conn = _connect_db()
    if not conn:
        return []

    if tenant_id == "":
        select_sql = "SELECT * FROM batch_management ORDER BY createdAt DESC"
    else:
        select_sql = "SELECT * FROM batch_management WHERE tenant_id = ? ORDER BY createdAt DESC"

    try:
        cursor = conn.cursor()
        if tenant_id == "":
            cursor.execute(select_sql)
        else:
            cursor.execute(select_sql, (tenant_id,))
        rows = cursor.fetchall()

        records = []
        if rows:
            col_names = [description[0] for description in cursor.description]
            for row in rows:
                record_data = dict(zip(col_names, row))
                for key, value in record_data.items():
                    record_data[key] = _deserialize_value(value)
                records.append(record_data)

        return records
    except sqlite3.Error as e:
        print(f"查询数据失败: {e}")
        return []
    finally:
        conn.close()

def update_batch_record(record_id: str, updates: dict, tenant_id: str):
    """
    根据 id 更新批量管理记录。
    updates 字典包含要更新的字段及其新值。
    """
    conn = _connect_db()
    if not conn:
        return False

    updates["updatedAt"] = datetime.now().isoformat()

    serialized_updates = {k: _serialize_value(v) for k, v in updates.items() if v is not None and v != "" and k in updates}

    set_clauses = ', '.join([f"{key} = ?" for key in serialized_updates.keys()])
    update_sql = f"UPDATE batch_management SET {set_clauses} WHERE id = ? AND tenant_id = ?"

    values = list(serialized_updates.values())
    values.append(record_id)
    values.append(tenant_id)

    try:
        cursor = conn.cursor()
        cursor.execute(update_sql, values)
        conn.commit()
        if cursor.rowcount > 0:
            print(f"批量管理记录 id: {record_id} 更新成功。")
            return True
        else:
            print(f"未找到 id 为 {record_id} 的批量管理记录，未能更新。")
            return False
    except sqlite3.Error as e:
        print(f"更新数据失败: {e}")
        return False
    finally:
        conn.close()

def delete_batch_record(record_id: str, tenant_id: str):
    """
    根据 id 删除批量管理记录。
    """
    conn = _connect_db()
    if not conn:
        return False

    delete_sql = "DELETE FROM batch_management WHERE id = ? AND tenant_id = ?"
    try:
        cursor = conn.cursor()
        cursor.execute(delete_sql, (record_id, tenant_id))
        conn.commit()
        if cursor.rowcount > 0:
            print(f"批量管理记录 id: {record_id} 删除成功。")
            return True
        else:
            print(f"未找到 id 为 {record_id} 的批量管理记录，未能删除。")
            return False
    except sqlite3.Error as e:
        print(f"删除数据失败: {e}")
        return False
    finally:
        conn.close()

