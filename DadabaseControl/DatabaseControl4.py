import sqlite3
import uuid
import json
from datetime import datetime
from DadabaseControl.DatabaseControl import DB_NAME,_connect_db,_serialize_value,_deserialize_value


def init_analysis_tables():
    """初始化定时分析相关的数据库表"""
    conn = _connect_db()
    if conn:
        cursor = conn.cursor()
        try:
            # 创建定时分析配置表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS scheduled_analysis (
                    id TEXT PRIMARY KEY,
                    tenant_id TEXT NOT NULL,
                    name TEXT NOT NULL,
                    prompt TEXT NOT NULL,
                    schedule_time TEXT NOT NULL,
                    time_range_days INTEGER DEFAULT 1,
                    is_auto_enabled INTEGER DEFAULT 1,
                    email_recipients TEXT,
                    job_classification_id TEXT,
                    last_execution_time TEXT,
                    next_execution_time TEXT,
                    status TEXT DEFAULT 'active',
                    createdAt TEXT,
                    updatedAt TEXT,
                    FOREIGN KEY (tenant_id) REFERENCES tenant07082(id),
                    FOREIGN KEY (job_classification_id) REFERENCES job_classification(id)
                );
            """)

            # 创建QA引擎表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS qa_engine (
                    id TEXT PRIMARY KEY,
                    job_classification_id TEXT NOT NULL,
                    question TEXT NOT NULL,
                    answer TEXT NOT NULL,
                    frequency INTEGER DEFAULT 0,
                    source TEXT DEFAULT 'manual',
                    createdAt TEXT,
                    updatedAt TEXT,
                    FOREIGN KEY (job_classification_id) REFERENCES job_classification(id)
                );
            """)

            # 创建知识库文本表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS knowledge_base (
                    id TEXT PRIMARY KEY,
                    job_classification_id TEXT NOT NULL,
                    content TEXT NOT NULL,
                    processed_qa_count INTEGER DEFAULT 0,
                    createdAt TEXT,
                    FOREIGN KEY (job_classification_id) REFERENCES job_classification(id)
                );
            """)

            # 创建分析结果表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS analysis_results (
                    id TEXT PRIMARY KEY,
                    analysis_id TEXT NOT NULL,
                    tenant_id TEXT NOT NULL,
                    user_id TEXT NOT NULL,
                    user_name TEXT,
                    user_phone TEXT,
                    message_count INTEGER DEFAULT 0,
                    analysis_content TEXT,
                    analysis_summary TEXT,
                    execution_time TEXT,
                    createdAt TEXT,
                    FOREIGN KEY (analysis_id) REFERENCES scheduled_analysis(id),
                    FOREIGN KEY (tenant_id) REFERENCES tenant07082(id),
                    FOREIGN KEY (user_id) REFERENCES user(userid)
                );
            """)

            # 创建分析执行日志表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS analysis_execution_log (
                    id TEXT PRIMARY KEY,
                    analysis_id TEXT NOT NULL,
                    tenant_id TEXT NOT NULL,
                    execution_time TEXT,
                    status TEXT,
                    total_users INTEGER DEFAULT 0,
                    successful_analyses INTEGER DEFAULT 0,
                    failed_analyses INTEGER DEFAULT 0,
                    error_message TEXT,
                    email_sent INTEGER DEFAULT 0,
                    createdAt TEXT,
                    FOREIGN KEY (analysis_id) REFERENCES scheduled_analysis(id),
                    FOREIGN KEY (tenant_id) REFERENCES tenant07082(id)
                );
            """)

            # 更新租户全局配置表，添加邮件配置字段
            cursor.execute("""
                ALTER TABLE tenant_global_settings ADD COLUMN smtp_server TEXT;
            """)
            cursor.execute("""
                ALTER TABLE tenant_global_settings ADD COLUMN smtp_port INTEGER DEFAULT 587;
            """)
            cursor.execute("""
                ALTER TABLE tenant_global_settings ADD COLUMN smtp_username TEXT;
            """)
            cursor.execute("""
                ALTER TABLE tenant_global_settings ADD COLUMN smtp_password TEXT;
            """)
            cursor.execute("""
                ALTER TABLE tenant_global_settings ADD COLUMN smtp_use_tls INTEGER DEFAULT 1;
            """)

            # 检查并添加job_classification_id字段（用于数据库迁移）
            try:
                cursor.execute("PRAGMA table_info(scheduled_analysis)")
                columns = [column[1] for column in cursor.fetchall()]
                if 'job_classification_id' not in columns:
                    cursor.execute("ALTER TABLE scheduled_analysis ADD COLUMN job_classification_id TEXT")
                    print("已为scheduled_analysis表添加job_classification_id字段")
            except sqlite3.Error as migration_error:
                if "duplicate column name" not in str(migration_error).lower():
                    print(f"添加job_classification_id字段失败: {migration_error}")

            conn.commit()
            print("定时分析相关表已创建或已存在。")
        except sqlite3.Error as e:
            # 如果是列已存在的错误，忽略它
            if "duplicate column name" not in str(e).lower():
                print(f"创建定时分析表失败: {e}")
        finally:
            conn.close()


def get_batch_management_by_city_job_category(city,job_category,tenant_id):
    """
    根据城市和职位分类获取批量管理数据,返回字典
    """
    conn = _connect_db()
    if conn:
        cursor = conn.cursor()
        if tenant_id == "": 
            cursor.execute("SELECT * FROM batch_management WHERE city = ? AND job_category = ?", (city,job_category))
        else:
            cursor.execute("SELECT * FROM batch_management WHERE city = ? AND job_category = ? AND tenant_id = ?", (city,job_category,tenant_id))
        result = cursor.fetchone()
        if result:
            return {
                'id':result[0],
                'city':result[1],
                'job_category':result[2],
                'work_content':result[3],
                'recruitment_requirements':result[4],
                'work_time':result[5],
                'salary_benefits':result[6],
                'interview_time':result[7],
                'training_time':result[8],
                'recruitment_area':result[9],
                'interview_address':result[10],
                'remarks':result[11],
                'tenant_id':result[12],
                'createdAt':result[13],
                'updatedAt':result[14]
            }
        else:
            return {}
    return {}


# ======================== 定时分析相关函数 ========================

def create_scheduled_analysis(tenant_id: str, name: str, prompt: str, schedule_time: str,
                            time_range_days: int = 1, is_auto_enabled: bool = True,
                            email_recipients: str = "", job_classification_id: str = ""):
    """创建定时分析配置"""
    conn = _connect_db()
    if conn:
        try:
            cursor = conn.cursor()
            analysis_id = str(uuid.uuid4())
            created_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            cursor.execute("""
                INSERT INTO scheduled_analysis
                (id, tenant_id, name, prompt, schedule_time, time_range_days,
                 is_auto_enabled, email_recipients, job_classification_id, status, createdAt, updatedAt)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', ?, ?)
            """, (analysis_id, tenant_id, name, prompt, schedule_time, time_range_days,
                  1 if is_auto_enabled else 0, email_recipients, job_classification_id, created_at, created_at))

            conn.commit()
            return True, analysis_id
        except sqlite3.Error as e:
            print(f"创建定时分析配置失败: {e}")
            return False, str(e)
        finally:
            conn.close()
    return False, "数据库连接失败"


def update_scheduled_analysis(analysis_id: str, tenant_id: str, **kwargs):
    """更新定时分析配置"""
    conn = _connect_db()
    if conn:
        try:
            cursor = conn.cursor()

            # 构建更新语句
            update_fields = []
            values = []

            allowed_fields = ['name', 'prompt', 'schedule_time', 'time_range_days',
                            'is_auto_enabled', 'email_recipients', 'job_classification_id', 'status',
                            'last_execution_time', 'next_execution_time']

            for field, value in kwargs.items():
                if field in allowed_fields:
                    update_fields.append(f"{field} = ?")
                    if field == 'is_auto_enabled':
                        values.append(1 if value else 0)
                    else:
                        values.append(value)

            if update_fields:
                update_fields.append("updatedAt = ?")
                values.append(datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
                values.extend([analysis_id, tenant_id])

                sql = f"UPDATE scheduled_analysis SET {', '.join(update_fields)} WHERE id = ? AND tenant_id = ?"
                cursor.execute(sql, values)
                conn.commit()
                return True, "更新成功"
            else:
                return False, "没有有效的更新字段"

        except sqlite3.Error as e:
            print(f"更新定时分析配置失败: {e}")
            return False, str(e)
        finally:
            conn.close()
    return False, "数据库连接失败"


def get_scheduled_analysis_list(tenant_id: str):
    """获取租户的定时分析配置列表，如果tenant_id为空则获取所有租户的数据"""
    conn = _connect_db()
    if conn:
        try:
            cursor = conn.cursor()
            if tenant_id == "":
                # 管理员查看所有数据
                cursor.execute("""
                    SELECT * FROM scheduled_analysis
                    WHERE status != 'deleted'
                    ORDER BY createdAt DESC
                """)
            else:
                # 租户查看自己的数据
                cursor.execute("""
                    SELECT * FROM scheduled_analysis
                    WHERE tenant_id = ? AND status != 'deleted'
                    ORDER BY createdAt DESC
                """, (tenant_id,))

            rows = cursor.fetchall()
            if rows:
                col_names = [description[0] for description in cursor.description]
                analyses = []
                for row in rows:
                    analysis_data = dict(zip(col_names, row))
                    analysis_data['is_auto_enabled'] = bool(analysis_data['is_auto_enabled'])
                    analyses.append(analysis_data)
                return analyses
            else:
                return []
        except sqlite3.Error as e:
            print(f"获取定时分析列表失败: {e}")
            return []
        finally:
            conn.close()
    return []


def get_scheduled_analysis(analysis_id: str, tenant_id: str):
    """获取单个定时分析配置，如果tenant_id为空则不限制租户"""
    conn = _connect_db()
    if conn:
        try:
            cursor = conn.cursor()
            if tenant_id == "":
                # 管理员查看，不限制租户
                cursor.execute("""
                    SELECT * FROM scheduled_analysis
                    WHERE id = ? AND status != 'deleted'
                """, (analysis_id,))
            else:
                # 租户查看，限制租户
                cursor.execute("""
                    SELECT * FROM scheduled_analysis
                    WHERE id = ? AND tenant_id = ? AND status != 'deleted'
                """, (analysis_id, tenant_id))

            row = cursor.fetchone()
            if row:
                col_names = [description[0] for description in cursor.description]
                analysis_data = dict(zip(col_names, row))
                analysis_data['is_auto_enabled'] = bool(analysis_data['is_auto_enabled'])
                return analysis_data
            else:
                return None
        except sqlite3.Error as e:
            print(f"获取定时分析配置失败: {e}")
            return None
        finally:
            conn.close()
    return None

def get_scheduled_analysis_by_job_classification(job_classification_id: str, tenant_id: str):
    """根据职位分类ID获取定时分析配置列表"""
    conn = _connect_db()
    if conn:
        try:
            cursor = conn.cursor()
            if tenant_id == "":
                # 管理员查看，不限制租户
                cursor.execute("""
                    SELECT * FROM scheduled_analysis
                    WHERE job_classification_id = ? AND status != 'deleted'
                    ORDER BY createdAt DESC
                """, (job_classification_id,))
            else:
                # 租户查看，限制租户
                cursor.execute("""
                    SELECT * FROM scheduled_analysis
                    WHERE job_classification_id = ? AND tenant_id = ? AND status != 'deleted'
                    ORDER BY createdAt DESC
                """, (job_classification_id, tenant_id))

            rows = cursor.fetchall()
            if rows:
                col_names = [description[0] for description in cursor.description]
                analyses = []
                for row in rows:
                    analysis_data = dict(zip(col_names, row))
                    analysis_data['is_auto_enabled'] = bool(analysis_data['is_auto_enabled'])
                    analyses.append(analysis_data)
                return analyses
            else:
                return []
        except sqlite3.Error as e:
            print(f"获取职位分类定时分析配置失败: {e}")
            return []
        finally:
            conn.close()
    return []


def delete_scheduled_analysis(analysis_id: str, tenant_id: str):
    """删除定时分析配置（软删除）"""
    conn = _connect_db()
    if conn:
        try:
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE scheduled_analysis
                SET status = 'deleted', updatedAt = ?
                WHERE id = ? AND tenant_id = ?
            """, (datetime.now().strftime("%Y-%m-%d %H:%M:%S"), analysis_id, tenant_id))

            conn.commit()
            return True, "删除成功"
        except sqlite3.Error as e:
            print(f"删除定时分析配置失败: {e}")
            return False, str(e)
        finally:
            conn.close()
    return False, "数据库连接失败"


def save_analysis_result(analysis_id: str, tenant_id: str, user_id: str,
                        user_name: str, user_phone: str, message_count: int,
                        analysis_content: str, analysis_summary: str):
    """保存分析结果"""
    conn = _connect_db()
    if conn:
        try:
            cursor = conn.cursor()
            result_id = str(uuid.uuid4())
            created_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            cursor.execute("""
                INSERT INTO analysis_results
                (id, analysis_id, tenant_id, user_id, user_name, user_phone,
                 message_count, analysis_content, analysis_summary, execution_time, createdAt)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (result_id, analysis_id, tenant_id, user_id, user_name, user_phone,
                  message_count, analysis_content, analysis_summary, created_at, created_at))

            conn.commit()
            return True, result_id
        except sqlite3.Error as e:
            print(f"保存分析结果失败: {e}")
            return False, str(e)
        finally:
            conn.close()
    return False, "数据库连接失败"


def get_analysis_results(analysis_id: str, tenant_id: str, limit: int = 100):
    """获取分析结果列表"""
    conn = _connect_db()
    if conn:
        try:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT * FROM analysis_results
                WHERE analysis_id = ? AND tenant_id = ?
                ORDER BY createdAt DESC
                LIMIT ?
            """, (analysis_id, tenant_id, limit))

            rows = cursor.fetchall()
            if rows:
                col_names = [description[0] for description in cursor.description]
                results = []
                for row in rows:
                    result_data = dict(zip(col_names, row))
                    results.append(result_data)
                return results
            else:
                return []
        except sqlite3.Error as e:
            print(f"获取分析结果失败: {e}")
            return []
        finally:
            conn.close()
    return []


def get_last_analysis_summary(analysis_id: str, tenant_id: str):
    """获取上一次分析的总结"""
    conn = _connect_db()
    if conn:
        try:
            cursor = conn.cursor()
            # 获取最近一次执行的分析总结
            cursor.execute("""
                SELECT analysis_summary FROM analysis_results
                WHERE analysis_id = ? AND tenant_id = ?
                AND analysis_summary IS NOT NULL AND analysis_summary != ''
                ORDER BY createdAt DESC
                LIMIT 1
            """, (analysis_id, tenant_id))

            row = cursor.fetchone()
            if row:
                return row[0]
            else:
                return None
        except sqlite3.Error as e:
            print(f"获取上一次分析总结失败: {e}")
            return None
        finally:
            conn.close()
    return None


def create_analysis_execution_log(analysis_id: str, tenant_id: str):
    """创建分析执行日志记录"""
    conn = _connect_db()
    if conn:
        try:
            cursor = conn.cursor()
            log_id = str(uuid.uuid4())
            created_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            cursor.execute("""
                INSERT INTO analysis_execution_log
                (id, analysis_id, tenant_id, execution_time, status, total_users,
                 successful_analyses, failed_analyses, error_message, email_sent, createdAt)
                VALUES (?, ?, ?, ?, 'running', 0, 0, 0, '', 0, ?)
            """, (log_id, analysis_id, tenant_id, created_at, created_at))

            conn.commit()
            return True, log_id
        except sqlite3.Error as e:
            print(f"创建分析执行日志失败: {e}")
            return False, str(e)
        finally:
            conn.close()
    return False, "数据库连接失败"


def update_analysis_execution_log(log_id: str, status: str,
                                 total_users: int = None, successful_analyses: int = None,
                                 failed_analyses: int = None, error_message: str = None,
                                 email_sent: bool = None):
    """更新分析执行日志"""
    conn = _connect_db()
    if conn:
        try:
            cursor = conn.cursor()

            # 构建更新语句
            update_fields = ["status = ?"]
            values = [status]

            if total_users is not None:
                update_fields.append("total_users = ?")
                values.append(total_users)

            if successful_analyses is not None:
                update_fields.append("successful_analyses = ?")
                values.append(successful_analyses)

            if failed_analyses is not None:
                update_fields.append("failed_analyses = ?")
                values.append(failed_analyses)

            if error_message is not None:
                update_fields.append("error_message = ?")
                values.append(error_message)

            if email_sent is not None:
                update_fields.append("email_sent = ?")
                values.append(1 if email_sent else 0)

            values.append(log_id)

            sql = f"UPDATE analysis_execution_log SET {', '.join(update_fields)} WHERE id = ?"
            cursor.execute(sql, values)

            conn.commit()
            return True, "更新成功"
        except sqlite3.Error as e:
            print(f"更新分析执行日志失败: {e}")
            return False, str(e)
        finally:
            conn.close()
    return False, "数据库连接失败"


def log_analysis_execution(analysis_id: str, tenant_id: str, status: str,
                          total_users: int = 0, successful_analyses: int = 0,
                          failed_analyses: int = 0, error_message: str = "",
                          email_sent: bool = False):
    """记录分析执行日志（兼容性函数，建议使用新的create/update函数）"""
    conn = _connect_db()
    if conn:
        try:
            cursor = conn.cursor()
            log_id = str(uuid.uuid4())
            created_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            cursor.execute("""
                INSERT INTO analysis_execution_log
                (id, analysis_id, tenant_id, execution_time, status, total_users,
                 successful_analyses, failed_analyses, error_message, email_sent, createdAt)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (log_id, analysis_id, tenant_id, created_at, status, total_users,
                  successful_analyses, failed_analyses, error_message,
                  1 if email_sent else 0, created_at))

            conn.commit()
            return True, log_id
        except sqlite3.Error as e:
            print(f"记录分析执行日志失败: {e}")
            return False, str(e)
        finally:
            conn.close()
    return False, "数据库连接失败"


def get_analysis_execution_logs(analysis_id: str, tenant_id: str, limit: int = 50):
    """获取分析执行日志，如果tenant_id为空则不限制租户"""
    conn = _connect_db()
    if conn:
        try:
            cursor = conn.cursor()
            if tenant_id == "":
                # 管理员查看，不限制租户
                cursor.execute("""
                    SELECT * FROM analysis_execution_log
                    WHERE analysis_id = ?
                    ORDER BY createdAt DESC
                    LIMIT ?
                """, (analysis_id, limit))
            else:
                # 租户查看，限制租户
                cursor.execute("""
                    SELECT * FROM analysis_execution_log
                    WHERE analysis_id = ? AND tenant_id = ?
                    ORDER BY createdAt DESC
                    LIMIT ?
                """, (analysis_id, tenant_id, limit))

            rows = cursor.fetchall()
            if rows:
                col_names = [description[0] for description in cursor.description]
                logs = []
                for row in rows:
                    log_data = dict(zip(col_names, row))
                    log_data['email_sent'] = bool(log_data['email_sent'])
                    logs.append(log_data)
                return logs
            else:
                return []
        except sqlite3.Error as e:
            print(f"获取分析执行日志失败: {e}")
            return []
        finally:
            conn.close()
    return []


def get_users_for_analysis(tenant_id: str, days: int = 1, job_classification_id: str = ""):
    """获取指定天数内有聊天记录的用户列表，可按职位分类筛选"""
    conn = _connect_db()
    if conn:
        try:
            cursor = conn.cursor()
            # 计算时间范围 - 使用时间戳
            from datetime import datetime, timedelta
            import time

            end_timestamp = int(time.time())
            start_timestamp = end_timestamp - (days * 24 * 60 * 60)

            print(f"查询时间范围: {start_timestamp} - {end_timestamp} (最近{days}天)")

            # 先查询chatlog表中的数据
            cursor.execute("""
                SELECT userid, COUNT(*) as message_count
                FROM chatlog
                WHERE createdAt >= ? AND createdAt <= ?
                GROUP BY userid
                HAVING COUNT(*) > 0
                ORDER BY message_count DESC
            """, (str(start_timestamp), str(end_timestamp)))

            chat_users = cursor.fetchall()
            print(f"找到有聊天记录的用户数: {len(chat_users)}")

            if not chat_users:
                return []

            # 获取这些用户的详细信息
            user_ids = [row[0] for row in chat_users]
            user_counts = {row[0]: row[1] for row in chat_users}

            # 查询用户详细信息，如果指定了职位分类，则需要关联position表进行筛选
            placeholders = ','.join(['?' for _ in user_ids])

            if job_classification_id:
                # 如果指定了职位分类，需要关联position表筛选
                print(f"按职位分类筛选用户: {job_classification_id}")
                cursor.execute(f"""
                    SELECT u.userid, u.real_name, u.name, u.phone, u.tenant_id
                    FROM user u
                    INNER JOIN position p ON u.positionId = p.dataId
                    WHERE u.userid IN ({placeholders}) AND u.tenant_id = ? AND p.job_classification_id = ?
                """, user_ids + [tenant_id, job_classification_id])

                user_rows = cursor.fetchall()

                # 如果没有找到，尝试查找所有匹配的用户ID（忽略tenant_id）
                if not user_rows:
                    print(f"精确匹配tenant_id {tenant_id} 和职位分类 {job_classification_id} 没有找到用户，尝试查找所有匹配的用户ID")
                    cursor.execute(f"""
                        SELECT u.userid, u.real_name, u.name, u.phone, u.tenant_id
                        FROM user u
                        INNER JOIN position p ON u.positionId = p.dataId
                        WHERE u.userid IN ({placeholders}) AND p.job_classification_id = ?
                    """, user_ids + [job_classification_id])

                    user_rows = cursor.fetchall()
            else:
                # 如果没有指定职位分类，使用原来的逻辑
                cursor.execute(f"""
                    SELECT userid, real_name, name, phone, tenant_id
                    FROM user
                    WHERE userid IN ({placeholders}) AND tenant_id = ?
                """, user_ids + [tenant_id])

                user_rows = cursor.fetchall()

                # 如果没有找到，尝试查找所有匹配的用户ID（忽略tenant_id）
                if not user_rows:
                    print(f"精确匹配tenant_id {tenant_id} 没有找到用户，尝试查找所有匹配的用户ID")
                    cursor.execute(f"""
                        SELECT userid, real_name, name, phone, tenant_id
                        FROM user
                        WHERE userid IN ({placeholders})
                    """, user_ids)

                    user_rows = cursor.fetchall()

            print(f"找到用户详细信息数: {len(user_rows)}")

            users = []
            for row in user_rows:
                user_data = {
                    'userid': row[0],
                    'real_name': row[1],
                    'name': row[2],
                    'phone': row[3],
                    'tenant_id': row[4],
                    'message_count': user_counts.get(row[0], 0)
                }
                users.append(user_data)
                print(f"用户: {user_data['real_name'] or user_data['name']} (tenant: {user_data['tenant_id']}) - 消息数: {user_data['message_count']}")

            return users

        except sqlite3.Error as e:
            print(f"获取用户列表失败: {e}")
            import traceback
            traceback.print_exc()
            return []
        finally:
            conn.close()
    return []


# ======================== 邮件配置相关函数 ========================

def update_tenant_email_settings(tenant_id: str, smtp_server: str, smtp_port: int,
                                smtp_username: str, smtp_password: str, smtp_use_tls: bool = True):
    """更新租户邮件配置"""
    from DadabaseControl.DatabaseControl3 import get_tenant_global_settings, update_tenant_global_settings

    # 获取现有设置
    current_settings = get_tenant_global_settings(tenant_id)
    if not current_settings:
        current_settings = {}

    # 更新邮件配置
    email_config = {
        'smtp_server': smtp_server,
        'smtp_port': smtp_port,
        'smtp_username': smtp_username,
        'smtp_password': smtp_password,
        'smtp_use_tls': smtp_use_tls
    }

    # 合并配置
    current_settings.update(email_config)

    # 保存配置
    success, message = update_tenant_global_settings(tenant_id, current_settings)
    return success, message


def get_tenant_email_settings(tenant_id: str):
    """获取租户邮件配置"""
    from DadabaseControl.DatabaseControl3 import get_tenant_global_settings

    settings = get_tenant_global_settings(tenant_id)
    if settings:
        return {
            'smtp_server': settings.get('smtp_server', ''),
            'smtp_port': settings.get('smtp_port', 587),
            'smtp_username': settings.get('smtp_username', ''),
            'smtp_password': settings.get('smtp_password', ''),
            'smtp_use_tls': settings.get('smtp_use_tls', True)
        }
    return {
        'smtp_server': '',
        'smtp_port': 587,
        'smtp_username': '',
        'smtp_password': '',
        'smtp_use_tls': True
    }


# ======================== QA引擎相关函数 ========================

def create_qa_item(job_classification_id: str, question: str, answer: str, source: str = 'manual'):
    """创建QA项"""
    conn = _connect_db()
    if conn:
        try:
            cursor = conn.cursor()
            qa_id = str(uuid.uuid4())
            created_at = datetime.now().isoformat()

            cursor.execute("""
                INSERT INTO qa_engine
                (id, job_classification_id, question, answer, frequency, source, createdAt, updatedAt)
                VALUES (?, ?, ?, ?, 0, ?, ?, ?)
            """, (qa_id, job_classification_id, question, answer, source, created_at, created_at))

            conn.commit()
            return qa_id
        except sqlite3.Error as e:
            print(f"创建QA项失败: {e}")
            return None
        finally:
            conn.close()
    return None


def get_qa_list_by_job_classification(job_classification_id: str):
    """根据岗位分类ID获取QA列表"""
    conn = _connect_db()
    if conn:
        try:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT id, question, answer, frequency, source, createdAt, updatedAt
                FROM qa_engine
                WHERE job_classification_id = ?
                ORDER BY createdAt DESC
            """, (job_classification_id,))

            rows = cursor.fetchall()
            qa_list = []
            for row in rows:
                qa_list.append({
                    'id': row[0],
                    'question': row[1],
                    'answer': row[2],
                    'frequency': row[3],
                    'source': row[4],
                    'createdAt': row[5],
                    'updatedAt': row[6]
                })
            return qa_list
        except sqlite3.Error as e:
            print(f"获取QA列表失败: {e}")
            return []
        finally:
            conn.close()
    return []


def update_qa_item(qa_id: str, question: str = None, answer: str = None):
    """更新QA项"""
    conn = _connect_db()
    if conn:
        try:
            cursor = conn.cursor()
            updated_at = datetime.now().isoformat()

            # 构建动态更新语句
            update_fields = []
            params = []

            if question is not None:
                update_fields.append("question = ?")
                params.append(question)

            if answer is not None:
                update_fields.append("answer = ?")
                params.append(answer)

            if update_fields:
                update_fields.append("updatedAt = ?")
                params.append(updated_at)
                params.append(qa_id)

                sql = f"UPDATE qa_engine SET {', '.join(update_fields)} WHERE id = ?"
                cursor.execute(sql, params)
                conn.commit()
                return cursor.rowcount > 0

            return False
        except sqlite3.Error as e:
            print(f"更新QA项失败: {e}")
            return False
        finally:
            conn.close()
    return False


def delete_qa_item(qa_id: str):
    """删除QA项"""
    conn = _connect_db()
    if conn:
        try:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM qa_engine WHERE id = ?", (qa_id,))
            conn.commit()
            return cursor.rowcount > 0
        except sqlite3.Error as e:
            print(f"删除QA项失败: {e}")
            return False
        finally:
            conn.close()
    return False


def increment_qa_frequency(qa_id: str):
    """增加QA项的频次"""
    conn = _connect_db()
    if conn:
        try:
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE qa_engine
                SET frequency = frequency + 1, updatedAt = ?
                WHERE id = ?
            """, (datetime.now().isoformat(), qa_id))
            conn.commit()
            return cursor.rowcount > 0
        except sqlite3.Error as e:
            print(f"更新QA频次失败: {e}")
            return False
        finally:
            conn.close()
    return False


def create_knowledge_base(job_classification_id: str, content: str):
    """创建知识库文本记录"""
    conn = _connect_db()
    if conn:
        try:
            cursor = conn.cursor()
            kb_id = str(uuid.uuid4())
            created_at = datetime.now().isoformat()

            cursor.execute("""
                INSERT INTO knowledge_base
                (id, job_classification_id, content, processed_qa_count, createdAt)
                VALUES (?, ?, ?, 0, ?)
            """, (kb_id, job_classification_id, content, created_at))

            conn.commit()
            return kb_id
        except sqlite3.Error as e:
            print(f"创建知识库记录失败: {e}")
            return None
        finally:
            conn.close()
    return None


def get_knowledge_base_list(job_classification_id: str):
    """获取知识库文本列表"""
    conn = _connect_db()
    if conn:
        try:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT id, content, processed_qa_count, createdAt
                FROM knowledge_base
                WHERE job_classification_id = ?
                ORDER BY createdAt DESC
            """, (job_classification_id,))

            rows = cursor.fetchall()
            kb_list = []
            for row in rows:
                kb_list.append({
                    'id': row[0],
                    'content': row[1],
                    'processed_qa_count': row[2],
                    'createdAt': row[3]
                })
            return kb_list
        except sqlite3.Error as e:
            print(f"获取知识库列表失败: {e}")
            return []
        finally:
            conn.close()
    return []


def update_knowledge_base_qa_count(kb_id: str, qa_count: int):
    """更新知识库的QA处理数量"""
    conn = _connect_db()
    if conn:
        try:
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE knowledge_base
                SET processed_qa_count = ?
                WHERE id = ?
            """, (qa_count, kb_id))
            conn.commit()
            return cursor.rowcount > 0
        except sqlite3.Error as e:
            print(f"更新知识库QA数量失败: {e}")
            return False
        finally:
            conn.close()
    return False


def batch_create_qa_items(job_classification_id: str, qa_pairs: list, source: str = 'ai_split'):
    """批量创建QA项"""
    conn = _connect_db()
    if conn:
        try:
            cursor = conn.cursor()
            created_ids = []
            created_at = datetime.now().isoformat()

            for qa_pair in qa_pairs:
                qa_id = str(uuid.uuid4())
                cursor.execute("""
                    INSERT INTO qa_engine
                    (id, job_classification_id, question, answer, frequency, source, createdAt, updatedAt)
                    VALUES (?, ?, ?, ?, 0, ?, ?, ?)
                """, (qa_id, job_classification_id, qa_pair['question'], qa_pair['answer'],
                      source, created_at, created_at))
                created_ids.append(qa_id)

            conn.commit()
            return created_ids
        except sqlite3.Error as e:
            print(f"批量创建QA项失败: {e}")
            return []
        finally:
            conn.close()
    return []