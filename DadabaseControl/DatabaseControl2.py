import sqlite3
import json
from datetime import datetime, timedelta
import uuid
import hashlib
import random
import string


DB_NAME = "sqlite3.db"

def _connect_db():
    """建立数据库连接"""
    try:
        conn = sqlite3.connect(DB_NAME)
        return conn
    except sqlite3.Error as e:
        print(f"数据库连接失败: {e}")
        return None

def _serialize_value(value):
    """将列表或字典序列化为 JSON 字符串，否则直接返回"""
    if isinstance(value, (list, dict)):
        return json.dumps(value, ensure_ascii=False)
    return value

def _deserialize_value(value):
    """尝试将字符串反序列化为 JSON，如果失败则返回原字符串"""
    if isinstance(value, str):
        try:
            return json.loads(value)
        except json.JSONDecodeError:
            pass
    return value

def _serialize_id_list(id_list):
    """
    将ID列表序列化为JSON字符串
    """
    if not id_list:
        return None
    
    # 确保所有值都是字符串
    id_list = [str(id) for id in id_list if id]
    
    # 如果列表为空，返回None
    if not id_list:
        return None
        
    # 序列化为JSON字符串
    return json.dumps(id_list)

def _deserialize_id_list(json_str):
    """
    将JSON字符串反序列化为ID列表
    """
    if not json_str:
        return []
    
    try:
        id_list = json.loads(json_str)
        # 确保结果是列表
        if not isinstance(id_list, list):
            return []
        return id_list
    except:
        return []

# 职位分类表的CRUD操作
def insert_job_classification(data: dict):
    """
    插入一条新的职位分类记录。
    如果 id 已存在，则不插入。
    """
    conn = _connect_db()
    if not conn:
        return False

    # 如果没有提供id，自动生成一个UUID
    if "id" not in data:
        data["id"] = str(uuid.uuid4())
    data["createdAt"] = data.get("createdAt", datetime.now().isoformat())

    columns = ', '.join(data.keys())
    placeholders = ', '.join(['?' for _ in data.keys()])
    insert_sql = f"INSERT OR IGNORE INTO job_classification ({columns}) VALUES ({placeholders})"

    try:
        cursor = conn.cursor()
        cursor.execute(insert_sql, list(data.values()))
        conn.commit()
        if cursor.rowcount > 0:
            print(f"职位分类 '{data.get('class_name', '未知')}' (id: {data.get('id', '未知')}) 插入成功。")
            return data["id"]
        else:
            print(f"职位分类 '{data.get('class_name', '未知')}' (id: {data.get('id', '未知')}) 已存在，未插入。")
            return None
    except sqlite3.Error as e:
        print(f"插入数据失败: {e}")
        return None
    finally:
        conn.close()

def get_job_classification(id: str,tenant_id: str):
    """
    根据 id 查询职位分类记录。
    返回一个字典，如果未找到则返回 None。
    """
    conn = _connect_db()
    if not conn:
        return None

    if tenant_id == "":
        select_sql = "SELECT * FROM job_classification WHERE id = ?"
    else:
        select_sql = "SELECT * FROM job_classification WHERE id = ? AND tenant_id = ?"
    try:
        cursor = conn.cursor()
        if tenant_id == "":
            cursor.execute(select_sql, (id,))
        else:
            cursor.execute(select_sql, (id,tenant_id))
        row = cursor.fetchone()
        if row:
            col_names = [description[0] for description in cursor.description]
            job_classification_data = dict(zip(col_names, row))
            return job_classification_data
        else:
            return None
    except sqlite3.Error as e:
        print(f"查询数据失败: {e}")
        return None
    finally:
        conn.close()

def get_all_job_classifications(tenant_id: str):
    """
    查询所有职位分类记录。
    返回一个包含字典的列表。
    """
    conn = _connect_db()
    if not conn:
        return []
    if tenant_id == "":
        select_all_sql = "SELECT * FROM job_classification"
    else:
        select_all_sql = "SELECT * FROM job_classification WHERE tenant_id = ?"
    try:
        cursor = conn.cursor()
        if tenant_id == "":
            cursor.execute(select_all_sql)
        else:
            cursor.execute(select_all_sql, (tenant_id,))
        rows = cursor.fetchall()
        if rows:
            col_names = [description[0] for description in cursor.description]
            all_job_classifications = []
            for row in rows:
                job_classification_data = dict(zip(col_names, row))
                all_job_classifications.append(job_classification_data)
            return all_job_classifications
        else:
            return []
    except sqlite3.Error as e:
        print(f"查询所有数据失败: {e}")
        return []
    finally:
        conn.close()

def update_job_classification(id: str, updates: dict,tenant_id: str):
    """
    根据 id 更新职位分类记录。
    updates 字典包含要更新的字段及其新值。
    """
    conn = _connect_db()
    if not conn:
        return False

    # 构建UPDATE语句
    set_clause = ', '.join([f"{k} = ?" for k in updates.keys()])
    update_sql = f"UPDATE job_classification SET {set_clause} WHERE id = ? AND tenant_id = ?"
    params = list(updates.values()) + [id,tenant_id]

    try:
        cursor = conn.cursor()
        cursor.execute(update_sql, params)
        conn.commit()
        if cursor.rowcount > 0:
            print(f"职位分类 id: {id} 更新成功。")
            return True
        else:
            print(f"职位分类 id: {id} 不存在或无变更，未更新。")
            return False
    except sqlite3.Error as e:
        print(f"更新数据失败: {e}")
        return False
    finally:
        conn.close()

def delete_job_classification(id: str,tenant_id: str):
    """
    根据 id 删除职位分类记录。
    """
    conn = _connect_db()
    if not conn:
        return False

    delete_sql = "DELETE FROM job_classification WHERE id = ? AND tenant_id = ?"
    try:
        cursor = conn.cursor()
        cursor.execute(delete_sql, (id,tenant_id))
        conn.commit()
        if cursor.rowcount > 0:
            print(f"职位分类 id: {id} 删除成功。")
            return True
        else:
            print(f"职位分类 id: {id} 不存在，无法删除。")
            return False
    except sqlite3.Error as e:
        print(f"删除数据失败: {e}")
        return False
    finally:
        conn.close()

def insert_score_trigger(data: dict):
    """
    插入一条新的分数触发器记录。
    如果 id 已存在，则不插入。
    """
    conn = _connect_db()
    if not conn:
        return False

    # 如果没有提供id，生成一个新的UUID
    if 'id' not in data or data['id'] == "":
        data['id'] = str(uuid.uuid4())
    
    data["createdAt"] = data.get("createdAt", datetime.now().isoformat())
    
    serialized_data = {k: _serialize_value(v) for k, v in data.items()}
    
    columns = ', '.join(serialized_data.keys())
    placeholders = ', '.join(['?' for _ in serialized_data.keys()])
    insert_sql = f"INSERT OR IGNORE INTO score_trigger ({columns}) VALUES ({placeholders})"
    
    try:
        cursor = conn.cursor()
        cursor.execute(insert_sql, list(serialized_data.values()))
        conn.commit()
        if cursor.rowcount > 0:
            print(f"分数触发器 (id: {data.get('id', '未知')}) 插入成功。")
            return True
        else:
            print(f"分数触发器 (id: {data.get('id', '未知')}) 已存在，未插入。")
            return False
    except sqlite3.Error as e:
        print(f"插入分数触发器失败: {e}")
        return False
    finally:
        conn.close()

def get_score_trigger(id: str):
    """
    根据 id 查询分数触发器记录。
    返回一个字典，如果未找到则返回 None。
    """
    conn = _connect_db()
    if not conn:
        return None
    
    select_sql = "SELECT * FROM score_trigger WHERE id = ?"
    try:
        cursor = conn.cursor()
        cursor.execute(select_sql, (id,))
        row = cursor.fetchone()
        if row:
            col_names = [description[0] for description in cursor.description]
            trigger_data = dict(zip(col_names, row))
            for key, value in trigger_data.items():
                trigger_data[key] = _deserialize_value(value)
            return trigger_data
        else:
            return None
    except sqlite3.Error as e:
        print(f"查询分数触发器失败: {e}")
        return None
    finally:
        conn.close()

def get_all_score_triggers():
    """
    查询所有分数触发器记录。
    返回一个包含字典的列表。
    """
    conn = _connect_db()
    if not conn:
        return []
    
    select_all_sql = "SELECT * FROM score_trigger"
    try:
        cursor = conn.cursor()
        cursor.execute(select_all_sql)
        rows = cursor.fetchall()
        
        triggers = []
        if rows:
            col_names = [description[0] for description in cursor.description]
            for row in rows:
                trigger_data = dict(zip(col_names, row))
                for key, value in trigger_data.items():
                    trigger_data[key] = _deserialize_value(value)
                triggers.append(trigger_data)
        return triggers
    except sqlite3.Error as e:
        print(f"查询所有分数触发器失败: {e}")
        return []
    finally:
        conn.close()

def update_score_trigger(id: str, updates: dict):
    """
    更新分数触发器记录。
    返回布尔值表示是否更新成功。
    """
    conn = _connect_db()
    if not conn:
        return False
    
    serialized_updates = {k: _serialize_value(v) for k, v in updates.items()}
    
    set_clause = ', '.join([f"{k} = ?" for k in serialized_updates.keys()])
    update_sql = f"UPDATE score_trigger SET {set_clause} WHERE id = ?"
    
    try:
        cursor = conn.cursor()
        cursor.execute(update_sql, list(serialized_updates.values()) + [id])
        conn.commit()
        
        if cursor.rowcount > 0:
            print(f"分数触发器 (id: {id}) 更新成功。")
            return True
        else:
            print(f"分数触发器 (id: {id}) 不存在或未更改。")
            return False
    except sqlite3.Error as e:
        print(f"更新分数触发器失败: {e}")
        return False
    finally:
        conn.close()

def delete_score_trigger(id: str):
    """
    删除分数触发器记录。
    返回布尔值表示是否删除成功。
    """
    conn = _connect_db()
    if not conn:
        return False
    
    delete_sql = "DELETE FROM score_trigger WHERE id = ?"
    
    try:
        cursor = conn.cursor()
        cursor.execute(delete_sql, (id,))
        conn.commit()
        
        if cursor.rowcount > 0:
            print(f"分数触发器 (id: {id}) 删除成功。")
            return True
        else:
            print(f"分数触发器 (id: {id}) 不存在，无法删除。")
            return False
    except sqlite3.Error as e:
        print(f"删除分数触发器失败: {e}")
        return False
    finally:
        conn.close()

# 虚拟HR相关函数
def insert_virtual_hr(data: dict):
    """
    插入一条新的虚拟HR记录。
    如果 id 已存在，则不插入。
    """
    conn = _connect_db()
    if not conn:
        return False

    data["id"] = data.get("id", str(uuid.uuid4()))
    data["createdAt"] = data.get("createdAt", datetime.now().isoformat())

    columns = ', '.join(data.keys())
    placeholders = ', '.join(['?' for _ in data.keys()])
    insert_sql = f"INSERT OR IGNORE INTO virtual_hr ({columns}) VALUES ({placeholders})"

    try:
        cursor = conn.cursor()
        cursor.execute(insert_sql, list(data.values()))
        conn.commit()
        if cursor.rowcount > 0:
            print(f"虚拟HR '{data.get('name', '未知')}' (id: {data.get('id', '未知')}) 插入成功。")
            return True
        else:
            print(f"虚拟HR '{data.get('name', '未知')}' (id: {data.get('id', '未知')}) 已存在，未插入。")
            return False
    except sqlite3.Error as e:
        print(f"插入数据失败: {e}")
        return False
    finally:
        conn.close()

def get_virtual_hr(id: str,tenant_id: str):
    """
    根据 id 查询虚拟HR记录。
    返回一个字典，如果未找到则返回 None。
    """
    conn = _connect_db()
    if not conn:
        return None

    if tenant_id == "":
        select_sql = "SELECT * FROM virtual_hr WHERE id = ?"
    else:
        select_sql = "SELECT * FROM virtual_hr WHERE id = ? AND tenant_id = ?"
    try:
        cursor = conn.cursor()
        if tenant_id == "":
            cursor.execute(select_sql, (id,))
        else:
            cursor.execute(select_sql, (id,tenant_id))
        row = cursor.fetchone()
        if row:
            col_names = [description[0] for description in cursor.description]
            virtual_hr_data = dict(zip(col_names, row))
            return virtual_hr_data
        else:
            return None
    except sqlite3.Error as e:
        print(f"查询数据失败: {e}")
        return None
    finally:
        conn.close()

def get_all_virtual_hrs(tenant_id: str):
    """
    查询所有虚拟HR记录。
    返回一个包含字典的列表。
    """
    conn = _connect_db()
    if not conn:
        return []

    if tenant_id == "":
        select_sql = "SELECT * FROM virtual_hr"
    else:
        select_sql = "SELECT * FROM virtual_hr WHERE tenant_id = ?"
    try:
        cursor = conn.cursor()
        if tenant_id == "":
            cursor.execute(select_sql)
        else:
            cursor.execute(select_sql, (tenant_id,))
        rows = cursor.fetchall()
        
        result = []
        for row in rows:
            col_names = [description[0] for description in cursor.description]
            virtual_hr_data = dict(zip(col_names, row))
            result.append(virtual_hr_data)
        
        return result
    except sqlite3.Error as e:
        print(f"查询数据失败: {e}")
        return []
    finally:
        conn.close()

def update_virtual_hr(id: str, updates: dict,tenant_id: str):
    """
    根据 id 更新虚拟HR记录。
    返回是否更新成功的布尔值。
    """
    conn = _connect_db()
    if not conn:
        return False
    
    # 避免更新主键
    if "id" in updates:
        del updates["id"]
    
    if not updates:  # 如果没有需要更新的字段
        return True
    
    set_clause = ', '.join([f"{key} = ?" for key in updates.keys()])
    update_sql = f"UPDATE virtual_hr SET {set_clause} WHERE id = ? AND tenant_id = ?"
    
    try:
        cursor = conn.cursor()
        values = list(updates.values()) + [id,tenant_id]
        cursor.execute(update_sql, values)
        conn.commit()
        if cursor.rowcount > 0:
            print(f"虚拟HR (id: {id}) 更新成功。")
            return True
        else:
            print(f"虚拟HR (id: {id}) 不存在或未发生实际更新。")
            return False
    except sqlite3.Error as e:
        print(f"更新数据失败: {e}")
        return False
    finally:
        conn.close()

def delete_virtual_hr(id: str,tenant_id: str):
    """
    根据 id 删除虚拟HR记录。
    返回是否删除成功的布尔值。
    """
    conn = _connect_db()
    if not conn:
        return False
    
    delete_sql = "DELETE FROM virtual_hr WHERE id = ? AND tenant_id = ?"
    
    try:
        cursor = conn.cursor()
        cursor.execute(delete_sql, (id,tenant_id))
        conn.commit()
        
        if cursor.rowcount > 0:
            print(f"虚拟HR (id: {id}) 删除成功。")
            return True
        else:
            print(f"虚拟HR (id: {id}) 不存在，未删除。")
            return False
    except sqlite3.Error as e:
        print(f"删除数据失败: {e}")
        return False
    finally:
        conn.close()

def insert_semantic_trigger(data: dict):
    """
    插入一条新的语义触发器记录。
    如果 id 已存在，则不插入。
    """
    conn = _connect_db()
    if not conn:
        return False

    data["createdAt"] = data.get("createdAt", datetime.now().isoformat())
    data["id"] = data.get("id", str(uuid.uuid4()))
    
    try:
        cursor = conn.cursor()
        columns = ", ".join(data.keys())
        placeholders = ", ".join(["?"] * len(data))
        sql = f"INSERT INTO semantic_trigger ({columns}) VALUES ({placeholders})"
        
        # 序列化需要的值
        values = [_serialize_value(data[column]) for column in data.keys()]
        
        cursor.execute(sql, values)
        conn.commit()
        return True
    except sqlite3.Error as e:
        print(f"插入语义触发器失败: {e}")
        return False
    finally:
        conn.close()

def get_semantic_trigger(id: str):
    """
    根据 id 获取语义触发器。
    """
    conn = _connect_db()
    if not conn:
        return None

    try:
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM semantic_trigger WHERE id = ?", (id,))
        row = cursor.fetchone()
        
        if row:
            columns = [column[0] for column in cursor.description]
            result = {columns[i]: row[i] for i in range(len(columns))}
            
            # 反序列化需要的字段
            return result
        return None
    except sqlite3.Error as e:
        print(f"获取语义触发器失败: {e}")
        return None
    finally:
        conn.close()

def get_all_semantic_triggers():
    """
    获取所有语义触发器。
    """
    conn = _connect_db()
    if not conn:
        return []

    try:
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM semantic_trigger ORDER BY createdAt DESC")
        rows = cursor.fetchall()
        
        if rows:
            columns = [column[0] for column in cursor.description]
            result = []
            for row in rows:
                item = {columns[i]: row[i] for i in range(len(columns))}
                result.append(item)
            
            return result
        return []
    except sqlite3.Error as e:
        print(f"获取所有语义触发器失败: {e}")
        return []
    finally:
        conn.close()

def update_semantic_trigger(id: str, updates: dict):
    """
    更新语义触发器记录。
    """
    conn = _connect_db()
    if not conn:
        return False

    try:
        cursor = conn.cursor()
        
        # 检查 id 是否存在
        cursor.execute("SELECT id FROM semantic_trigger WHERE id = ?", (id,))
        if not cursor.fetchone():
            print(f"语义触发器不存在: {id}")
            return False
        
        # 构建更新语句
        set_clause = ", ".join([f"{k} = ?" for k in updates.keys()])
        sql = f"UPDATE semantic_trigger SET {set_clause} WHERE id = ?"
        
        # 序列化需要的值
        values = [_serialize_value(updates[key]) for key in updates.keys()]
        values.append(id)
        
        cursor.execute(sql, values)
        conn.commit()
        return True
    except sqlite3.Error as e:
        print(f"更新语义触发器失败: {e}")
        return False
    finally:
        conn.close()

def delete_semantic_trigger(id: str):
    """
    删除语义触发器记录。
    """
    conn = _connect_db()
    if not conn:
        return False

    try:
        cursor = conn.cursor()
        cursor.execute("DELETE FROM semantic_trigger WHERE id = ?", (id,))
        conn.commit()
        return cursor.rowcount > 0
    except sqlite3.Error as e:
        print(f"删除语义触发器失败: {e}")
        return False
    finally:
        conn.close()

# 语义分类器相关函数
def insert_semantic_classifier(data: dict):
    """
    插入一条新的语义分类器记录
    """
    conn = _connect_db()
    if not conn:
        return False

    data["id"] = data.get("id", str(uuid.uuid4()))
    data["createdAt"] = data.get("createdAt", datetime.now().isoformat())

    columns = ', '.join(data.keys())
    placeholders = ', '.join(['?' for _ in data.keys()])
    insert_sql = f"INSERT INTO semantic_classifier ({columns}) VALUES ({placeholders})"

    try:
        cursor = conn.cursor()
        cursor.execute(insert_sql, list(data.values()))
        conn.commit()
        return data["id"]
    except sqlite3.Error as e:
        print(f"插入语义分类器失败: {e}")
        return None
    finally:
        conn.close()

def get_semantic_classifier(id: str):
    """
    根据ID获取语义分类器
    """
    conn = _connect_db()
    if not conn:
        return None

    select_sql = "SELECT * FROM semantic_classifier WHERE id = ?"
    try:
        cursor = conn.cursor()
        cursor.execute(select_sql, (id,))
        row = cursor.fetchone()
        if row:
            # 获取列名
            columns = [desc[0] for desc in cursor.description]
            # 将行转换为字典
            result = dict(zip(columns, row))
            return result
        return None
    except sqlite3.Error as e:
        print(f"获取语义分类器失败: {e}")
        return None
    finally:
        conn.close()

def get_all_semantic_classifiers():
    """
    获取所有语义分类器
    """
    conn = _connect_db()
    if not conn:
        return []

    select_sql = "SELECT * FROM semantic_classifier ORDER BY createdAt DESC"
    try:
        cursor = conn.cursor()
        cursor.execute(select_sql)
        rows = cursor.fetchall()
        # 获取列名
        columns = [desc[0] for desc in cursor.description]
        # 将行转换为字典列表
        result = [dict(zip(columns, row)) for row in rows]
        
        # 获取每个分类器的问题数量
        for classifier in result:
            count_sql = "SELECT COUNT(*) FROM semantic_classifier_question WHERE classifier_id = ?"
            cursor.execute(count_sql, (classifier["id"],))
            count = cursor.fetchone()[0]
            classifier["question_count"] = count
            
        return result
    except sqlite3.Error as e:
        print(f"获取语义分类器列表失败: {e}")
        return []
    finally:
        conn.close()

def update_semantic_classifier(id: str, updates: dict):
    """
    更新语义分类器
    """
    conn = _connect_db()
    if not conn:
        return False

    update_items = [f"{k} = ?" for k in updates.keys()]
    update_sql = f"UPDATE semantic_classifier SET {', '.join(update_items)} WHERE id = ?"
    
    values = list(updates.values()) + [id]

    try:
        cursor = conn.cursor()
        cursor.execute(update_sql, values)
        conn.commit()
        return cursor.rowcount > 0
    except sqlite3.Error as e:
        print(f"更新语义分类器失败: {e}")
        return False
    finally:
        conn.close()

def delete_semantic_classifier(id: str):
    """
    删除语义分类器及其问题
    """
    conn = _connect_db()
    if not conn:
        return False

    try:
        cursor = conn.cursor()
        # 先删除关联的问题
        cursor.execute("DELETE FROM semantic_classifier_question WHERE classifier_id = ?", (id,))
        # 再删除分类器
        cursor.execute("DELETE FROM semantic_classifier WHERE id = ?", (id,))
        conn.commit()
        return cursor.rowcount > 0
    except sqlite3.Error as e:
        print(f"删除语义分类器失败: {e}")
        return False
    finally:
        conn.close()

# 语义分类器问题相关函数
def insert_semantic_classifier_question(data: dict):
    """
    插入语义分类器问题
    """
    conn = _connect_db()
    if not conn:
        return False

    data["id"] = data.get("id", str(uuid.uuid4()))
    data["createdAt"] = data.get("createdAt", datetime.now().isoformat())

    columns = ', '.join(data.keys())
    placeholders = ', '.join(['?' for _ in data.keys()])
    insert_sql = f"INSERT INTO semantic_classifier_question ({columns}) VALUES ({placeholders})"

    try:
        cursor = conn.cursor()
        cursor.execute(insert_sql, list(data.values()))
        conn.commit()
        return data["id"]
    except sqlite3.Error as e:
        print(f"插入语义分类器问题失败: {e}")
        return None
    finally:
        conn.close()

def get_semantic_classifier_question(id: str):
    """
    根据ID获取语义分类器问题
    """
    conn = _connect_db()
    if not conn:
        return None

    select_sql = "SELECT * FROM semantic_classifier_question WHERE id = ?"
    try:
        cursor = conn.cursor()
        cursor.execute(select_sql, (id,))
        row = cursor.fetchone()
        if row:
            # 获取列名
            columns = [desc[0] for desc in cursor.description]
            # 将行转换为字典
            result = dict(zip(columns, row))
            return result
        return None
    except sqlite3.Error as e:
        print(f"获取语义分类器问题失败: {e}")
        return None
    finally:
        conn.close()

def get_questions_by_classifier(classifier_id: str):
    """
    获取某个分类器的所有问题
    """
    conn = _connect_db()
    if not conn:
        return []

    select_sql = "SELECT * FROM semantic_classifier_question WHERE classifier_id = ?"
    try:
        cursor = conn.cursor()
        cursor.execute(select_sql, (classifier_id,))
        rows = cursor.fetchall()
        # 获取列名
        columns = [desc[0] for desc in cursor.description]
        # 将行转换为字典列表
        result = [dict(zip(columns, row)) for row in rows]
        return result
    except sqlite3.Error as e:
        print(f"获取分类器问题列表失败: {e}")
        return []
    finally:
        conn.close()

def update_semantic_classifier_question(id: str, updates: dict):
    """
    更新语义分类器问题
    """
    conn = _connect_db()
    if not conn:
        return False

    update_items = [f"{k} = ?" for k in updates.keys()]
    update_sql = f"UPDATE semantic_classifier_question SET {', '.join(update_items)} WHERE id = ?"
    
    values = list(updates.values()) + [id]

    try:
        cursor = conn.cursor()
        cursor.execute(update_sql, values)
        conn.commit()
        return cursor.rowcount > 0
    except sqlite3.Error as e:
        print(f"更新语义分类器问题失败: {e}")
        return False
    finally:
        conn.close()

def delete_semantic_classifier_question(id: str):
    """
    删除语义分类器问题
    """
    conn = _connect_db()
    if not conn:
        return False

    try:
        cursor = conn.cursor()
        cursor.execute("DELETE FROM semantic_classifier_question WHERE id = ?", (id,))
        conn.commit()
        return cursor.rowcount > 0
    except sqlite3.Error as e:
        print(f"删除语义分类器问题失败: {e}")
        return False
    finally:
        conn.close()

# 答案分类器相关函数
def insert_answer_classifier(data: dict):
    """
    插入答案分类器记录
    """
    conn = _connect_db()
    if not conn:
        return False

    data["id"] = data.get("id", str(uuid.uuid4()))
    data["createdAt"] = data.get("createdAt", datetime.now().isoformat())

    columns = ', '.join(data.keys())
    placeholders = ', '.join(['?' for _ in data.keys()])
    insert_sql = f"INSERT INTO answer_classifier ({columns}) VALUES ({placeholders})"

    try:
        cursor = conn.cursor()
        cursor.execute(insert_sql, list(data.values()))
        conn.commit()
        return data["id"]
    except sqlite3.Error as e:
        print(f"插入答案分类器失败: {e}")
        return None
    finally:
        conn.close()

def get_answer_classifier(id: str):
    """
    根据ID获取答案分类器
    """
    conn = _connect_db()
    if not conn:
        return None

    select_sql = "SELECT * FROM answer_classifier WHERE id = ?"
    try:
        cursor = conn.cursor()
        cursor.execute(select_sql, (id,))
        row = cursor.fetchone()
        if row:
            # 获取列名
            columns = [desc[0] for desc in cursor.description]
            # 将行转换为字典
            result = dict(zip(columns, row))
            return result
        return None
    except sqlite3.Error as e:
        print(f"获取答案分类器失败: {e}")
        return None
    finally:
        conn.close()

def get_answer_classifier_by_type(type: str):
    """
    根据类型获取答案分类器
    """
    conn = _connect_db()
    if not conn:
        return None

    select_sql = "SELECT * FROM answer_classifier WHERE type = ?"
    try:
        cursor = conn.cursor()
        cursor.execute(select_sql, (type,))
        row = cursor.fetchone()
        if row:
            # 获取列名
            columns = [desc[0] for desc in cursor.description]
            # 将行转换为字典
            result = dict(zip(columns, row))
            return result
        return None
    except sqlite3.Error as e:
        print(f"获取答案分类器失败: {e}")
        return None
    finally:
        conn.close()

def get_all_answer_classifiers():
    """
    获取所有答案分类器
    """
    conn = _connect_db()
    if not conn:
        return []

    select_sql = "SELECT * FROM answer_classifier"
    try:
        cursor = conn.cursor()
        cursor.execute(select_sql)
        rows = cursor.fetchall()
        # 获取列名
        columns = [desc[0] for desc in cursor.description]
        # 将行转换为字典列表
        result = [dict(zip(columns, row)) for row in rows]
        return result
    except sqlite3.Error as e:
        print(f"获取答案分类器列表失败: {e}")
        return []
    finally:
        conn.close()

def update_answer_classifier(id: str, updates: dict):
    """
    更新答案分类器
    """
    conn = _connect_db()
    if not conn:
        return False

    update_items = [f"{k} = ?" for k in updates.keys()]
    update_sql = f"UPDATE answer_classifier SET {', '.join(update_items)} WHERE id = ?"
    
    values = list(updates.values()) + [id]

    try:
        cursor = conn.cursor()
        cursor.execute(update_sql, values)
        conn.commit()
        return cursor.rowcount > 0
    except sqlite3.Error as e:
        print(f"更新答案分类器失败: {e}")
        return False
    finally:
        conn.close()

def delete_answer_classifier(id: str):
    """删除答案分类器"""
    conn = _connect_db()
    if conn:
        try:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM answer_classifier WHERE id = ?", (id,))
            conn.commit()
            return cursor.rowcount > 0
        except sqlite3.Error as e:
            print(f"删除答案分类器失败: {e}")
            return False
        finally:
            conn.close()
    return False

def get_answer_classifier_by_name(name: str):
    """根据名称获取答案分类器"""
    conn = _connect_db()
    if conn:
        try:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM answer_classifier WHERE name = ?", (name,))
            result = cursor.fetchone()
            if result:
                return dict(result)
            return None
        except sqlite3.Error as e:
            print(f"根据名称获取答案分类器失败: {e}")
            return None
        finally:
            conn.close()
        return None

# ======================== 管理员用户相关函数 ========================

def generate_salt07082():
    """生成随机盐值"""
    return ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(16))

def hash_password07082(password, salt):
    """使用MD5+salt对密码进行哈希"""
    hash_obj = hashlib.md5((password + salt).encode())
    return hash_obj.hexdigest()

def insert_admin07082(data: dict):
    """添加一个新的管理员用户"""
    conn = _connect_db()
    if conn:
        try:
            cursor = conn.cursor()
            # 生成盐值和哈希密码
            salt = data.get("salt") or generate_salt07082()
            hashed_password = hash_password07082(data["password"], salt)
            
            # 生成UUID作为ID
            admin_id = data.get("id") or str(uuid.uuid4())
            created_at = data.get("createdAt") or datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            cursor.execute(
                "INSERT INTO admin_user07082 (id, account, password, salt, nickname, createdAt) VALUES (?, ?, ?, ?, ?, ?)",
                (admin_id, data["account"], hashed_password, salt, data.get("nickname", ""), created_at)
            )
            conn.commit()
            return admin_id
        except sqlite3.Error as e:
            print(f"添加管理员用户失败: {e}")
            return None
        finally:
            conn.close()
    return None

def get_admin07082(admin_id: str):
    """根据ID获取管理员用户信息"""
    conn = _connect_db()
    if conn:
        try:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM admin_user07082 WHERE id = ?", (admin_id,))
            result = cursor.fetchone()
            if result:
                return dict(result)
            return None
        except sqlite3.Error as e:
                print(f"获取管理员用户失败: {e}")
                return None
        finally:
            conn.close()
    return None

def get_admin_by_account07082(account: str):
    """根据账号获取管理员用户信息"""
    conn = _connect_db()
    if conn:
        try:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM admin_user07082 WHERE account = ?", (account,))
            result = cursor.fetchone()
            if result:
                return dict(result)
            return None
        except sqlite3.Error as e:
            print(f"根据账号获取管理员用户失败: {e}")
            return None
        finally:
            conn.close()
    return None

def get_all_admins07082():
    """获取所有管理员用户"""
    conn = _connect_db()
    if conn:
        try:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM admin_user07082")
            results = cursor.fetchall()
            return [dict(row) for row in results]
        except sqlite3.Error as e:
            print(f"获取所有管理员用户失败: {e}")
            return []
        finally:
            conn.close()
    return []

def update_admin07082(admin_id: str, updates: dict):
    """更新管理员用户信息"""
    conn = _connect_db()
    if conn:
        try:
            cursor = conn.cursor()
            set_clauses = []
            params = []
            
            for key, value in updates.items():
                if key != "id":  # 防止更新主键
                    # 如果更新密码，需要重新生成哈希
                    if key == "password":
                        admin = get_admin07082(admin_id)
                        if admin:
                            value = hash_password07082(value, admin["salt"])
                    
                    set_clauses.append(f"{key} = ?")
                    params.append(value)
            
            if set_clauses:
                params.append(admin_id)
                sql = f"UPDATE admin_user07082 SET {', '.join(set_clauses)} WHERE id = ?"
                cursor.execute(sql, params)
                conn.commit()
                return cursor.rowcount > 0
            
            return False
        except sqlite3.Error as e:
            print(f"更新管理员用户失败: {e}")
            return False
        finally:
            conn.close()
    return False

def delete_admin07082(admin_id: str):
    """删除管理员用户"""
    conn = _connect_db()
    if conn:
        try:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM admin_user07082 WHERE id = ?", (admin_id,))
            conn.commit()
            return cursor.rowcount > 0
        except sqlite3.Error as e:
            print(f"删除管理员用户失败: {e}")
            return False
        finally:
            conn.close()
    return False

def verify_admin_login07082(account: str, password: str):
    """验证管理员登录"""
    admin = get_admin_by_account07082(account)
    if admin:
        hashed_password = hash_password07082(password, admin["salt"])
        if hashed_password == admin["password"]:
            return admin
    return None
