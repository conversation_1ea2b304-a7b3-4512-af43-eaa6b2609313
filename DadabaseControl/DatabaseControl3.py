import sqlite3
import json
from datetime import datetime, timedelta
import uuid
import hashlib
import random
import string

from DadabaseControl.DatabaseControl import DB_NAME,_connect_db,_serialize_value,_deserialize_value
from DadabaseControl.DatabaseControl2 import generate_salt07082,hash_password07082,verify_admin_login07082,get_job_classification

# ======================== 租户用户相关函数 ========================

def insert_tenant07082(data: dict):
    """添加一个新的租户用户"""
    conn = _connect_db()
    if conn:
        try:
            cursor = conn.cursor()
            # 生成盐值和哈希密码
            salt = data.get("salt") or generate_salt07082()
            hashed_password = hash_password07082(data["password"], salt)
            
            # 生成UUID作为ID
            tenant_id = data.get("id") or str(uuid.uuid4())
            created_at = data.get("createdAt") or datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            cursor.execute(
                "INSERT INTO tenant07082 (id, account, password, salt, nickname, phone, email, wechat, createdAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
                (tenant_id, data["account"], hashed_password, salt, 
                 data.get("nickname", ""), data.get("phone", ""), 
                 data.get("email", ""), data.get("wechat", ""), created_at)
            )
            conn.commit()
            return tenant_id
        except sqlite3.Error as e:
            print(f"添加租户用户失败: {e}")
            return None
        finally:
            conn.close()
    return None

def get_tenant07082(tenant_id: str):
    """根据ID获取租户用户信息"""
    conn = _connect_db()
    if conn:
        try:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM tenant07082 WHERE id = ?", (tenant_id,))
            result = cursor.fetchone()
            if result:
                return dict(result)
            return None
        except sqlite3.Error as e:
            print(f"获取租户用户失败: {e}")
            return None
        finally:
            conn.close()
    return None

def get_tenant_by_account07082(account: str):
    """根据账号获取租户用户信息"""
    conn = _connect_db()
    if conn:
        try:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM tenant07082 WHERE account = ?", (account,))
            result = cursor.fetchone()
            if result:
                return dict(result)
            return None
        except sqlite3.Error as e:
            print(f"根据账号获取租户用户失败: {e}")
            return None
        finally:
            conn.close()
    return None

def get_all_tenants07082():
    """获取所有租户用户"""
    conn = _connect_db()
    if conn:
        try:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM tenant07082")
            results = cursor.fetchall()
            return [dict(row) for row in results]
        except sqlite3.Error as e:
            print(f"获取所有租户用户失败: {e}")
            return []
        finally:
            conn.close()
    return []

def update_tenant07082(tenant_id: str, updates: dict):
    """更新租户用户信息"""
    conn = _connect_db()
    if conn:
        try:
            cursor = conn.cursor()
            set_clauses = []
            params = []
            
            for key, value in updates.items():
                if key != "id":  # 防止更新主键
                    # 如果更新密码，需要重新生成哈希
                    if key == "password":
                        tenant = get_tenant07082(tenant_id)
                        if tenant:
                            value = hash_password07082(value, tenant["salt"])
                    
                    set_clauses.append(f"{key} = ?")
                    params.append(value)
            
            if set_clauses:
                params.append(tenant_id)
                sql = f"UPDATE tenant07082 SET {', '.join(set_clauses)} WHERE id = ?"
                cursor.execute(sql, params)
                conn.commit()
                return cursor.rowcount > 0
            
            return False
        except sqlite3.Error as e:
            print(f"更新租户用户失败: {e}")
            return False
        finally:
            conn.close()
    return False

def delete_tenant07082(tenant_id: str):
    """删除租户用户"""
    conn = _connect_db()
    if conn:
        try:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM tenant07082 WHERE id = ?", (tenant_id,))
            conn.commit()
            return cursor.rowcount > 0
        except sqlite3.Error as e:
            print(f"删除租户用户失败: {e}")
            return False
        finally:
            conn.close()
    return False

def verify_tenant_login07082(account: str, password: str):
    """验证租户登录"""
    tenant = get_tenant_by_account07082(account)
    if tenant:
        hashed_password = hash_password07082(password, tenant["salt"])
        if hashed_password == tenant["password"]:
            return tenant
    return None

def get_tenant_by_id(tenant_id: str):
    """根据ID获取租户信息"""
    conn = _connect_db()
    if conn:
        try:
            cursor = conn.cursor()
            cursor.execute("SELECT account,nickname FROM tenant07082 WHERE id = ?", (tenant_id,))
            result = cursor.fetchone()
            if result:
                return {"account":result[0],"nickname":result[1]}
            return None
        except sqlite3.Error as e:
            print(f"获取租户信息失败: {e}")
            return None
        
def verify_login07082(account: str, password: str, user_type: str):
    """验证登录（管理员或租户）"""
    if user_type == "admin":
        return verify_admin_login07082(account, password)
    elif user_type == "tenant":
        return verify_tenant_login07082(account, password)
    return None

# ======================== APIKey相关函数 ========================

def generate_apikey():
    """生成随机APIKey"""
    return str(uuid.uuid4()).replace("-", "") + str(uuid.uuid4()).replace("-", "")

def insert_tenant_apikey(tenant_id: str, name: str):
    """为租户添加一个新的APIKey"""
    conn = _connect_db()
    if conn:
        try:
            cursor = conn.cursor()
            apikey_id = str(uuid.uuid4())
            apikey = generate_apikey()
            created_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            cursor.execute(
                "INSERT INTO tenant_apikey (id, tenant_id, name, apikey, createdAt) VALUES (?, ?, ?, ?, ?)",
                (apikey_id, tenant_id, name, apikey, created_at)
            )
            conn.commit()
            return {"id": apikey_id, "tenant_id": tenant_id, "name": name, "apikey": apikey, "createdAt": created_at}
        except sqlite3.Error as e:
            print(f"添加APIKey失败: {e}")
            return None
        finally:
            conn.close()
    return None

def get_apikeys_by_tenant(tenant_id: str = None):
    """获取租户的所有APIKey"""
    conn = _connect_db()
    if conn:
        try:
            cursor = conn.cursor()
            if tenant_id is None:
                cursor.execute("SELECT * FROM tenant_apikey")
            else:
                cursor.execute("SELECT * FROM tenant_apikey WHERE tenant_id = ?", (tenant_id,))
            rows = cursor.fetchall()
            
            apikeys = []
            for row in rows:
                apikeys.append({
                    "id": row[0],
                    "tenant_id": row[1],
                    "name": row[2],
                    "apikey": row[3],
                    "createdAt": row[4]
                })
            return apikeys
        except sqlite3.Error as e:
            print(f"获取APIKey失败: {e}")
            return []
        finally:
            conn.close()
    return []

def delete_apikey(apikey_id: str):
    """删除APIKey"""
    conn = _connect_db()
    if conn:
        try:
            cursor = conn.cursor()
            # 先获取APIKey信息
            cursor.execute("SELECT apikey FROM tenant_apikey WHERE id = ?", (apikey_id,))
            row = cursor.fetchone()
            if not row:
                return False
                
            apikey = row[0]
            
            # 删除相关的token记录
            cursor.execute("DELETE FROM apikey_token WHERE apikey = ?", (apikey,))
            
            # 删除APIKey记录
            cursor.execute("DELETE FROM tenant_apikey WHERE id = ?", (apikey_id,))
            conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"删除APIKey失败: {e}")
            return False
        finally:
            conn.close()
    return False

def get_apikey_by_value(apikey: str):
    """通过APIKey值获取信息"""
    conn = _connect_db()
    if conn:
        try:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM tenant_apikey WHERE apikey = ?", (apikey,))
            row = cursor.fetchone()
            if row:
                return {
                    "id": row[0],
                    "tenant_id": row[1],
                    "name": row[2],
                    "apikey": row[3],
                    "createdAt": row[4]
                }
            return None
        except sqlite3.Error as e:
            print(f"获取APIKey信息失败: {e}")
            return None
        finally:
            conn.close()
    return None

# ======================== Token用量统计相关函数 ========================

def insert_apikey_token_usage(tenant_id: str, apikey: str, uptoken: int, downtoken: int):
    """记录APIKey的Token使用情况"""
    conn = _connect_db()
    if conn:
        try:
            cursor = conn.cursor()
            token_id = str(uuid.uuid4())
            created_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            cursor.execute(
                "INSERT INTO apikey_token (id, tenant_id, apikey, uptoken, downtoken, createdAt) VALUES (?, ?, ?, ?, ?, ?)",
                (token_id, tenant_id, apikey, uptoken, downtoken, created_at)
            )
            conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"记录Token用量失败: {e}")
            return False
        finally:
            conn.close()
    return False

def get_token_usage_by_tenant(tenant_id: str, start_date=None, end_date=None):
    """获取租户的Token使用情况，可选择时间范围"""
    conn = _connect_db()
    if conn:
        try:
            cursor = conn.cursor()
            query = "SELECT apikey, SUM(uptoken) as total_uptoken, SUM(downtoken) as total_downtoken FROM apikey_token WHERE tenant_id = ?"
            params = [tenant_id]
            
            # 添加日期筛选
            if start_date and end_date:
                query += " AND createdAt BETWEEN ? AND ?"
                params.extend([start_date, end_date])
            
            query += " GROUP BY apikey"
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            usage_data = []
            for row in rows:
                apikey = row[0]
                # 获取APIKey名称
                cursor.execute("SELECT name FROM tenant_apikey WHERE apikey = ?", (apikey,))
                name_row = cursor.fetchone()
                name = name_row[0] if name_row else "未知"
                
                usage_data.append({
                    "apikey": apikey,
                    "name": name,
                    "uptoken": row[1],
                    "downtoken": row[2],
                    "total": row[1] + row[2]
                })
            return usage_data
        except sqlite3.Error as e:
            print(f"获取Token用量失败: {e}")
            return []
        finally:
            conn.close()
    return []

def get_token_usage_daily(tenant_id: str = None, start_date=None, end_date=None):
    """获取每日Token使用情况"""
    conn = _connect_db()
    if conn:
        try:
            cursor = conn.cursor()
            if tenant_id is None:
                query = """
                    SELECT 
                        substr(createdAt, 1, 10) as date, 
                        SUM(uptoken) as total_uptoken, 
                        SUM(downtoken) as total_downtoken,
                        apikey
                    FROM apikey_token
                    GROUP BY date ORDER BY date
                """
            else:
                query = """
                    SELECT 
                        substr(createdAt, 1, 10) as date, 
                        SUM(uptoken) as total_uptoken, 
                        SUM(downtoken) as total_downtoken,
                        apikey
                    FROM apikey_token 
                    WHERE tenant_id = ?
                    GROUP BY date ORDER BY date
                """
            params = []
            
            if tenant_id is not None:
                params.append(tenant_id)
            # 添加日期筛选
            if start_date and end_date:
                query += " AND createdAt BETWEEN ? AND ?"
                params.extend([start_date, end_date])
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            daily_data = []
            for row in rows:
                if row[3] is None:
                    continue
                daily_data.append({
                    "date": row[0],
                    "uptoken": row[1],
                    "downtoken": row[2],
                    "total": row[1] + row[2],
                    "apikey": row[3]
                })
            return daily_data
        except sqlite3.Error as e:
            print(f"获取每日Token用量失败: {e}")
            return []
        finally:
            conn.close()
    return []

def get_hourly_token_usage(apikey: str, date: str):
    """
    获取指定API密钥在指定日期的每小时token使用量
    """
    conn = _connect_db()
    if not conn:
        return []

    try:
        if not apikey:
            return []
        # 获取指定日期的所有记录
        cursor = conn.cursor()
        cursor.execute("""
            SELECT apikey, createdAt, uptoken, downtoken
            FROM apikey_token
            WHERE apikey = ? AND DATE(createdAt) LIKE ?
        """, (apikey, date))
        
        records = cursor.fetchall()
        if not records:
            return []
        
        # 初始化每小时数据
        hourly_data = {}
        for hour in range(24):
            hour_str = f"{hour:02d}"
            hourly_data[hour_str] = {
                "hour": hour_str,
                "uptoken": 0,
                "downtoken": 0
            }
        
        # 汇总数据
        for record in records:
            apikey, created_at, uptoken, downtoken = record
            try:
                dt = datetime.fromisoformat(created_at)
                hour = f"{dt.hour:02d}"
                
                hourly_data[hour]["uptoken"] += uptoken or 0
                hourly_data[hour]["downtoken"] += downtoken or 0
            except:
                # 如果日期格式有误，跳过
                continue
                
        # 转换为列表
        return [data for data in hourly_data.values()]
    except sqlite3.Error as e:
        print(f"获取小时token使用量失败: {e}")
        return []
    finally:
        conn.close()

# 渠道接入管理相关函数
def insert_channel(data: dict):
    """
    插入一条新的渠道记录。
    """
    conn = _connect_db()
    if not conn:
        return False, "数据库连接失败"

    if "id" not in data:
        data["id"] = str(uuid.uuid4()).replace("-", "")
    data["createdAt"] = data.get("createdAt", datetime.now().isoformat())

    serialized_data = {k: _serialize_value(v) for k, v in data.items()}

    columns = ', '.join(serialized_data.keys())
    placeholders = ', '.join(['?' for _ in serialized_data.keys()])
    insert_sql = f"INSERT OR REPLACE INTO channel ({columns}) VALUES ({placeholders})"

    try:
        cursor = conn.cursor()
        cursor.execute(insert_sql, list(serialized_data.values()))
        conn.commit()
        if cursor.rowcount > 0:
            print(f"渠道 '{data.get('channel_type', '未知')}' 插入成功。")
            return True, data["id"]
        else:
            return False, "渠道插入失败"
    except sqlite3.Error as e:
        print(f"插入渠道数据失败: {e}")
        return False, str(e)
    finally:
        conn.close()

def get_channel(id: str):
    """
    通过ID获取渠道信息。
    """
    conn = _connect_db()
    if not conn:
        return None

    try:
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM channel WHERE id = ?", (id,))
        channel = cursor.fetchone()
        
        if not channel:
            return None
        
        columns = [col[0] for col in cursor.description]
        channel_dict = {columns[i]: _deserialize_value(channel[i]) for i in range(len(columns))}
        
        return channel_dict
    except sqlite3.Error as e:
        print(f"获取渠道失败: {e}")
        return None
    finally:
        conn.close()

def get_channel_by_params(params: dict):
    """
    通过多个参数获取渠道信息。
    params: 包含查询条件的字典，如 {"tenant_id": "xxx", "channel_type": "yyy", "apikey": "zzz"}
    """
    conn = _connect_db()
    if not conn:
        return None

    try:
        cursor = conn.cursor()
        
        # 构建查询条件
        conditions = []
        values = []
        
        for key, value in params.items():
            conditions.append(f"{key} = ?")
            values.append(value)
        
        # 组合查询条件
        where_clause = " AND ".join(conditions)
        
        # 执行查询
        query = f"SELECT * FROM channel WHERE {where_clause}"
        cursor.execute(query, values)
        
        channel = cursor.fetchone()
        
        if not channel:
            return None
        
        columns = [col[0] for col in cursor.description]
        channel_dict = {columns[i]: _deserialize_value(channel[i]) for i in range(len(columns))}
        
        return channel_dict
    except sqlite3.Error as e:
        print(f"获取渠道失败: {e}")
        return None
    finally:
        conn.close()

def get_channel_by_tenant_and_type(tenant_id: str,apikey: str, channel_type: str):
    """
    通过租户ID和apikey和渠道类型获取渠道信息。
    """
    conn = _connect_db()
    if not conn:
        return None

    try:
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM channel WHERE tenant_id = ? AND apikey = ? AND channel_type = ?", (tenant_id, apikey, channel_type))
        channel = cursor.fetchone()
        
        if not channel:
            return None
        
        columns = [col[0] for col in cursor.description]
        channel_dict = {columns[i]: _deserialize_value(channel[i]) for i in range(len(columns))}
        
        return channel_dict
    except sqlite3.Error as e:
        print(f"获取渠道失败: {e}")
        return None
    finally:
        conn.close()

def get_channels_by_tenant(tenant_id: str):
    """
    获取租户的所有渠道。
    """
    conn = _connect_db()
    if not conn:
        return []

    try:
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM channel WHERE tenant_id = ?", (tenant_id,))
        channels = cursor.fetchall()
        
        if not channels:
            return []
        
        columns = [col[0] for col in cursor.description]
        channels_list = []
        
        for channel in channels:
            channel_dict = {columns[i]: _deserialize_value(channel[i]) for i in range(len(columns))}
            channels_list.append(channel_dict)
        
        return channels_list
    except sqlite3.Error as e:
        print(f"获取渠道列表失败: {e}")
        return []
    finally:
        conn.close()

def update_channel(id: str, updates: dict):
    """
    更新渠道信息。
    """
    conn = _connect_db()
    if not conn:
        return False, "数据库连接失败"

    serialized_updates = {k: _serialize_value(v) for k, v in updates.items()}
    set_clause = ', '.join([f"{key} = ?" for key in serialized_updates.keys()])
    update_sql = f"UPDATE channel SET {set_clause} WHERE id = ?"
    
    values = list(serialized_updates.values()) + [id]

    try:
        cursor = conn.cursor()
        cursor.execute(update_sql, values)
        conn.commit()
        
        if cursor.rowcount > 0:
            print(f"渠道 (ID: {id}) 更新成功。")
            return True, "渠道更新成功"
        else:
            print(f"渠道 (ID: {id}) 不存在或未更改。")
            return False, "渠道不存在或未更改"
    except sqlite3.Error as e:
        print(f"更新渠道失败: {e}")
        return False, str(e)
    finally:
        conn.close()

def delete_channel(id: str):
    """
    删除渠道。
    """
    conn = _connect_db()
    if not conn:
        return False, "数据库连接失败"

    try:
        cursor = conn.cursor()
        cursor.execute("DELETE FROM channel WHERE id = ?", (id,))
        conn.commit()
        
        if cursor.rowcount > 0:
            print(f"渠道 (ID: {id}) 删除成功。")
            return True, "渠道删除成功"
        else:
            print(f"渠道 (ID: {id}) 不存在。")
            return False, "渠道不存在"
    except sqlite3.Error as e:
        print(f"删除渠道失败: {e}")
        return False, str(e)
    finally:
        conn.close()

def get_user_by_wxid(wxid: str,tenant_id: str):
    """
    通过wxid获取用户信息
    """
    conn = _connect_db()
    if not conn:
        return None
    
    try:
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM user WHERE wxid = ? AND tenant_id = ?", (wxid,tenant_id))
        user = cursor.fetchone()
        if not user:
            return None
        else:
            columns = [col[0] for col in cursor.description]
            user_dict = {columns[i]: user[i] for i in range(len(columns))}
            return user_dict
    except sqlite3.Error as e:
        print(f"获取用户失败: {e}")
        return None
    finally:
        conn.close()

def get_user_by_name(name: str):
    """
    通过name获取用户信息
    """
    conn = _connect_db()
    if not conn:
        return []
    
    try:
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM user WHERE name = ?", (name,))
        users = cursor.fetchall()
        if not users:
            return []
        else:
            return users
    except sqlite3.Error as e:
        print(f"获取用户失败: {e}")
        return []

def get_user_by_real_name(real_name: str,tenant_id: str):
    """
    通过real_name获取用户信息
    """
    if real_name is None or real_name == "":
        return []
    conn = _connect_db()
    if not conn:
        return []
    
    try:
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM user WHERE real_name = ? AND (wxid IS NULL OR wxid = '') AND tenant_id = ?", (real_name,tenant_id))
        users = cursor.fetchall()
        # 将users转换为字典列表
        col_names = [description[0] for description in cursor.description]
        users_list = []
        for user in users:
            user_dict = {col_names[i]: user[i] for i in range(len(col_names))}
            users_list.append(user_dict)
        if not users_list:
            return []
        else:
            return users_list
    except sqlite3.Error as e:
        print(f"获取用户失败: {e}")
        return []

def get_user_by_phone_and_not_wxid(phone: str,tenant_id: str):
    """
    通过phone获取用户信息，只获取没有wxid的user。
    """
    conn = _connect_db()
    if not conn:
        return []
    
    try:
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM user WHERE phone = ? AND (wxid IS NULL OR wxid = '') AND tenant_id = ?", (phone,tenant_id))
        user = cursor.fetchone()
        if not user:
            return None
        col_names = [description[0] for description in cursor.description]
        user_dict = {col_names[i]: user[i] for i in range(len(col_names))}
        return [user_dict]
    except sqlite3.Error as e:
        print(f"获取用户失败: {e}")
        return []
    finally:
        conn.close()

def get_user_by_position_and_name_and_not_wxid(position_id: str,name: str):
    """
    通过岗位ID和姓名获取用户信息，只获取没有wxid的user。
    """
    conn = _connect_db()
    if not conn:
        return None
    
    try:
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM user WHERE positionId = ? AND name = ? AND (wxid IS NULL OR wxid = '')", (position_id,name))
        user = cursor.fetchone()
        if not user:
            return None
        col_names = [description[0] for description in cursor.description]
        user_dict = {col_names[i]: user[i] for i in range(len(col_names))}
        return user_dict
    except sqlite3.Error as e:
        print(f"获取用户失败: {e}")
        return None
    finally:
        conn.close()

def get_phone_by_name(name: str):
    """
    通过姓名获取手机号，只获取没有wxid的user，只获取最近三天的数据
    """
    conn = _connect_db()
    if not conn:
        return []
    try:
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM user WHERE name = ? AND wxid IS NULL AND createdAt > ?", (name,datetime.now() - timedelta(days=3)))
        users = cursor.fetchall()
        if not users:
            return []
        col_names = [description[0] for description in cursor.description]
        user_dicts = []
        for user in users:
            user_dict = {col_names[i]: user[i] for i in range(len(col_names))}
            user_dicts.append(user_dict)
        return user_dicts
    except sqlite3.Error as e:
        print(f"获取手机号失败: {e}")
        return []
    finally:
        conn.close()

def get_phone_by_real_name(real_name: str,tenant_id: str):
    """
    通过姓名获取手机号，只获取没有wxid的user，只获取最近三天的数据
    """
    conn = _connect_db()
    if not conn:
        return []
    try:
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM user WHERE real_name = ? AND (wxid IS NULL OR wxid = '') AND createdAt > ? AND tenant_id = ?", (real_name,datetime.now() - timedelta(days=3),tenant_id))
        users = cursor.fetchall()
        if not users:
            return []
        col_names = [description[0] for description in cursor.description]
        user_dicts = []
        for user in users:
            user_dict = {col_names[i]: user[i] for i in range(len(col_names))}
            user_dicts.append(user_dict)
        return user_dicts
    except sqlite3.Error as e:
        print(f"获取手机号失败: {e}")
        return []
    finally:
        conn.close()

def get_position_by_name(name: str):
    """
    通过岗位名称获取岗位信息,返回字典包含全部字段
    """
    conn = _connect_db()
    if not conn:
        return None
    
    try:
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM position WHERE positionName = ?", (name,))
        position = cursor.fetchone()
        if not position:
            return None
        col_names = [description[0] for description in cursor.description]
        position_dict = {col_names[i]: position[i] for i in range(len(col_names))}
        return position_dict
    except sqlite3.Error as e:
        print(f"获取岗位失败: {e}")
        return None
    finally:
        conn.close()

def get_chatlog_by_channel(userid: str,channel: str):
    """
    通过userid和channel获取聊天记录
    """
    conn = _connect_db()
    if not conn:
        return []
    
    try:
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM chatlog WHERE userid = ? AND channel = ?", (userid,channel))
        chatlogs = cursor.fetchall()
        if not chatlogs:
            return []
        col_names = [description[0] for description in cursor.description]
        chatlog_dicts = []
        for chatlog in chatlogs:
            chatlog_dict = {col_names[i]: chatlog[i] for i in range(len(col_names))}
            chatlog_dicts.append(chatlog_dict)
        return chatlog_dicts
    except sqlite3.Error as e:
        print(f"获取聊天记录失败: {e}")
        return []
    finally:
        conn.close()

# 获取虚拟HR的统计信息
def get_virtualhr_statistics(hr_id: str) -> dict:
    """
    获取虚拟HR相关的统计信息:
    - 管理了多少职位分类
    - 管理了多少职位
    - 与多少用户聊过天
    """
    conn = _connect_db()
    if not conn:
        return {
            "job_categories_count": 0,
            "positions_count": 0,
            "chat_users_count": 0
        }
    
    try:
        cursor = conn.cursor()
        stats = {}
        
        # 1. 获取该虚拟HR管理的职位分类数量
        cursor.execute("""
            SELECT COUNT(DISTINCT id) 
            FROM job_classification 
            WHERE virtual_hr_id = ?
        """, (hr_id,))
        job_categories_count = cursor.fetchone()[0] or 0
        stats["job_categories_count"] = job_categories_count
        
        # 2. 用virtual_hr_id获取该虚拟HR管理的职位数量
        cursor.execute("""
            SELECT COUNT(DISTINCT dataId) 
            FROM position 
            WHERE virtual_hr_id = ?
        """, (hr_id,))
        positions_count = cursor.fetchone()[0] or 0
        stats["positions_count"] = positions_count
        
        # 3. 获取与该虚拟HR聊过天的用户数量
        cursor.execute("""
            SELECT COUNT(DISTINCT userid) 
            FROM user 
            WHERE virtual_hr_id = ?
        """, (hr_id,))
        stats["chat_users_count"] = cursor.fetchone()[0] or 0
        
        return stats
    
    except sqlite3.Error as e:
        print(f"获取虚拟HR统计信息失败: {e}")
        return {
            "job_categories_count": 0,
            "positions_count": 0,
            "chat_users_count": 0
        }
    
    finally:
        conn.close()

def get_job_classification_statistics(job_classification_id: str):
    """
    获取统计信息，有多少岗位，有多少用户，有多少场景
    """
    conn = _connect_db()
    if not conn:
        return None
    
    try:
        cursor = conn.cursor()
        # 获取有多少岗位，得到列表
        cursor.execute("SELECT dataId FROM position WHERE job_classification_id = ?", (job_classification_id,))
        positions_ids = cursor.fetchall()
        positions_count = len(positions_ids)
        # 根据岗位，获取有多少用户
        users_count = 0
        for position in positions_ids:
            cursor.execute("SELECT COUNT(DISTINCT userid) FROM user WHERE positionId = ?", (position[0],))
            users_count += cursor.fetchone()[0] or 0
        # 获取有多少场景
        cursor.execute("SELECT COUNT(DISTINCT sceneId) FROM scene WHERE job_classification_id = ?", (job_classification_id,))
        scenes_count = cursor.fetchone()[0] or 0

        state = {
            "positions_count": positions_count,
            "users_count": users_count,
            "scenes_count": scenes_count
        }
        return state
    except sqlite3.Error as e:
        print(f"获取统计信息失败: {e}")
        return None
    finally:
        conn.close()

# ======================== 租户全局设置相关函数 ========================

def upsert_tenant_global_settings(tenant_id: str, updates: dict):
    """
    新增或更新租户全局设置
    如果设置不存在则创建，否则更新
    """
    conn = _connect_db()
    if not conn:
        return False, "数据库连接失败"
    
    try:
        cursor = conn.cursor()
        # 首先检查是否已存在该租户的设置
        cursor.execute("SELECT id FROM tenant_global_settings WHERE tenant_id = ?", (tenant_id,))
        existing = cursor.fetchone()
        
        if existing:
            # 如果存在，则更新
            settings_id = existing[0]
            set_clauses = []
            params = []
            
            for key, value in updates.items():
                if key != "id" and key != "tenant_id":  # 不更新id和tenant_id
                    set_clauses.append(f"{key} = ?")
                    params.append(value)
            
            if set_clauses:
                params.append(settings_id)
                sql = f"UPDATE tenant_global_settings SET {', '.join(set_clauses)} WHERE id = ?"
                cursor.execute(sql, params)
                conn.commit()
                return True, settings_id
        else:
            # 如果不存在，则创建
            settings_id = str(uuid.uuid4())
            created_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            data = {
                "id": settings_id,
                "tenant_id": tenant_id,
                "createdAt": created_at
            }
            # 合并传入的更新数据
            data.update(updates)
            
            columns = ', '.join(data.keys())
            placeholders = ', '.join(['?' for _ in data.keys()])
            values = list(data.values())
            
            sql = f"INSERT INTO tenant_global_settings ({columns}) VALUES ({placeholders})"
            cursor.execute(sql, values)
            conn.commit()
            return True, settings_id
            
        return False, "更新失败"
    except sqlite3.Error as e:
        print(f"更新租户全局设置失败: {e}")
        return False, str(e)
    finally:
        conn.close()

def update_tenant_global_settings(tenant_id: str, updates: dict):
    """
    更新租户全局设置
    """
    conn = _connect_db()
    if not conn:
        return False, "数据库连接失败"
    
    try:
        cursor = conn.cursor()
        # 首先检查是否存在
        cursor.execute("SELECT id FROM tenant_global_settings WHERE tenant_id = ?", (tenant_id,))
        existing = cursor.fetchone()
        
        if not existing:
            return False, "设置不存在"
        
        settings_id = existing[0]
        set_clauses = []
        params = []
        
        for key, value in updates.items():
            if key != "id" and key != "tenant_id":  # 不更新id和tenant_id
                set_clauses.append(f"{key} = ?")
                params.append(value)
        
        if set_clauses:
            params.append(settings_id)
            sql = f"UPDATE tenant_global_settings SET {', '.join(set_clauses)} WHERE id = ?"
            cursor.execute(sql, params)
            conn.commit()
            return True, "更新成功"
            
        return False, "没有可更新的内容"
    except sqlite3.Error as e:
        print(f"更新租户全局设置失败: {e}")
        return False, str(e)
    finally:
        conn.close()

def get_tenant_global_settings(tenant_id: str):
    """
    获取租户全局配置
    """
    conn = _connect_db()
    if not conn:
        return None
    
    try:
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM tenant_global_settings WHERE tenant_id = ?", (tenant_id,))
        settings = cursor.fetchone()
        if not settings:
            return None
        col_names = [description[0] for description in cursor.description]
        settings_dict = {col_names[i]: settings[i] for i in range(len(col_names))}
        return settings_dict
    except sqlite3.Error as e:
        print(f"获取租户全局配置失败: {e}")
        return None
    finally:
        conn.close()

# ======================== 仪表盘统计相关函数 ========================

def get_dashboard_stats(tenant_id: str = None):
    """
    获取仪表盘所需的各项统计数据:
    - 场景数量
    - 职位数量
    - 候选人数量
    - 虚拟HR数量
    - 沟通次数(聊天记录数)
    """
    conn = _connect_db()
    if not conn:
        return {
            "position_count": 0,
            "user_count": 0,
            "scene_count": 0,
            "virtual_hr_count": 0,
            "chat_count": 0
        }
    
    try:
        cursor = conn.cursor()
        result = {}
        
        # 获取职位数量
        if tenant_id:
            cursor.execute("SELECT COUNT(*) FROM position WHERE tenant_id = ?", (tenant_id,))
        else:
            cursor.execute("SELECT COUNT(*) FROM position")
        result["position_count"] = cursor.fetchone()[0] or 0
        
        # 获取用户(候选人)数量
        if tenant_id:
            cursor.execute("SELECT COUNT(*) FROM user WHERE tenant_id = ?", (tenant_id,))
        else:
            cursor.execute("SELECT COUNT(*) FROM user")
        result["user_count"] = cursor.fetchone()[0] or 0
        
        # 获取场景数量
        cursor.execute("SELECT COUNT(*) FROM scene")
        result["scene_count"] = cursor.fetchone()[0] or 0
        
        # 获取虚拟HR数量
        if tenant_id:
            cursor.execute("SELECT COUNT(*) FROM virtual_hr WHERE tenant_id = ?", (tenant_id,))
        else:
            cursor.execute("SELECT COUNT(*) FROM virtual_hr")
        result["virtual_hr_count"] = cursor.fetchone()[0] or 0
        
        # 获取沟通次数(聊天记录数)
        if tenant_id:
            cursor.execute("""
                SELECT COUNT(*) FROM chatlog 
                WHERE userid IN (SELECT userid FROM user WHERE tenant_id = ?)
            """, (tenant_id,))
        else:
            cursor.execute("SELECT COUNT(*) FROM chatlog")
        result["chat_count"] = cursor.fetchone()[0] or 0
        
        return result
    
    except sqlite3.Error as e:
        print(f"获取仪表盘统计数据失败: {e}")
        return {
            "position_count": 0,
            "user_count": 0,
            "scene_count": 0,
            "virtual_hr_count": 0,
            "chat_count": 0
        }
    
    finally:
        conn.close()

def get_daily_stats(tenant_id: str = None, days: int = 7):
    """
    获取每日统计数据变化情况(过去n天)
    - 每日职位数量
    - 每日候选人数量
    """
    conn = _connect_db()
    if not conn:
        return {
            "position_stats": [],
            "user_stats": []
        }
    
    try:
        cursor = conn.cursor()
        result = {"position_stats": [], "user_stats": []}
        
        # 计算起始日期
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # 生成日期列表
        date_list = []
        current_date = start_date
        while current_date <= end_date:
            date_list.append(current_date.strftime("%Y-%m-%d"))
            current_date += timedelta(days=1)
        
        # 获取每日职位数量
        position_stats = []
        for date_str in date_list:
            if tenant_id:
                cursor.execute("""
                    SELECT COUNT(*) FROM position 
                    WHERE tenant_id = ? AND DATE(createdAt) <= ?
                """, (tenant_id, date_str))
            else:
                cursor.execute("""
                    SELECT COUNT(*) FROM position 
                    WHERE DATE(createdAt) <= ?
                """, (date_str,))
            count = cursor.fetchone()[0] or 0
            position_stats.append({"date": date_str, "count": count})
        
        result["position_stats"] = position_stats
        
        # 获取每日用户(候选人)数量
        user_stats = []
        for date_str in date_list:
            if tenant_id:
                cursor.execute("""
                    SELECT COUNT(*) FROM user 
                    WHERE tenant_id = ? AND DATE(createdAt) <= ?
                """, (tenant_id, date_str))
            else:
                cursor.execute("""
                    SELECT COUNT(*) FROM user 
                    WHERE DATE(createdAt) <= ?
                """, (date_str,))
            count = cursor.fetchone()[0] or 0
            user_stats.append({"date": date_str, "count": count})
        
        result["user_stats"] = user_stats
        
        return result
    
    except sqlite3.Error as e:
        print(f"获取每日统计数据失败: {e}")
        return {
            "position_stats": [],
            "user_stats": []
        }
    
    finally:
        conn.close()

def get_recent_new_positions(tenant_id: str = None, days: int = 7):
    """
    获取最近n天新增的职位列表
    """
    conn = _connect_db()
    if not conn:
        return {"positions": [], "count": 0}
    
    try:
        cursor = conn.cursor()
        
        # 计算起始日期
        end_date = datetime.now()
        start_date_str = (end_date - timedelta(days=days)).strftime("%Y-%m-%d")
        
        # 查询最近新增的职位
        if tenant_id:
            cursor.execute("""
                SELECT * FROM position 
                WHERE tenant_id = ? AND DATE(createdAt) >= ? 
                ORDER BY createdAt DESC
            """, (tenant_id, start_date_str))
        else:
            cursor.execute("""
                SELECT * FROM position 
                WHERE DATE(createdAt) >= ? 
                ORDER BY createdAt DESC
            """, (start_date_str,))
        
        rows = cursor.fetchall()
        
        # 格式化结果
        positions = []
        if rows:
            col_names = [description[0] for description in cursor.description]
            for row in rows:
                position_data = {}
                for i, name in enumerate(col_names):
                    position_data[name] = _deserialize_value(row[i]) if name in ["jobIntentions", "expectPositions", "preferredCompanies"] else row[i]
                    if name == "tenant_id":
                        tenant_info = get_tenant_by_id(row[i])
                        position_data["tenantNickname"] = tenant_info["nickname"]
                        position_data["tenantAccount"] = tenant_info["account"]
                    if name == "job_classification_id":
                        job_classification_info = get_job_classification(row[i],"")
                        position_data["classificationName"] = job_classification_info["class_name"]
                positions.append(position_data)
        
        # 返回结果前按key排序
        result = {"positions": positions, "count": len(positions)}
        # 对result字典的键进行排序，确保返回顺序一致
        return dict(sorted(result.items()))
    
    except sqlite3.Error as e:
        print(f"获取最近新增职位失败: {e}")
        return {"positions": [], "count": 0}
    
    finally:
        conn.close()

def get_recent_new_users(tenant_id: str = None, days: int = 7):
    """
    获取最近n天新增的候选人列表
    """
    conn = _connect_db()
    if not conn:
        return {"users": [], "count": 0}
    
    try:
        cursor = conn.cursor()
        
        # 计算起始日期
        end_date = datetime.now()
        start_date_str = (end_date - timedelta(days=days)).strftime("%Y-%m-%d")
        
        # 查询最近新增的用户
        if tenant_id:
            cursor.execute("""
                SELECT u.*, p.positionName FROM user u
                LEFT JOIN position p ON u.positionId = p.dataId
                WHERE u.tenant_id = ? AND DATE(u.createdAt) >= ? 
                ORDER BY u.createdAt DESC
            """, (tenant_id, start_date_str))
        else:
            cursor.execute("""
                SELECT u.*, p.positionName FROM user u
                LEFT JOIN position p ON u.positionId = p.dataId
                WHERE DATE(u.createdAt) >= ? 
                ORDER BY u.createdAt DESC
            """, (start_date_str,))
        
        rows = cursor.fetchall()
        
        # 格式化结果
        users = []
        if rows:
            col_names = [description[0] for description in cursor.description]
            for row in rows:
                user_data = {}
                for i, name in enumerate(col_names):
                    user_data[name] = row[i]
                    if name == "tenant_id":
                        tenant_info = get_tenant_by_id(row[i])
                        user_data["tenantNickname"] = tenant_info["nickname"]
                        user_data["tenantAccount"] = tenant_info["account"]
                users.append(user_data)
        
        # 返回结果前按key排序
        result = {"users": users, "count": len(users)}
        # 对result字典的键进行排序，确保返回顺序一致
        return dict(sorted(result.items()))
    
    except sqlite3.Error as e:
        print(f"获取最近新增候选人失败: {e}")
        return {"users": [], "count": 0}
    
    finally:
        conn.close()

def get_token_usage_by_apikey(apikey: str, start_date: str, end_date: str):
    """
    获取APIKey的token使用情况
    """
    conn = _connect_db()
    if not conn:
        return []
    try:
        cursor = conn.cursor()
        cursor.execute("SELECT apikey, createdAt, uptoken, downtoken FROM apikey_token WHERE apikey = ? AND createdAt >= ? AND createdAt <= ?", (apikey, start_date, end_date))
        rows = cursor.fetchall()
        result = []
        for row in rows:
            result.append({
                "apikey": row[0],
                "createdAt": row[1],
                "uptoken": row[2],
                "downtoken": row[3],
                "total": row[2] + row[3]
            })
        return result
    except sqlite3.Error as e:
        print(f"获取APIKey的token使用情况失败: {e}")
        return []
    finally:
        conn.close()

def get_token_usage_by_tenants(start_date=None, end_date=None, limit=10):
    """
    获取所有租户的Token使用情况，返回使用量最多的前N个租户
    专为管理员用户设计
    """
    conn = _connect_db()
    if not conn:
        return []
    
    try:
        cursor = conn.cursor()
        query = """
            SELECT tenant_id, SUM(uptoken) as total_uptoken, SUM(downtoken) as total_downtoken 
            FROM apikey_token
        """
        params = []
        
        # 添加日期筛选
        if start_date and end_date:
            query += " WHERE createdAt BETWEEN ? AND ?"
            params.extend([start_date, end_date])
        
        query += " GROUP BY tenant_id ORDER BY (total_uptoken + total_downtoken) DESC LIMIT ?"
        params.append(limit)
        
        cursor.execute(query, params)
        rows = cursor.fetchall()
        
        usage_data = []
        for row in rows:
            tenant_id = row[0]
            # 获取租户信息
            tenant_info = get_tenant_by_id(tenant_id)
            if not tenant_info:
                continue
                
            usage_data.append({
                "tenant_id": tenant_id,
                "tenant_name": tenant_info.get("nickname", "未知"),
                "tenant_account": tenant_info.get("account", "未知"),
                "uptoken": row[1],
                "downtoken": row[2],
                "total": row[1] + row[2]
            })
        return usage_data
    except sqlite3.Error as e:
        print(f"获取租户Token使用量失败: {e}")
        return []
    finally:
        conn.close()

def get_token_usage_week_summary(tenant_id: str):
    """
    获取过去7天的Token使用量汇总
    """
    # 计算起始日期和结束日期
    end_date = datetime.now()
    start_date = end_date - timedelta(days=7)
    start_date_str = start_date.strftime("%Y-%m-%d")
    end_date_str = end_date.strftime("%Y-%m-%d")
    
    # 调用已有函数获取Token使用情况
    daily_usage = get_token_usage_daily(tenant_id, start_date_str, end_date_str)
    
    # 如果是管理员用户(tenant_id为None)，则获取租户级别的使用情况
    if tenant_id is None:
        tenant_usage = get_token_usage_by_tenants(start_date_str, end_date_str)
        return {
            "daily_usage": daily_usage,
            "tenant_usage": tenant_usage
        }
    
    # 获取APIKey列表
    apikeys = get_apikeys_by_tenant(tenant_id)
    apikey_usage = []
    
    for apikey_info in apikeys:
        apikey = apikey_info["apikey"]
        name = apikey_info["name"]
        # 获取该APIKey的token使用情况
        apikey_usage_list = get_token_usage_by_apikey(apikey, start_date_str, end_date_str)
        apikey_usage.append({
            "name": name,
            "apikey": apikey,
            "total_usage": sum(usage["total"] for usage in apikey_usage_list)
        })
    
    return {
        "daily_usage": daily_usage,
        "apikey_usage": apikey_usage
    }