#!/usr/bin/env python3
"""
调试定时任务调度器
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from Utils.SchedulerManager import analysis_scheduler, list_all_jobs
from DadabaseControl.DatabaseControl4 import get_scheduled_analysis_list
import time

def check_scheduler_status():
    """检查调度器状态"""
    print("=== 调度器状态检查 ===")
    
    try:
        # 检查调度器是否运行
        if analysis_scheduler.scheduler.running:
            print("✅ 调度器正在运行")
        else:
            print("❌ 调度器未运行")
            return
        
        # 列出所有任务
        jobs = list_all_jobs()
        print(f"当前定时任务数量: {len(jobs)}")
        
        if jobs:
            print("定时任务列表:")
            for i, job in enumerate(jobs):
                print(f"  {i+1}. ID: {job['id']}")
                print(f"     名称: {job['name']}")
                print(f"     下次执行: {job['next_run_time']}")
                print(f"     触发器: {job['trigger']}")
                print()
        else:
            print("❌ 没有找到定时任务")
        
    except Exception as e:
        print(f"检查调度器状态失败: {e}")
        import traceback
        traceback.print_exc()

def check_analysis_configs():
    """检查分析配置"""
    print("=== 分析配置检查 ===")
    
    try:
        # 获取所有租户的分析配置
        test_tenant_ids = ['29d88d7c-b4d6-42d8-97a4-13f4dfb4db54', '4172415f-c0f7-42df-b3bd-8037044be41a']
        
        for tenant_id in test_tenant_ids:
            print(f"\n租户 {tenant_id[:8]}... 的分析配置:")
            analyses = get_scheduled_analysis_list(tenant_id)
            
            if analyses:
                for analysis in analyses:
                    print(f"  - 名称: {analysis['name']}")
                    print(f"    ID: {analysis['id']}")
                    print(f"    执行时间: {analysis['schedule_time']}")
                    print(f"    自动执行: {analysis['is_auto_enabled']}")
                    print(f"    下次执行: {analysis.get('next_execution_time', '未设置')}")
                    print()
            else:
                print("  没有找到分析配置")
        
    except Exception as e:
        print(f"检查分析配置失败: {e}")
        import traceback
        traceback.print_exc()

def test_add_schedule():
    """测试添加定时任务"""
    print("=== 测试添加定时任务 ===")
    
    try:
        # 获取当前时间，设置1分钟后执行
        import datetime
        now = datetime.datetime.now()
        test_time = now + datetime.timedelta(minutes=1)
        schedule_time = test_time.strftime("%H:%M")
        
        print(f"当前时间: {now.strftime('%H:%M:%S')}")
        print(f"测试执行时间: {schedule_time}")
        
        # 添加测试任务
        from Utils.SchedulerManager import add_analysis_schedule
        success = add_analysis_schedule(
            "test_analysis_123", 
            "test_tenant_456", 
            schedule_time, 
            "测试分析任务"
        )
        
        if success:
            print("✅ 测试任务添加成功")
            
            # 等待并检查任务状态
            print("等待任务执行...")
            time.sleep(70)  # 等待70秒
            
            # 检查任务是否执行
            jobs = list_all_jobs()
            test_job = None
            for job in jobs:
                if job['id'] == 'analysis_test_analysis_123':
                    test_job = job
                    break
            
            if test_job:
                print(f"任务状态: {test_job}")
            else:
                print("测试任务已执行或被移除")
        else:
            print("❌ 测试任务添加失败")
        
    except Exception as e:
        print(f"测试添加定时任务失败: {e}")
        import traceback
        traceback.print_exc()

def check_system_time():
    """检查系统时间"""
    print("=== 系统时间检查 ===")
    
    import datetime
    now = datetime.datetime.now()
    print(f"当前系统时间: {now.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"时区: {now.astimezone().tzinfo}")

if __name__ == "__main__":
    print("开始调试定时任务调度器...")
    
    check_system_time()
    check_scheduler_status()
    check_analysis_configs()
    
    # 询问是否要测试添加任务
    response = input("\n是否要测试添加一个1分钟后执行的任务? (y/n): ")
    if response.lower() == 'y':
        test_add_schedule()
    
    print("\n调试完成!")
