from openai import OpenAI
import json
from DadabaseControl.DatabaseControl import *
from DadabaseControl.DatabaseControl2 import *
from DadabaseControl.DatabaseControl3 import *

extract_information_prompt = """
你是一个专业的对话聊天信息提取机器人，可以从聊天数据中提取出用户的姓名手机号等个人信息。

请根据以下对话的上下文，提取出用户的姓名手机号等个人信息。

对话内容：
{conversation}

请返回一个json格式，包含用户的姓名手机号职位名称。json格式如下：

```json
{{
    "name": "张三",
    "phone": "13800138000",
    "position": "职位名称"
}}

如果提取不到信息，请返回空json：

```json
{{
    "name": "",
    "phone": "",
    "position": ""
}}
```

"""

async def extract_information(userid):
    chat_logs = await get_chat_log(userid)
    conversation = ""
    for chat_log in chat_logs:
        conversation += f"{chat_log.get('role')}: {chat_log.get('content')}\n"
    with open("config.json", "r") as f:
        config = json.load(f)
    client = OpenAI(api_key=config["openai_config"]["api_key"], base_url=config["openai_config"]["base_url"])
    response = client.chat.completions.create(
        model=config["openai_config"]["model_name"],
        messages=[{"role": "system", "content": extract_information_prompt.format(conversation=conversation)}, {"role": "user", "content": "请根据对话内容提取出用户的姓名手机号等个人信息。"}],
        response_format={"type": "json_object"}
    )
    result_json = json.loads(response.choices[0].message.content.replace("```json","").replace("```",""))
    print(result_json)
    return result_json

async def get_chat_log(userid):
    chat_logs = get_chatlogs_by_user(userid)
    return chat_logs