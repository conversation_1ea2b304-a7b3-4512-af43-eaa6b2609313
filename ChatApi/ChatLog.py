from DadabaseControl.DatabaseControl import *
from DadabaseControl.DatabaseControl2 import *
from DadabaseControl.DatabaseControl3 import *

def get_user_last_chat_log(user):
    user_id = user.get("userid")
    chat_log = get_chatlogs_by_user(user_id)
    if len(chat_log) > 0:
        last_chat_log = chat_log[-1]
        return last_chat_log
    else:
        return {}
    

def save_chat_log(user,messages,new_score,score_tracking,actions,channel):
    position = get_position(user.get("positionId"),user.get("tenant_id"))
    if position:
        virtual_hr_id = position.get("virtual_hr_id")
    else:
        virtual_hr_id = ""
    for message in messages:
        data = {
            "userid":user.get("userid"),
            "positionId":user.get("positionId"),
            "role":message.get("role"),
            "content":message.get("content"),
            "sceneId":user.get("sceneId"),
            "createdAt":int(message.get("timestamp")),
            "score":new_score,
            "score_tracking":score_tracking,
            "actions":actions,
            "virtual_hr_id":virtual_hr_id,
            "channel":channel
        }
        insert_chatlog(data)
