from DadabaseControl.DatabaseControl import *
from DadabaseControl.DatabaseControl2 import *
from DadabaseControl.DatabaseControl3 import *
from DadabaseControl.DatabaseControl5 import *

def call_action(userid: str,actions: list):
    user = get_user(userid,"")
    result_dict = {}
    for action in actions:
        if action.get("action") == "update_user_info":
            print(f"语意触发器-更新用户信息: {userid} - {action.get('params')}")
            update_user(userid,action.get("params"),user.get("tenant_id"))
        elif action.get("action") == "concerns":
            print(f"语意触发器-记录用户痛点: {userid} - {action.get('params')}")
            if not user["concerns"]:
                user["concerns"] = []
            user["concerns"].append(action.get('params')["concerns"])
            update_user(userid,{"concerns":json.dumps(user["concerns"],ensure_ascii=False)},user.get("tenant_id"))
        elif action.get("action") == "scene_switcher":
            print(f"场景切换器: {userid} - {action.get('params')}")
            positionId = user.get("positionId")
            job_classification_id = get_position(positionId,user.get("tenant_id")).get("job_classification_id")
            target_scene_id = get_scene_id_by_name(action.get("params").get("target_scene"),job_classification_id)
            if target_scene_id:
                score = action.get("params").get("score")
                update_user(userid,{"sceneId":target_scene_id,"score":score},user.get("tenant_id"))
                result_dict["activate_message"] = action.get("params").get("activate_message")
                result_dict["target_scene_id"] = target_scene_id
                result_dict["score"] = score
                print(f"切换成功,{result_dict}")
            else:
                target_scene = action.get("params").get("target_scene")
                print(f"切换失败,{target_scene}-查不到对应的场景,{job_classification_id}")
    return result_dict