from openai import AsyncOpenAI
import json
from DadabaseControl.DatabaseControl import *
from DadabaseControl.DatabaseControl2 import *
from DadabaseControl.DatabaseControl3 import *

async def reply_chat(prompt, messages, apikey=None, tenant_id=None):
    print("messages:", messages)
    with open("config.json", "r") as f:
        config = json.load(f)
    openai_config = config.get("openai_config")
    openai_client = AsyncOpenAI(base_url=openai_config.get("base_url"), api_key=openai_config.get("api_key"))
    
    response = await openai_client.chat.completions.create(
        model=openai_config.get("model_name"),
        messages=[{"role": "system", "content": prompt}] + messages,
    )
    
    # 记录Token使用量
    if apikey and tenant_id:
        prompt_tokens = response.usage.prompt_tokens
        completion_tokens = response.usage.completion_tokens
        insert_apikey_token_usage(tenant_id, apikey, prompt_tokens, completion_tokens)
        print(f"记录Token使用量: prompt={prompt_tokens}, completion={completion_tokens}")
    
    return response.choices[0].message.content


async def get_result_json(userid, prompt, user_message, apikey=None, tenant_id=None):
    messages = await conversion_messages(userid, user_message)
    count = 0
    # 最多重试5次
    while count < 5:
        count += 1
        result = await reply_chat(prompt, messages, apikey, tenant_id)
        print("AI回复:", result)
        if result.startswith("```json"):
            result_json = json.loads(result.replace("```json","").replace("```",""))
            return result_json
        else:
            try:
                result_json = json.loads(result)
                return result_json
            except:
                print("AI回复不是json格式，重新生成:", result)
            continue
    print(f"AI回复不是json格式，重试了5次依旧未得到json格式的返回。报错用户{userid}")
    return None


async def conversion_messages(userid,user_message):
    new_messages = []
    # for message in messages:
    #     if message["role"] == "assistant":
    #         timestamp = message["timestamp"]
    #         chatlog = get_chatlogs_by_userid_timestamp(userid,timestamp)
    #         if chatlog:
    #             new_messages.append({"role": "assistant", "content": json.dumps(chatlog[0]["content"],ensure_ascii=False)})
    #         else:
    #             new_messages.append({"role": "assistant", "content": message["content"]})
    #     elif message["role"] == "user":
    #         new_messages.append({"role": "user", "content": str(message["content"])})
    #     else:
    #         new_messages.append(message)
    chatlogs = get_chatlogs_by_user(userid)
    for chatlog in chatlogs:
        new_messages.append({"role": chatlog.get("role"), "content": json.dumps(chatlog.get("content"),ensure_ascii=False), "timestamp": chatlog.get("createdAt")})
    if user_message.get("role") == "user" and user_message.get("content") == "":
        # 如果用户没有输入内容，则不添加用户消息
        pass
    else:
        new_messages.append(user_message)
    return new_messages