from datetime import datetime

def get_current_time():
    # 获取当前时间格式为2025-07-11 10:00:00 星期几
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    current_week = datetime.now().strftime("%w")
    # current_week是数字，需要转换为中文
    current_week_chinese = {
        "0": "星期日",
        "1": "星期一",
        "2": "星期二",
        "3": "星期三",
        "4": "星期四",
        "5": "星期五",
        "6": "星期六"
    }
    return f"当前时间是{current_time}，今天是{current_week_chinese[current_week]}"