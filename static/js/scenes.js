// 全局变量
let currentJobClassificationId = null;
let jobClassifications = [];
let scenes = [];
let triggers = []; // 分数触发器数组
let semanticTriggers = []; // 语义触发器数组
let semanticClassifiers = []; // 语义分类器数组
let answerClassifiers = []; // 答案分类器数组
let triggersLoaded = false; // 触发器是否已加载标志
let flowchartManager = null; // 流程图管理器
let currentSwitcherData = null; // 当前切换器数据
let currentViewMode = 'flowchart'; // 当前视图模式：'flowchart' 或 'list'

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', () => {
    // 初始化Select2
    initSelect2();
    
    // 加载触发器和分类器
    Promise.all([
        loadTriggers(),
        loadSemanticTriggers(),
        loadSemanticClassifiers(),
        loadAnswerClassifiers()
    ]).then(() => {
        // 加载职位分类列表
        loadJobClassifications();
    });
    
    // 初始化流程图管理器
    initFlowchart();

    // 添加事件监听器
    document.getElementById('addSceneBtn').addEventListener('click', showAddSceneModal);
    document.getElementById('saveSceneBtn').addEventListener('click', saveScene);
    document.getElementById('saveQuestionBtn').addEventListener('click', saveQuestion);
    document.getElementById('updateSceneBtn').addEventListener('click', updateScene);

    // 流程图工具栏事件
    document.getElementById('fitToContent').addEventListener('click', () => {
        if (flowchartManager) flowchartManager.fitToContent();
    });
    document.getElementById('resetZoom').addEventListener('click', () => {
        if (flowchartManager) flowchartManager.resetZoom();
    });
    document.getElementById('toggleSidebar').addEventListener('click', toggleSidebar);
    document.getElementById('sidebarToggle').addEventListener('click', toggleSidebar);
    document.getElementById('fullscreenBtn').addEventListener('click', toggleFullscreen);

    // 视图切换事件
    document.getElementById('flowchartViewBtn').addEventListener('click', () => switchView('flowchart'));
    document.getElementById('listViewBtn').addEventListener('click', () => switchView('list'));

    // 切换器弹窗事件
    document.getElementById('saveSwitcher').addEventListener('click', saveSwitcher);
    
    // 关闭弹窗按钮
    document.querySelectorAll('.close, .btn[data-dismiss="modal"]').forEach(element => {
        element.addEventListener('click', (e) => {
            const modalId = e.target.closest('.modal').id;
            hideModal(modalId);
        });
    });

    // 监听切换器弹窗事件
    document.addEventListener('showSwitcherModal', handleSwitcherModal);

    // 监听流程图右键菜单事件
    document.addEventListener('editScene', (e) => {
        editScene(e.detail);
    });

    document.addEventListener('deleteScene', (e) => {
        if (confirm(`确定要删除场景吗？删除后不可恢复。`)) {
            deleteScene(e.detail);
        }
    });

    document.addEventListener('viewSceneQuestions', (e) => {
        const sceneData = e.detail;
        showAddQuestionModal(sceneData.sceneId);
    });

    document.addEventListener('editSwitcher', (e) => {
        // 可以在这里实现编辑切换器的逻辑
        console.log('编辑切换器:', e.detail);
    });
});

// 初始化流程图
function initFlowchart() {
    // 延迟初始化，确保X6库完全加载
    setTimeout(() => {
        if (typeof X6 !== 'undefined' && X6.Graph) {
            try {
                flowchartManager = new FlowchartManager('x6-container', {
                    width: 800,
                    height: 600
                });
                console.log('流程图管理器初始化成功');
            } catch (error) {
                console.error('流程图管理器初始化失败:', error);
            }
        } else {
            console.error('X6库未加载或加载不完整');
            // 再次尝试初始化
            setTimeout(initFlowchart, 1000);
        }
    }, 100);
}

// 切换侧边栏显示/隐藏
function toggleSidebar() {
    const sidebar = document.getElementById('scenesSidebar');
    sidebar.classList.toggle('collapsed');
}

// 切换全屏模式
function toggleFullscreen() {
    const scenesPanel = document.querySelector('.scenes-panel');
    const fullscreenBtn = document.getElementById('fullscreenBtn');
    const icon = fullscreenBtn.querySelector('i');

    scenesPanel.classList.toggle('fullscreen');

    if (scenesPanel.classList.contains('fullscreen')) {
        icon.className = 'fas fa-compress';
        fullscreenBtn.innerHTML = '<i class="fas fa-compress"></i> 退出全屏';
    } else {
        icon.className = 'fas fa-expand';
        fullscreenBtn.innerHTML = '<i class="fas fa-expand"></i> 全屏';
    }

    // 重新调整流程图大小
    if (flowchartManager && flowchartManager.graph) {
        setTimeout(() => {
            flowchartManager.graph.resize();
        }, 300);
    }
}

// 切换视图模式
function switchView(mode) {
    currentViewMode = mode;

    const flowchartContainer = document.querySelector('.flowchart-container');
    const listView = document.getElementById('scenesListView');
    const flowchartBtn = document.getElementById('flowchartViewBtn');
    const listBtn = document.getElementById('listViewBtn');
    const fitBtn = document.getElementById('fitToContent');
    const resetBtn = document.getElementById('resetZoom');
    const sidebarBtn = document.getElementById('toggleSidebar');

    if (mode === 'flowchart') {
        // 显示流程图视图
        flowchartContainer.style.display = 'block';
        listView.style.display = 'none';

        // 更新按钮状态
        flowchartBtn.classList.add('active');
        listBtn.classList.remove('active');

        // 启用流程图相关按钮
        fitBtn.style.display = 'inline-block';
        resetBtn.style.display = 'inline-block';
        sidebarBtn.style.display = 'inline-block';

        // 重新初始化流程图（如果需要）
        if (flowchartManager && currentJobClassificationId) {
            flowchartManager.loadScenes(currentJobClassificationId);
        }
    } else {
        // 显示列表视图
        flowchartContainer.style.display = 'none';
        listView.style.display = 'block';

        // 更新按钮状态
        flowchartBtn.classList.remove('active');
        listBtn.classList.add('active');

        // 隐藏流程图相关按钮
        fitBtn.style.display = 'none';
        resetBtn.style.display = 'none';
        sidebarBtn.style.display = 'none';

        // 渲染传统列表视图
        renderScenesList();
    }
}

// 移除切换器类型改变事件，现在分数条件和语义条件可以同时设置

// 处理切换器弹窗显示
function handleSwitcherModal(event) {
    console.log('handleSwitcherModal called with event:', event);

    const { sourceScene, targetScene, onConfirm } = event.detail;

    console.log('Handle switcher modal:', sourceScene, targetScene);
    console.log('onConfirm function:', onConfirm);

    // 显示弹窗
    console.log('Attempting to show switcherModal');
    showModal('switcherModal');

    // 延迟设置弹窗数据，确保弹窗已经显示
    setTimeout(() => {
        // 先重置表单（除了场景名称字段）
        const form = document.getElementById('switcherForm');
        const sourceInput = document.getElementById('sourceSceneName');
        const targetInput = document.getElementById('targetSceneName');

        // 保存场景名称
        const sourceName = sourceScene && sourceScene.sceneName ? sourceScene.sceneName : '未知场景';
        const targetName = targetScene && targetScene.sceneName ? targetScene.sceneName : '未知场景';

        // 重置表单
        form.reset();

        // 重新设置场景名称
        if (sourceInput) {
            sourceInput.value = sourceName;
            console.log('Set source scene name:', sourceName);
        } else {
            console.error('Source scene input not found');
        }

        if (targetInput) {
            targetInput.value = targetName;
            console.log('Set target scene name:', targetName);
        } else {
            console.error('Target scene input not found');
        }

        console.log('Form setup complete');
    }, 100);

    // 保存回调函数
    currentSwitcherData = {
        sourceScene,
        targetScene,
        onConfirm
    };
}

// 保存切换器
async function saveSwitcher() {
    if (!currentSwitcherData) return;

    const form = document.getElementById('switcherForm');
    const formData = new FormData(form);

    const switcherData = {
        target_score: parseInt(formData.get('target_score')) || 0,
        user_message: formData.get('user_message') || ''
    };

    // 分数条件（可选）
    const symbol = formData.get('symbol');
    const score = formData.get('score');
    if (symbol && score) {
        switcherData.symbol = symbol;
        switcherData.score = parseInt(score);
    }

    // 语义条件（可选）
    const semanticCondition = formData.get('semantic_condition');
    if (semanticCondition && semanticCondition.trim()) {
        switcherData.semantic_condition = semanticCondition.trim();
    }

    // 至少需要设置一个条件
    if (!switcherData.symbol && !switcherData.semantic_condition) {
        alert('请至少设置一个切换条件（分数条件或语义条件）');
        return;
    }

    try {
        // 调用回调函数
        await currentSwitcherData.onConfirm(switcherData);

        // 隐藏弹窗
        hideModal('switcherModal');

        // 清空数据
        currentSwitcherData = null;

        showSuccess('场景切换器创建成功');
    } catch (error) {
        console.error('保存切换器失败:', error);
        showError('保存切换器失败，请重试');
    }
}

// 初始化Select2
function initSelect2() {
    // 添加场景模态框中的select2初始化
    $('#score_trigger_ids').select2({
        placeholder: '选择分数触发器',
        allowClear: true
    });
    
    $('#semantic_classifier_ids').select2({
        placeholder: '选择语义分类器',
        allowClear: true
    });
    
    $('#semantic_trigger_ids').select2({
        placeholder: '选择语义触发器',
        allowClear: true
    });
    
    // 编辑场景模态框中的select2初始化
    $('#editScoreTriggerIds').select2({
        placeholder: '选择分数触发器',
        allowClear: true
    });
    
    $('#editSemanticClassifierIds').select2({
        placeholder: '选择语义分类器',
        allowClear: true
    });
    
    $('#editSemanticTriggerIds').select2({
        placeholder: '选择语义触发器',
        allowClear: true
    });
}

// 加载所有职位分类
async function loadJobClassifications() {
    try {
        const token = localStorage.getItem('accessToken');
        const response = await fetch('/web_job_classifications',{
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        const result = await response.json();
        jobClassifications = result.data;
        
        renderJobClassificationsList();
    } catch (error) {
        console.error('加载职位分类列表失败:', error);
        showError('加载职位分类列表失败，请刷新页面重试。');
    }
}

// 渲染职位分类列表
function renderJobClassificationsList() {
    const jobClassificationsList = document.getElementById('positionsList');
    
    if (jobClassifications.length === 0) {
        jobClassificationsList.innerHTML = '<div class="no-positions-message">没有找到职位分类数据</div>';
        return;
    }
    
    // 清空加载中的提示
    jobClassificationsList.innerHTML = '';
    
    // 为每个职位分类创建列表项
    jobClassifications.forEach(jobClassification => {
        const jobClassificationItem = document.createElement('div');
        jobClassificationItem.className = 'position-item';
        jobClassificationItem.dataset.jobClassificationId = jobClassification.id;
        
        if (currentJobClassificationId === jobClassification.id) {
            jobClassificationItem.classList.add('active');
        }
        
        // 查询该职位分类有多少场景
        const token = localStorage.getItem('accessToken');
        fetch(`/scenes/count?job_classification_id=${jobClassification.id}`,{
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        })
            .then(response => response.json())
            .then(data => {
                jobClassificationItem.innerHTML = `
                    <div class="position-item-name">${jobClassification.class_name}</div>
                    <div class="position-item-count">${data.count || 0}</div>
                `;
            })
            .catch(error => {
                jobClassificationItem.innerHTML = `
                    <div class="position-item-name">${jobClassification.class_name}</div>
                    <div class="position-item-count">0</div>
                `;
            });
        
        jobClassificationItem.addEventListener('click', () => selectJobClassification(jobClassification.id));
        jobClassificationsList.appendChild(jobClassificationItem);
    });
}

// 选择职位分类
function selectJobClassification(jobClassificationId) {
    // 如果当前已经选择了这个职位分类，则不做操作
    if (currentJobClassificationId === jobClassificationId) return;

    currentJobClassificationId = jobClassificationId;

    // 更新UI
    document.querySelectorAll('.position-item').forEach(item => {
        item.classList.toggle('active', item.dataset.jobClassificationId === jobClassificationId);
    });

    // 更新右侧标题
    const jobClassification = jobClassifications.find(jc => jc.id === jobClassificationId);
    if (jobClassification) {
        document.getElementById('currentPositionName').textContent = jobClassification.class_name;
        document.getElementById('addSceneBtn').disabled = false;
        document.getElementById('flowchartViewBtn').disabled = false;
        document.getElementById('listViewBtn').disabled = false;
        document.getElementById('fitToContent').disabled = false;
        document.getElementById('resetZoom').disabled = false;
        document.getElementById('toggleSidebar').disabled = false;
        document.getElementById('fullscreenBtn').disabled = false;
    }

    // 隐藏空状态提示
    document.getElementById('noPositionSelected').style.display = 'none';

    // 加载该职位分类的场景到流程图
    loadScenesForFlowchart(jobClassificationId);
}

// 加载场景数据到流程图
async function loadScenesForFlowchart(jobClassificationId) {
    try {
        // 显示加载状态
        document.getElementById('flowchartLoading').style.display = 'block';

        // 确保触发器已加载
        if (!triggersLoaded) {
            await loadTriggers();
        }

        // 加载场景数据
        const token = localStorage.getItem('accessToken');
        const response = await fetch(`/scenes/job_classification/${jobClassificationId}`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        scenes = await response.json();

        console.log("加载的场景数据:", scenes);

        // 根据当前视图模式渲染内容
        if (currentViewMode === 'flowchart') {
            // 渲染侧边栏场景列表
            renderDraggableScenesList();

            // 加载流程图
            if (flowchartManager) {
                await flowchartManager.loadScenes(jobClassificationId);
            }
        } else {
            // 渲染传统列表视图
            renderScenesList();
        }

        // 隐藏加载状态
        document.getElementById('flowchartLoading').style.display = 'none';

    } catch (error) {
        console.error('加载场景列表失败:', error);
        showError('加载场景列表失败，请刷新页面重试。');
        document.getElementById('flowchartLoading').style.display = 'none';
    }
}

// 渲染可拖拽的场景列表
function renderDraggableScenesList() {
    const container = document.getElementById('draggableScenesList');
    container.innerHTML = '';

    if (scenes.length === 0) {
        container.innerHTML = `
            <div class="text-center p-3 text-muted">
                <i class="fas fa-inbox fa-2x mb-2"></i>
                <p>暂无场景</p>
                <small>点击"添加场景"创建第一个场景</small>
            </div>
        `;
        return;
    }

    scenes.forEach(scene => {
        const sceneItem = document.createElement('div');
        sceneItem.className = 'draggable-scene-item';
        sceneItem.draggable = true;
        sceneItem.dataset.sceneId = scene.sceneId;

        const isDefault = scene.isDefault === 1;
        const scoreRange = scene.scoreRange ? `${scene.scoreRange[0]}-${scene.scoreRange[1]}` : '未设置';

        sceneItem.innerHTML = `
            <div class="scene-item-header">
                <div class="scene-item-toggle">
                    <i class="fas fa-chevron-right"></i>
                </div>
                <div class="scene-item-info">
                    <div class="scene-item-name">${scene.sceneName}</div>
                    <div class="scene-item-desc">${scene.sceneDescription || '暂无描述'}</div>
                    <div class="scene-item-meta">
                        <span>分数: ${scoreRange}</span>
                        ${isDefault ? '<span class="scene-item-default">默认</span>' : ''}
                    </div>
                </div>
            </div>
            <div class="scene-item-content">
                <div class="scene-item-questions" id="sidebarQuestions-${scene.sceneId}">
                    <div class="questions-loading">加载问题中...</div>
                </div>
                <div class="scene-item-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="showAddQuestionModal('${scene.sceneId}')">
                        <i class="fas fa-plus"></i> 添加问题
                    </button>
                </div>
            </div>
        `;

        // 移除拖拽功能
        sceneItem.draggable = false;

        // 添加展开/收起功能
        const toggle = sceneItem.querySelector('.scene-item-toggle');
        const content = sceneItem.querySelector('.scene-item-content');
        const header = sceneItem.querySelector('.scene-item-header');

        header.addEventListener('click', (e) => {
            // 如果点击的是拖拽区域，不触发展开/收起
            if (e.target.closest('.scene-item-info')) return;

            const isExpanded = content.classList.contains('expanded');
            content.classList.toggle('expanded');
            toggle.classList.toggle('expanded');

            // 如果展开且还没加载问题，则加载问题
            if (!isExpanded && !content.dataset.questionsLoaded) {
                loadSidebarQuestions(scene.sceneId);
                content.dataset.questionsLoaded = 'true';
            }
        });

        container.appendChild(sceneItem);
    });
}

// 加载侧边栏场景的问题
async function loadSidebarQuestions(sceneId) {
    try {
        const token = localStorage.getItem('accessToken');
        const response = await fetch(`/scene_questions/${sceneId}`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        const questions = await response.json();

        const questionsContainer = document.getElementById(`sidebarQuestions-${sceneId}`);
        if (!questionsContainer) return;

        if (questions.length === 0) {
            questionsContainer.innerHTML = '<div class="no-questions">暂无问题</div>';
            return;
        }

        questionsContainer.innerHTML = questions.map((question, index) => `
            <div class="sidebar-question-item" data-question-id="${question.questionId}">
                <div class="question-drag-handle">
                    <i class="fas fa-grip-vertical"></i>
                </div>
                <div class="question-info">
                    <div class="question-content">${question.questionContent}</div>
                    <div class="question-score">分数: ${question.questionScore}</div>
                </div>
                <div class="question-actions">
                    <button class="question-action-btn delete-question" title="删除问题">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');

        // 初始化排序功能
        if (typeof Sortable !== 'undefined') {
            new Sortable(questionsContainer, {
                handle: '.question-drag-handle',
                animation: 150,
                onEnd: function(evt) {
                    reorderSidebarQuestions(sceneId, questionsContainer);
                }
            });
        }

        // 添加问题操作事件
        questionsContainer.addEventListener('click', (e) => {
            const questionItem = e.target.closest('.sidebar-question-item');
            if (!questionItem) return;

            const questionId = questionItem.dataset.questionId;

            if (e.target.closest('.edit-question')) {
                editQuestion(questionId);
            } else if (e.target.closest('.delete-question')) {
                if (confirm('确定要删除这个问题吗？')) {
                    deleteQuestion(questionId);
                }
            }
        });

    } catch (error) {
        console.error('加载侧边栏问题失败:', error);
        const questionsContainer = document.getElementById(`sidebarQuestions-${sceneId}`);
        if (questionsContainer) {
            questionsContainer.innerHTML = '<div class="error-message">加载失败</div>';
        }
    }
}

// 重新排序侧边栏问题
async function reorderSidebarQuestions(sceneId, container) {
    try {
        const questionItems = container.querySelectorAll('.sidebar-question-item');
        const questionIds = Array.from(questionItems).map(item => item.dataset.questionId);

        const token = localStorage.getItem('accessToken');
        const response = await fetch('/scene_questions/reorder', {
            method: 'PUT',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                scene_id: sceneId,
                question_ids: questionIds
            })
        });

        const result = await response.json();
        if (result.code !== 200) {
            console.error('重新排序失败:', result.message);
        }
    } catch (error) {
        console.error('重新排序问题失败:', error);
    }
}

// 加载职位分类的场景（保留原有功能，用于兼容）
async function loadScenes(jobClassificationId) {
    // 直接调用新的流程图加载函数
    await loadScenesForFlowchart(jobClassificationId);
}

// 渲染场景列表
function renderScenesList() {
    const scenesList = document.getElementById('scenesList');
    
    if (!currentJobClassificationId) {
        scenesList.innerHTML = '<div class="no-position-selected">请从左侧选择一个职位分类</div>';
        return;
    }
    
    if (scenes.length === 0) {
        scenesList.innerHTML = '<div class="no-scenes-message">该职位分类下暂无场景，请点击"添加场景"按钮创建。</div>';
        return;
    }
    
    // 清空列表
    scenesList.innerHTML = '';
    
    // 为每个场景创建列表项
    scenes.forEach(scene => {
        const sceneItem = document.createElement('div');
        sceneItem.className = 'scene-item';
        sceneItem.dataset.sceneId = scene.sceneId;
        
        // 场景头部
        const sceneHeader = document.createElement('div');
        sceneHeader.className = 'scene-header';
        sceneHeader.innerHTML = `
            <div class="scene-title">
                <div class="scene-toggle">
                    <i class="fas fa-chevron-down"></i>
                </div>
                <h4>${scene.sceneName} ${scene.isDefault ? '<span class="default-scene-badge">默认</span>' : ''}</h4>
            </div>
            <div class="scene-actions">
                <div class="scene-action edit" title="编辑场景">
                    <i class="fas fa-edit"></i>
                </div>
                <div class="scene-action delete" title="删除场景">
                    <i class="fas fa-trash-alt"></i>
                </div>
            </div>
        `;
        
        // 场景内容区域
        const sceneContent = document.createElement('div');
        sceneContent.className = 'scene-content';
        
        // 获取多选分数触发器信息
        let scoreTriggerIdsInfo = '未设置';
        if (scene.score_trigger_ids && scene.score_trigger_ids.length > 0) {
            const selectedTriggers = scene.score_trigger_ids.map(id => {
                const trigger = triggers.find(t => t.id === id);
                return trigger ? trigger.score_trigger_name : `ID: ${id}`;
            });
            scoreTriggerIdsInfo = selectedTriggers.join(', ');
        }
        
        // 获取语义分类器信息
        let semanticClassifierInfo = '未设置';
        if (scene.semantic_classifier_ids && scene.semantic_classifier_ids.length > 0) {
            const selectedClassifiers = scene.semantic_classifier_ids.map(id => {
                const classifier = semanticClassifiers.find(c => c.id === id);
                return classifier ? classifier.name : `ID: ${id}`;
            });
            semanticClassifierInfo = selectedClassifiers.join(', ');
        }
        
        // 获取语义触发器信息
        let semanticTriggerInfo = '未设置';
        if (scene.semantic_trigger_ids && scene.semantic_trigger_ids.length > 0) {
            const selectedTriggers = scene.semantic_trigger_ids.map(id => {
                const trigger = semanticTriggers.find(t => t.id === id);
                return trigger ? trigger.semantic_trigger_name : `ID: ${id}`;
            });
            semanticTriggerInfo = selectedTriggers.join(', ');
        }
        
        // 获取答案分类器信息
        let answerClassifierInfo = '未设置';
        if (scene.answer_classifier_id) {
            const classifier = answerClassifiers.find(c => c.id === scene.answer_classifier_id);
            if (classifier) {
                answerClassifierInfo = classifier.name;
            } else {
                answerClassifierInfo = `ID: ${scene.answer_classifier_id} (未找到详情)`;
            }
        }
        
        // 场景信息 - 使用表格布局
        const sceneInfo = document.createElement('div');
        sceneInfo.className = 'scene-info';
        
        // 创建表格布局
        sceneInfo.innerHTML = `
            <div class="scene-info-row">
                <div class="scene-info-col">
                    <table class="scene-info-table">
                        <tr>
                            <td>场景描述:</td>
                            <td>${scene.sceneDescription || '无'}</td>
                        </tr>
                        <tr>
                            <td>分数范围:</td>
                            <td>${getScoreRange(scene.scoreRange)}</td>
                        </tr>
                        <tr>
                            <td>语义分类器:</td>
                            <td>${scene.enable_semantic_classifier === 1 ? '启用' : '禁用'}</td>
                        </tr>
                        <tr>
                            <td>答案分类器:</td>
                            <td>${scene.enable_answer_classifier === 1 ? '启用' : '禁用'}</td>
                        </tr>
                        <tr>
                            <td>分数触发器状态:</td>
                            <td>${scene.enable_score_trigger === 1 ? '启用' : '禁用'}</td>
                        </tr>
                        <tr>
                            <td>语意触发器状态:</td>
                            <td>${scene.enable_semantic_trigger === 1 ? '启用' : '禁用'}</td>
                        </tr>
                    </table>
                </div>
                <div class="scene-info-col">
                    <table class="scene-info-table">
                        <tr>
                            <td>允许回退:</td>
                            <td>${scene.allowed_to_fall_back === 1 ? '是' : '否'}</td>
                        </tr>
                        <tr>
                            <td>分数触发器:</td>
                            <td>${scoreTriggerIdsInfo}</td>
                        </tr>
                        <tr>
                            <td>语义分类器列表:</td>
                            <td>${semanticClassifierInfo}</td>
                        </tr>
                        <tr>
                            <td>语义触发器列表:</td>
                            <td>${semanticTriggerInfo}</td>
                        </tr>
                        <tr>
                            <td>答案分类器:</td>
                            <td>${answerClassifierInfo}</td>
                        </tr>
                    </table>
                </div>
            </div>
        `;
        
        // 问题列表区域
        const sceneQuestions = document.createElement('div');
        sceneQuestions.className = 'scene-questions';
        sceneQuestions.innerHTML = `
            <div class="scene-questions-header">
                <div class="scene-questions-title">面试问题</div>
            </div>
            <div class="questions-list" id="questionsList-${scene.sceneId}">
                <div class="questions-loading">加载中...</div>
            </div>
            <div class="add-question-btn" data-scene-id="${scene.sceneId}">
                <i class="fas fa-plus-circle"></i> 添加问题
            </div>
        `;
        
        // 组合场景内容
        sceneContent.appendChild(sceneInfo);
        sceneContent.appendChild(sceneQuestions);
        
        // 组合整个场景项
        sceneItem.appendChild(sceneHeader);
        sceneItem.appendChild(sceneContent);
        scenesList.appendChild(sceneItem);
        
        // 为该场景加载问题
        loadQuestions(scene.sceneId);
        
        // 添加场景的事件监听器
        sceneHeader.addEventListener('click', (e) => {
            // 如果点击的是操作按钮，不展开/收起
            if (e.target.closest('.scene-actions')) return;
            
            const toggle = sceneHeader.querySelector('.scene-toggle');
            const content = sceneItem.querySelector('.scene-content');
            
            toggle.classList.toggle('expanded');
            content.classList.toggle('expanded');
        });
        
        // 编辑场景按钮
        sceneItem.querySelector('.scene-action.edit').addEventListener('click', () => {
            editScene(scene);
        });
        
        // 删除场景按钮
        sceneItem.querySelector('.scene-action.delete').addEventListener('click', () => {
            if (confirm(`确定要删除场景"${scene.sceneName}"吗？删除后不可恢复。`)) {
                deleteScene(scene.sceneId);
            }
        });
        
        // 添加问题按钮
        sceneItem.querySelector('.add-question-btn').addEventListener('click', () => {
            showAddQuestionModal(scene.sceneId);
        });
    });
}

function deleteScene(sceneId) {
    const token = localStorage.getItem('accessToken');
    fetch(`/scene/${sceneId}`,{
        method: 'DELETE',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            showSuccess('删除场景成功');
            // 重新加载场景列表
            loadScenes(currentJobClassificationId);
            // 更新左侧职位分类场景数
            loadJobClassifications();
        } else {
            showError(data.message);
        }
    })
    .catch(error => {
        console.error('删除场景失败:', error);
        showError('删除场景失败，请刷新页面重试。');
    });
}
// 获取分数范围的显示文本
function getScoreRange(scoreRange) {
    if (!scoreRange) return '未设置';
    
    // 如果已经是数组，直接使用
    if (Array.isArray(scoreRange) && scoreRange.length >= 2) {
        return `${scoreRange[0]} - ${scoreRange[1]}`;
    }
    
    // 如果是字符串，尝试解析
    try {
        const range = JSON.parse(scoreRange);
        if (Array.isArray(range) && range.length >= 2) {
            return `${range[0]} - ${range[1]}`;
        } else {
            return String(scoreRange); // 转换为字符串
        }
    } catch (e) {
        console.warn('解析分数范围失败:', e, scoreRange);
        return String(scoreRange); // 如果解析失败，直接返回原字符串
    }
}

// 加载场景的问题
async function loadQuestions(sceneId) {
    try {
        const token = localStorage.getItem('accessToken');
        const response = await fetch(`/scene_questions/${sceneId}`,{
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        const questions = await response.json();
        
        renderQuestionsList(sceneId, questions);
    } catch (error) {
        console.error(`加载场景 ${sceneId} 的问题失败:`, error);
        document.getElementById(`questionsList-${sceneId}`).innerHTML = '<div class="error-message">加载问题失败</div>';
    }
}

// 渲染问题列表
function renderQuestionsList(sceneId, questions) {
    const questionsList = document.getElementById(`questionsList-${sceneId}`);
    
    if (questions.length === 0) {
        questionsList.innerHTML = '<div class="no-questions-message">暂无问题，请添加。</div>';
        return;
    }
    
    // 清空列表
    questionsList.innerHTML = '';
    
    // 创建问题容器，用于拖拽排序
    const questionsContainer = document.createElement('div');
    questionsContainer.className = 'questions-sortable-container';
    questionsContainer.dataset.sceneId = sceneId;
    
    // 为每个问题创建列表项
    questions.forEach((question, index) => {
        const questionItem = document.createElement('div');
        questionItem.className = 'question-item';
        questionItem.dataset.questionId = question.questionId;
        
        // 显示序号
        const seq = question.seq || (index + 1);
        
        questionItem.innerHTML = `
            <div class="question-drag-handle" title="拖动排序">
                <i class="fas fa-grip-lines"></i>
            </div>
            <div class="question-sequence">${seq}.</div>
            <div class="question-content">${question.questionContent}</div>
            <div class="question-score">${question.questionScore}分</div>
            <div class="question-action" title="删除问题">
                <i class="fas fa-times"></i>
            </div>
        `;
        
        // 删除问题按钮
        questionItem.querySelector('.question-action').addEventListener('click', (e) => {
            e.stopPropagation();
            if (confirm('确定要删除这个问题吗？')) {
                deleteQuestion(question.questionId, sceneId);
            }
        });
        
        questionsContainer.appendChild(questionItem);
    });
    
    questionsList.appendChild(questionsContainer);
    
    // 初始化拖拽排序
    initSortable(sceneId);
}

// 初始化拖拽排序
function initSortable(sceneId) {
    const container = document.querySelector(`.questions-sortable-container[data-scene-id="${sceneId}"]`);
    if (!container) return;
    
    // 确保已引入Sortable.js库
    if (typeof Sortable === 'undefined') {
        // 如果页面中没有引入Sortable.js，则动态加载它
        const script = document.createElement('script');
        script.src = '/static/js/Sortable.min.js';
        script.onload = () => {
            initSortableInstance(container, sceneId);
        };
        document.head.appendChild(script);
    } else {
        // 如果已经引入了Sortable.js，则直接初始化
        initSortableInstance(container, sceneId);
    }
}

// 初始化Sortable实例
function initSortableInstance(container, sceneId) {
    Sortable.create(container, {
        animation: 150,
        handle: '.question-drag-handle',
        onEnd: function(evt) {
            updateQuestionOrder(sceneId);
        }
    });
}

// 更新问题顺序
async function updateQuestionOrder(sceneId) {
    const container = document.querySelector(`.questions-sortable-container[data-scene-id="${sceneId}"]`);
    if (!container) return;
    
    // 获取所有问题项
    const questionItems = container.querySelectorAll('.question-item');
    const updateData = [];
    
    // 为每个问题构建更新数据
    questionItems.forEach((item, index) => {
        updateData.push({
            questionId: item.dataset.questionId,
            seq: index + 1
        });
        
        // 更新显示的序号
        const seqElement = item.querySelector('.question-sequence');
        if (seqElement) {
            seqElement.textContent = `${index + 1}.`;
        }
    });
    
    try {
        // 发送请求更新问题顺序
        const token = localStorage.getItem('accessToken');
        const response = await fetch('/scene_questions/reorder', {
            method: 'PUT',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(updateData)
        });
        
        const result = await response.json();
        
        if (result.code === 200) {
            showSuccess('问题顺序更新成功');
        } else {
            throw new Error(result.message || '更新问题顺序失败');
        }
    } catch (error) {
        console.error('更新问题顺序失败:', error);
        showError('更新问题顺序失败: ' + error.message);
    }
}

// 加载所有分数触发器
async function loadTriggers() {
    try {
        const token = localStorage.getItem('accessToken');
        const response = await fetch('/score-triggers',{
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        const result = await response.json();
        
        if (result.code === 200 && result.data) {
            triggers = result.data;
        } else if (Array.isArray(result)) {
            triggers = result;
        } else {
            triggers = [];
            console.warn("Unexpected API response format:", result);
        }
        
        console.log("加载的分数触发器数据:", triggers); // 调试日志
        
        // 更新触发器下拉框
        updateDropdowns();
        
        triggersLoaded = true;
        return triggers;
    } catch (error) {
        console.error('加载分数触发器列表失败:', error);
        showError('加载分数触发器列表失败，请刷新页面重试。');
        triggersLoaded = false;
        return [];
    }
}

// 加载所有语义触发器
async function loadSemanticTriggers() {
    try {
        const token = localStorage.getItem('accessToken');
        const response = await fetch('/semantic-triggers',{
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        const result = await response.json();
        
        if (result.code === 200 && result.data) {
            semanticTriggers = result.data;
        } else if (result.success === true && result.data) {
            // 处理 success 字段格式的响应
            semanticTriggers = result.data;
        } else if (Array.isArray(result)) {
            // 处理直接返回数组的情况
            semanticTriggers = result;
        } else {
            semanticTriggers = [];
            console.warn("Unexpected API response format for semantic triggers:", result);
        }
        
        console.log("加载的语义触发器数据:", semanticTriggers); // 调试日志
        
        // 更新下拉框
        updateDropdowns();
        
        return semanticTriggers;
    } catch (error) {
        console.error('加载语义触发器列表失败:', error);
        showError('加载语义触发器列表失败，请刷新页面重试。');
        return [];
    }
}

// 加载所有语义分类器
async function loadSemanticClassifiers() {
    try {
        const token = localStorage.getItem('accessToken');
        const response = await fetch('/semantic-classifiers',{
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        const result = await response.json();
        
        if (result.code === 200 && result.data) {
            semanticClassifiers = result.data;
        } else if (result.success === true && result.data) {
            // 处理 success 字段格式的响应
            semanticClassifiers = result.data;
        } else if (Array.isArray(result)) {
            // 处理直接返回数组的情况
            semanticClassifiers = result;
        } else {
            semanticClassifiers = [];
            console.warn("Unexpected API response format for semantic classifiers:", result);
        }
        
        console.log("加载的语义分类器数据:", semanticClassifiers); // 调试日志
        
        // 更新下拉框
        updateDropdowns();
        
        return semanticClassifiers;
    } catch (error) {
        console.error('加载语义分类器列表失败:', error);
        showError('加载语义分类器列表失败，请刷新页面重试。');
        return [];
    }
}

// 加载所有答案分类器
async function loadAnswerClassifiers() {
    try {
        const token = localStorage.getItem('accessToken');
        const response = await fetch('/answer-classifiers',{
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        const result = await response.json();
        
        if (result.code === 200 && result.data) {
            answerClassifiers = result.data;
        } else if (result.success === true && result.data) {
            // 处理 success 字段格式的响应
            answerClassifiers = result.data;
        } else if (Array.isArray(result)) {
            // 处理直接返回数组的情况
            answerClassifiers = result;
        } else {
            answerClassifiers = [];
            console.warn("Unexpected API response format for answer classifiers:", result);
        }
        
        console.log("加载的答案分类器数据:", answerClassifiers); // 调试日志
        
        // 更新下拉框
        updateDropdowns();
        
        return answerClassifiers;
    } catch (error) {
        console.error('加载答案分类器列表失败:', error);
        showError('加载答案分类器列表失败，请刷新页面重试。');
        return [];
    }
}

// 删除问题
async function deleteQuestion(questionId, sceneId) {
    try {
        // 发送请求删除问题
        const token = localStorage.getItem('accessToken');
        const response = await fetch(`/scene_question/${questionId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        const result = await response.json();
        
        if (result.code === 200) {
            // 重新加载问题列表
            loadQuestions(sceneId);
            showSuccess('删除问题成功');
        } else {
            throw new Error(result.message || '删除问题失败');
        }
    } catch (error) {
        console.error('删除问题失败:', error);
        showError('删除问题失败: ' + error.message);
    }
}

// 更新所有下拉框
function updateDropdowns() {
    // 更新分数触发器下拉框
    updateScoreTriggerDropdowns();
    
    // 更新语义触发器下拉框
    updateSemanticTriggerDropdowns();
    
    // 更新语义分类器下拉框
    updateSemanticClassifierDropdowns();
    
    // 更新答案分类器下拉框
    updateAnswerClassifierDropdowns();
}

// 更新分数触发器下拉框
function updateScoreTriggerDropdowns() {
    const addMultiDropdown = document.getElementById('score_trigger_ids');
    const editMultiDropdown = document.getElementById('editScoreTriggerIds');
    
    // 更新多选下拉框
    if (addMultiDropdown) {
        // 清空现有选项
        $(addMultiDropdown).empty();
        
        // 添加触发器选项
        triggers.forEach(trigger => {
            const option = document.createElement('option');
            option.value = trigger.id;
            option.textContent = `${trigger.score_trigger_name} (分数: ${trigger.score})`;
            addMultiDropdown.appendChild(option);
        });
    }
    
    if (editMultiDropdown) {
        // 清空现有选项
        $(editMultiDropdown).empty();
        
        // 添加触发器选项
        triggers.forEach(trigger => {
            const option = document.createElement('option');
            option.value = trigger.id;
            option.textContent = `${trigger.score_trigger_name} (分数: ${trigger.score})`;
            editMultiDropdown.appendChild(option);
        });
    }
}

// 更新语义触发器下拉框
function updateSemanticTriggerDropdowns() {
    const addDropdown = document.getElementById('semantic_trigger_ids');
    const editDropdown = document.getElementById('editSemanticTriggerIds');
    
    if (addDropdown && semanticTriggers.length > 0) {
        // 清空现有选项
        $(addDropdown).empty();
        
        // 添加语义触发器选项
        semanticTriggers.forEach(trigger => {
            const option = document.createElement('option');
            option.value = trigger.id;
            option.textContent = trigger.semantic_trigger_name;
            addDropdown.appendChild(option);
        });
    }
    
    if (editDropdown && semanticTriggers.length > 0) {
        // 清空现有选项
        $(editDropdown).empty();
        
        // 添加语义触发器选项
        semanticTriggers.forEach(trigger => {
            const option = document.createElement('option');
            option.value = trigger.id;
            option.textContent = trigger.semantic_trigger_name;
            editDropdown.appendChild(option);
        });
    }
}

// 更新语义分类器下拉框
function updateSemanticClassifierDropdowns() {
    const addDropdown = document.getElementById('semantic_classifier_ids');
    const editDropdown = document.getElementById('editSemanticClassifierIds');
    
    if (addDropdown && semanticClassifiers.length > 0) {
        // 清空现有选项
        $(addDropdown).empty();
        
        // 添加语义分类器选项
        semanticClassifiers.forEach(classifier => {
            const option = document.createElement('option');
            option.value = classifier.id;
            option.textContent = classifier.name;
            addDropdown.appendChild(option);
        });
    }
    
    if (editDropdown && semanticClassifiers.length > 0) {
        // 清空现有选项
        $(editDropdown).empty();
        
        // 添加语义分类器选项
        semanticClassifiers.forEach(classifier => {
            const option = document.createElement('option');
            option.value = classifier.id;
            option.textContent = classifier.name;
            editDropdown.appendChild(option);
        });
    }
}

// 更新答案分类器下拉框
function updateAnswerClassifierDropdowns() {
    const addDropdown = document.getElementById('answer_classifier_id');
    const editDropdown = document.getElementById('editAnswerClassifierId');
    
    if (addDropdown && answerClassifiers.length > 0) {
        // 清空现有选项，保留第一个"不设置分类器"选项
        while (addDropdown.options.length > 1) {
            addDropdown.remove(1);
        }
        
        // 添加答案分类器选项
        answerClassifiers.forEach(classifier => {
            const option = document.createElement('option');
            option.value = classifier.id;
            option.textContent = classifier.name;
            addDropdown.appendChild(option);
        });
    }
    
    if (editDropdown && answerClassifiers.length > 0) {
        // 清空现有选项，保留第一个"不设置分类器"选项
        while (editDropdown.options.length > 1) {
            editDropdown.remove(1);
        }
        
        // 添加答案分类器选项
        answerClassifiers.forEach(classifier => {
            const option = document.createElement('option');
            option.value = classifier.id;
            option.textContent = classifier.name;
            editDropdown.appendChild(option);
        });
    }
}

// 显示添加场景弹窗
function showAddSceneModal() {
    // 重置表单
    document.getElementById('addSceneForm').reset();
    
    // 重置Select2
    $('#score_trigger_ids').val(null).trigger('change');
    $('#semantic_classifier_ids').val(null).trigger('change');
    $('#semantic_trigger_ids').val(null).trigger('change');
    
    // 设置当前职位分类ID
    document.getElementById('positionId').value = currentJobClassificationId;
    
    // 更新下拉框
    updateDropdowns();
    
    // 显示弹窗
    showModal('addSceneModal');
}

// 显示添加问题弹窗
function showAddQuestionModal(sceneId) {
    // 重置表单
    document.getElementById('addQuestionForm').reset();
    
    // 设置当前场景ID
    document.getElementById('questionSceneId').value = sceneId;
    
    // 显示弹窗
    showModal('addQuestionModal');
}

// 保存场景
async function saveScene() {
    // 获取表单数据
    const form = document.getElementById('addSceneForm');
    if (!form) {
        console.error('添加场景表单不存在');
        return;
    }
    
    const formData = new FormData(form);
    
    // 获取多选字段的值
    const scoreTriggerIds = $('#score_trigger_ids').val() || [];
    const semanticClassifierIds = $('#semantic_classifier_ids').val() || [];
    const semanticTriggerIds = $('#semantic_trigger_ids').val() || [];
    const answerClassifierId = formData.get('answer_classifier_id') || null;
    
    const data = {
        job_classification_id: formData.get('job_classification_id'),
        sceneName: formData.get('sceneName'),
        sceneDescription: formData.get('sceneDescription'),
        scoreRange: JSON.stringify([
            parseInt(formData.get('scoreMin') || 0),
            parseInt(formData.get('scoreMax') || 100)
        ]),
        isDefault: formData.get('isDefault') ? 1 : 0,
        enable_semantic_classifier: formData.get('enableSemanticClassifier') ? 1 : 0,
        enable_answer_classifier: formData.get('enableAnswerClassifier') ? 1 : 0,
        enable_score_trigger: formData.get('enableScoreTrigger') ? 1 : 0,
        enable_semantic_trigger: formData.get('enableSemanticTrigger') ? 1 : 0,
        allowed_to_fall_back: formData.get('allowedToFallBack') ? 1 : 0,
        // 移除单选触发器
        score_trigger_id: null,
        // 添加多选字段
        score_trigger_ids: scoreTriggerIds,
        semantic_classifier_ids: semanticClassifierIds,
        semantic_trigger_ids: semanticTriggerIds,
        answer_classifier_id: answerClassifierId
    };
    
    // 表单验证
    if (!data.sceneName) {
        alert('请输入场景名称');
        return;
    }
    
    if (isNaN(parseInt(formData.get('scoreMin'))) || isNaN(parseInt(formData.get('scoreMax')))) {
        alert('请输入有效的分数范围');
        return;
    }
    
    try {
        // 发送请求
        const token = localStorage.getItem('accessToken');
        const response = await fetch('/scene', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.code === 200) {
            // 隐藏弹窗
            hideModal('addSceneModal');
            
            // 重新加载场景列表
            loadScenes(currentJobClassificationId);
            
            // 更新左侧职位分类场景数
            loadJobClassifications();
            
            showSuccess('添加场景成功');
        } else {
            throw new Error(result.message || '添加场景失败');
        }
    } catch (error) {
        console.error('保存场景失败:', error);
        alert('保存场景失败: ' + error.message);
    }
}

// 保存问题
async function saveQuestion() {
    // 获取表单数据
    const form = document.getElementById('addQuestionForm');
    const formData = new FormData(form);
    const data = {
        sceneId: formData.get('sceneId'),
        questionContent: formData.get('questionContent'),
        questionScore: parseInt(formData.get('questionScore'))
    };
    
    // 表单验证
    if (!data.questionContent) {
        alert('请输入问题内容');
        return;
    }
    
    if (isNaN(data.questionScore) || data.questionScore < 0) {
        alert('请输入有效的分数');
        return;
    }
    
    try {
        // 发送请求
        const token = localStorage.getItem('accessToken');
        const response = await fetch('/scene_question', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.code === 200) {
            // 隐藏弹窗
            hideModal('addQuestionModal');
            
            // 重新加载问题列表
            loadQuestions(data.sceneId);
            
            showSuccess('添加问题成功');
        } else {
            throw new Error(result.message || '添加问题失败');
        }
    } catch (error) {
        console.error('保存问题失败:', error);
        alert('保存问题失败: ' + error.message);
    }
}

// 编辑场景
function editScene(scene) {
    console.log("编辑场景数据:", scene); // 调试日志
    
    // 填充表单数据
    document.getElementById('editSceneId').value = scene.sceneId;
    document.getElementById('editPositionId').value = scene.job_classification_id;
    document.getElementById('editSceneName').value = scene.sceneName || '';
    document.getElementById('editSceneDescription').value = scene.sceneDescription || '';
    
    // 处理分数范围
    let scoreMin = 0;
    let scoreMax = 100;
    if (scene.scoreRange) {
        try {
            // 检查scoreRange是否已经是数组
            if (Array.isArray(scene.scoreRange) && scene.scoreRange.length >= 2) {
                scoreMin = scene.scoreRange[0];
                scoreMax = scene.scoreRange[1];
            } else {
                // 如果是字符串，尝试解析
                const range = JSON.parse(scene.scoreRange);
                if (Array.isArray(range) && range.length >= 2) {
                    scoreMin = range[0];
                    scoreMax = range[1];
                }
            }
        } catch (e) {
            console.error('解析分数范围失败:', e);
        }
    }
    document.getElementById('editScoreMin').value = scoreMin;
    document.getElementById('editScoreMax').value = scoreMax;
    
    // 处理复选框
    const editIsDefault = document.getElementById('editIsDefault');
    if (editIsDefault) editIsDefault.checked = scene.isDefault === 1;
    
    const editEnableSemanticClassifier = document.getElementById('editEnableSemanticClassifier');
    if (editEnableSemanticClassifier) editEnableSemanticClassifier.checked = scene.enable_semantic_classifier === 1;
    
    const editEnableAnswerClassifier = document.getElementById('editEnableAnswerClassifier');
    if (editEnableAnswerClassifier) editEnableAnswerClassifier.checked = scene.enable_answer_classifier === 1;
    
    const editEnableScoreTrigger = document.getElementById('editEnableScoreTrigger');
    if (editEnableScoreTrigger) editEnableScoreTrigger.checked = scene.enable_score_trigger === 1;
    
    const editEnableSemanticTrigger = document.getElementById('editEnableSemanticTrigger');
    if (editEnableSemanticTrigger) editEnableSemanticTrigger.checked = scene.enable_semantic_trigger === 1;
    
    const editAllowedToFallBack = document.getElementById('editAllowedToFallBack');
    if (editAllowedToFallBack) editAllowedToFallBack.checked = scene.allowed_to_fall_back === 1;
    
    // 先更新所有下拉框
    updateDropdowns();
    
    // 处理多选字段
    // 分数触发器多选
    if (scene.score_trigger_ids && scene.score_trigger_ids.length > 0) {
        $('#editScoreTriggerIds').val(scene.score_trigger_ids).trigger('change');
    } else {
        $('#editScoreTriggerIds').val([]).trigger('change');
    }
    
    // 语义分类器多选
    if (scene.semantic_classifier_ids && scene.semantic_classifier_ids.length > 0) {
        $('#editSemanticClassifierIds').val(scene.semantic_classifier_ids).trigger('change');
    } else {
        $('#editSemanticClassifierIds').val([]).trigger('change');
    }
    
    // 语义触发器多选
    if (scene.semantic_trigger_ids && scene.semantic_trigger_ids.length > 0) {
        $('#editSemanticTriggerIds').val(scene.semantic_trigger_ids).trigger('change');
    } else {
        $('#editSemanticTriggerIds').val([]).trigger('change');
    }
    
    // 答案分类器单选
    const answerClassifierSelect = document.getElementById('editAnswerClassifierId');
    if (answerClassifierSelect && scene.answer_classifier_id) {
        for (let i = 0; i < answerClassifierSelect.options.length; i++) {
            if (answerClassifierSelect.options[i].value === scene.answer_classifier_id) {
                answerClassifierSelect.selectedIndex = i;
                break;
            }
        }
    } else if (answerClassifierSelect) {
        answerClassifierSelect.selectedIndex = 0;
    }
    
    // 显示弹窗
    showModal('editSceneModal');
}

// 更新场景
async function updateScene() {
    // 获取表单数据
    const form = document.getElementById('editSceneForm');
    if (!form) {
        console.error('编辑场景表单不存在');
        return;
    }
    
    const formData = new FormData(form);
    const sceneId = formData.get('sceneId');
    
    // 获取多选字段的值
    const scoreTriggerIds = $('#editScoreTriggerIds').val() || [];
    const semanticClassifierIds = $('#editSemanticClassifierIds').val() || [];
    const semanticTriggerIds = $('#editSemanticTriggerIds').val() || [];
    const answerClassifierId = formData.get('answer_classifier_id') || null;
    
    const data = {
        job_classification_id: formData.get('job_classification_id'),
        sceneName: formData.get('sceneName'),
        sceneDescription: formData.get('sceneDescription'),
        scoreRange: JSON.stringify([
            parseInt(formData.get('scoreMin') || 0),
            parseInt(formData.get('scoreMax') || 100)
        ]),
        isDefault: formData.get('isDefault') ? 1 : 0,
        enable_semantic_classifier: formData.get('enableSemanticClassifier') ? 1 : 0,
        enable_answer_classifier: formData.get('enableAnswerClassifier') ? 1 : 0,
        enable_score_trigger: formData.get('enableScoreTrigger') ? 1 : 0,
        enable_semantic_trigger: formData.get('enableSemanticTrigger') ? 1 : 0,
        allowed_to_fall_back: formData.get('allowedToFallBack') ? 1 : 0,
        // 移除单选触发器
        score_trigger_id: null,
        // 添加多选字段
        score_trigger_ids: scoreTriggerIds,
        semantic_classifier_ids: semanticClassifierIds,
        semantic_trigger_ids: semanticTriggerIds,
        answer_classifier_id: answerClassifierId
    };
    
    // 表单验证
    if (!data.sceneName) {
        alert('请输入场景名称');
        return;
    }
    
    try {
        // 发送请求
        const token = localStorage.getItem('accessToken');
        const response = await fetch(`/scene/${sceneId}`, {
            method: 'PUT',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.code === 200) {
            // 隐藏弹窗
            hideModal('editSceneModal');
            
            // 重新加载场景列表
            loadScenes(currentJobClassificationId);
            
            showSuccess('更新场景成功');
        } else {
            throw new Error(result.message || '更新场景失败');
        }
    } catch (error) {
        console.error('更新场景失败:', error);
        alert('更新场景失败: ' + error.message);
    }
}

// 显示弹窗
function showModal(modalId) {
    console.log('showModal called with modalId:', modalId);
    const modal = document.getElementById(modalId);
    console.log('Found modal element:', modal);
    if (modal) {
        modal.classList.add('show');
        modal.style.display = 'flex';
        console.log('Modal should now be visible');
    } else {
        console.error(`Modal with ID ${modalId} not found`);
    }
}

// 隐藏弹窗
function hideModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('show');
        modal.style.display = 'none';
    } else {
        console.error(`Modal with ID ${modalId} not found`);
    }
}

// 显示成功消息
function showSuccess(message) {
    alert(message); // 简单实现，实际可以使用自定义的消息提示
}

// 显示错误消息
function showError(message) {
    alert(message); // 简单实现，实际可以使用自定义的消息提示
}