// 职位分类管理页面脚本
document.addEventListener('DOMContentLoaded', function() {
    // 初始化职位分类列表
    initClassificationPage();
});

// 状态变量
let classifications = [];
let virtualHRs = [];
let currentPage = 1;
let pageSize = 10;
let totalPages = 1;
let currentFilters = {
    search: '',
    sortBy: 'newest'
};

// 初始化职位分类页面
async function initClassificationPage() {
    // 绑定事件监听
    bindEvents();
    
    // 加载虚拟HR数据
    await loadVirtualHRs();
    
    // 加载职位分类数据
    await loadClassifications();
}

// 绑定事件监听
function bindEvents() {
    // 新增职位分类按钮
    const addClassificationBtn = document.getElementById('addClassificationBtn');
    addClassificationBtn.addEventListener('click', showAddClassificationModal);
    
    // 搜索按钮
    const searchBtn = document.getElementById('searchBtn');
    searchBtn.addEventListener('click', handleSearch);
    
    // 搜索输入框回车事件
    const searchInput = document.getElementById('searchInput');
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            handleSearch();
        }
    });
    
    // 排序方式变化事件
    const sortBy = document.getElementById('sortBy');
    sortBy.addEventListener('change', handleFilterChange);
    
    // 模态框关闭按钮
    const modalCloseButtons = document.querySelectorAll('.modal-close');
    modalCloseButtons.forEach(button => {
        button.addEventListener('click', closeAllModals);
    });
    
    // 取消按钮
    const cancelBtn = document.getElementById('cancelBtn');
    cancelBtn.addEventListener('click', closeAllModals);
    
    // 保存职位分类按钮
    const saveClassificationBtn = document.getElementById('saveClassificationBtn');
    saveClassificationBtn.addEventListener('click', saveClassification);
    
    // 详情模态框关闭按钮
    const closeDetailBtn = document.getElementById('closeDetailBtn');
    closeDetailBtn.addEventListener('click', closeAllModals);
    
    // 编辑职位分类按钮
    const editClassificationBtn = document.getElementById('editClassificationBtn');
    editClassificationBtn.addEventListener('click', function() {
        // 获取当前职位分类ID
        const classificationId = document.getElementById('classificationDetails').getAttribute('data-id');
        if (classificationId) {
            // 先关闭详情模态框，再打开编辑模态框
            const detailModal = document.getElementById('classificationDetailModal');
            detailModal.classList.remove('show');
            setTimeout(() => {
                detailModal.style.display = 'none';
                openEditClassificationModal(classificationId);
            }, 300);
        }
    });
}

// 加载虚拟HR数据
async function loadVirtualHRs() {
    try {
        // 使用API对象获取虚拟HR数据
        const result = await API.virtualHR.getAll();
        
        if (result) {
            virtualHRs = result;
            populateVirtualHRDropdown(virtualHRs);
        } else {
            console.error('加载虚拟HR数据失败:', result ? result.message : '未知错误');
            showNotification('加载虚拟HR数据失败，请刷新页面重试', 'warning');
        }
    } catch (error) {
        console.error('加载虚拟HR数据失败:', error);
        showNotification('加载虚拟HR数据失败，请刷新页面重试', 'error');
    }
}

// 填充虚拟HR下拉选择框
function populateVirtualHRDropdown(hrList) {
    const virtualHrSelect = document.getElementById('virtualHrId');
    
    // 清空现有选项，保留第一个默认选项
    virtualHrSelect.innerHTML = '<option value="">请选择虚拟HR</option>';
    
    // 添加虚拟HR选项
    hrList.forEach(hr => {
        const option = document.createElement('option');
        option.value = hr.id;
        option.textContent = hr.name;
        virtualHrSelect.appendChild(option);
    });
}

// 加载职位分类数据
async function loadClassifications() {
    try {
        const classificationList = document.getElementById('classificationList');
        
        // 显示加载状态
        classificationList.innerHTML = `
            <div class="classification-list-loading">
                <p>加载中...</p>
            </div>
        `;
        
        // 从API获取职位分类数据
        const token = localStorage.getItem('accessToken');
        const response = await fetch('/web_job_classifications',{
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        const result = await response.json();
        
        if (!result.data || result.data.length === 0) {
            classificationList.innerHTML = `
                <div class="classification-empty">
                    <i class="fas fa-tags"></i>
                    <p>暂无职位分类数据</p>
                    <button class="btn btn-primary mt-10" id="emptyAddBtn">
                        <i class="fas fa-plus"></i> 添加职位分类
                    </button>
                </div>
            `;
            
            // 绑定空状态下的添加按钮
            const emptyAddBtn = document.getElementById('emptyAddBtn');
            if (emptyAddBtn) {
                emptyAddBtn.addEventListener('click', showAddClassificationModal);
            }
            
            return;
        }
        
        // 保存职位分类数据
        classifications = result.data;
        
        // 获取每个职位分类的统计数据
        for (let i = 0; i < classifications.length; i++) {
            try {
                const token = localStorage.getItem('accessToken');
                const stats = await fetch(`/api/job_classification/${classifications[i].id}`,{
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                }).then(res => res.json());
                classifications[i].positions_count = stats.positions_count || 0;
                classifications[i].users_count = stats.users_count || 0;
                classifications[i].scenes_count = stats.scenes_count || 0; // 新增场景数量
            } catch (statsError) {
                console.error(`获取职位分类 ${classifications[i].id} 统计数据失败:`, statsError);
                classifications[i].positions_count = 0;
                classifications[i].users_count = 0;
                classifications[i].scenes_count = 0; // 新增场景数量
            }
        }
        
        // 应用筛选和排序
        classifications = filterAndSortClassifications(classifications);
        
        // 计算分页
        totalPages = Math.ceil(classifications.length / pageSize);
        
        // 渲染职位分类列表
        renderClassificationList();
        
        // 渲染分页
        renderPagination();
    } catch (error) {
        console.error('加载职位分类数据失败:', error);
        
        const classificationList = document.getElementById('classificationList');
        classificationList.innerHTML = `
            <div class="classification-empty">
                <i class="fas fa-exclamation-circle"></i>
                <p>加载职位分类数据失败</p>
                <button class="btn btn-primary mt-10" id="retryBtn">
                    <i class="fas fa-redo"></i> 重试
                </button>
            </div>
        `;
        
        // 绑定重试按钮
        const retryBtn = document.getElementById('retryBtn');
        if (retryBtn) {
            retryBtn.addEventListener('click', loadClassifications);
        }
    }
}

// 筛选和排序职位分类
function filterAndSortClassifications(classificationList) {
    // 复制数组以避免修改原数组
    let filtered = [...classificationList];
    
    // 应用搜索筛选
    if (currentFilters.search) {
        const searchLower = currentFilters.search.toLowerCase();
        filtered = filtered.filter(classification => 
            (classification.class_name && classification.class_name.toLowerCase().includes(searchLower)) ||
            (classification.class_description && classification.class_description.toLowerCase().includes(searchLower))
        );
    }
    
    // 应用排序
    switch (currentFilters.sortBy) {
        case 'name':
            filtered.sort((a, b) => {
                if (!a.class_name) return 1;
                if (!b.class_name) return -1;
                return a.class_name.localeCompare(b.class_name);
            });
            break;
        case 'newest':
        default:
            filtered.sort((a, b) => {
                if (!a.createdAt) return 1;
                if (!b.createdAt) return -1;
                return new Date(b.createdAt) - new Date(a.createdAt);
            });
            break;
    }
    
    return filtered;
}

// 渲染职位分类列表
function renderClassificationList() {
    const classificationList = document.getElementById('classificationList');
    
    // 计算当前页的数据
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const currentPageData = classifications.slice(startIndex, endIndex);
    
    // 如果当前页没有数据且不是第一页，则返回上一页
    if (currentPageData.length === 0 && currentPage > 1) {
        currentPage--;
        renderClassificationList();
        return;
    }
    
    // 生成HTML
    let html = '';
    
    currentPageData.forEach(classification => {
        // 查找关联的虚拟HR名称
        let virtualHrName = '未设置';
        if (classification.virtual_hr_id) {
            const virtualHr = virtualHRs.find(hr => hr.id === classification.virtual_hr_id);
            if (virtualHr) {
                virtualHrName = virtualHr.name;
            }
        }
        
        // 获取统计数据（这里可以从API获取，暂时使用模拟数据）
        const positionsCount = classification.positions_count || 0;
        const usersCount = classification.users_count || 0;
        const scenesCount = classification.scenes_count || 0;
        
        html += `
            <div class="classification-card" data-id="${classification.id}">
                <!-- 卡片顶部 - 名称 -->
                <div class="classification-top">
                    <h3 class="classification-name">${classification.class_name || '未命名分类'}</h3>
                    <div class="virtual-hr-tag">
                        <i class="fas fa-robot"></i>
                        <span>${virtualHrName}</span>
                    </div>
                </div>
                
                <!-- 卡片中部 - 统计数据 -->
                <div class="classification-middle">
                    <div class="classification-stat">
                        <i class="fas fa-briefcase"></i>
                        <span>${positionsCount} 职位</span>
                    </div>
                    <div class="classification-stat">
                        <i class="fas fa-users"></i>
                        <span>${usersCount} 用户</span>
                    </div>
                    <div class="classification-stat">
                        <i class="fas fa-tasks"></i>
                        <span>${scenesCount} 场景</span>
                    </div>
                </div>
                
                <!-- 卡片底部 - 操作按钮 -->
                <div class="classification-bottom">
                    <div class="classification-action-btn view-details-btn" data-id="${classification.id}" title="查看详情">
                        <i class="fas fa-eye"></i>
                    </div>
                    <div class="classification-action-btn edit-btn" data-id="${classification.id}" title="编辑">
                        <i class="fas fa-edit"></i>
                    </div>
                    <div class="classification-action-btn delete-btn" data-id="${classification.id}" title="删除">
                        <i class="fas fa-trash-alt"></i>
                    </div>
                </div>
            </div>
        `;
    });
    
    // 更新DOM
    classificationList.innerHTML = html;
    
    // 绑定事件
    bindClassificationItemEvents();
}

// 绑定职位分类项事件
function bindClassificationItemEvents() {
    // 查看按钮
    const viewButtons = document.querySelectorAll('.view-details-btn');
    viewButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            showClassificationDetail(id);
        });
    });
    
    // 编辑按钮
    const editButtons = document.querySelectorAll('.edit-btn');
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            openEditClassificationModal(id);
        });
    });
    
    // 删除按钮
    const deleteButtons = document.querySelectorAll('.delete-btn');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            confirmDeleteClassification(id);
        });
    });
}

// 渲染分页
function renderPagination() {
    const paginationContainer = document.getElementById('pagination');
    
    if (totalPages <= 1) {
        paginationContainer.innerHTML = '';
        return;
    }
    
    let html = '<div class="pagination">';
    
    // 上一页按钮
    if (currentPage > 1) {
        html += `
            <div class="pagination-item">
                <span class="pagination-link" data-page="${currentPage - 1}">
                    <i class="fas fa-chevron-left"></i>
                </span>
            </div>
        `;
    } else {
        html += `
            <div class="pagination-item">
                <span class="pagination-link disabled">
                    <i class="fas fa-chevron-left"></i>
                </span>
            </div>
        `;
    }
    
    // 页码按钮
    const maxPages = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxPages / 2));
    let endPage = Math.min(totalPages, startPage + maxPages - 1);
    
    if (endPage - startPage + 1 < maxPages) {
        startPage = Math.max(1, endPage - maxPages + 1);
    }
    
    for (let i = startPage; i <= endPage; i++) {
        if (i === currentPage) {
            html += `
                <div class="pagination-item">
                    <span class="pagination-link active">${i}</span>
                </div>
            `;
        } else {
            html += `
                <div class="pagination-item">
                    <span class="pagination-link" data-page="${i}">${i}</span>
                </div>
            `;
        }
    }
    
    // 下一页按钮
    if (currentPage < totalPages) {
        html += `
            <div class="pagination-item">
                <span class="pagination-link" data-page="${currentPage + 1}">
                    <i class="fas fa-chevron-right"></i>
                </span>
            </div>
        `;
    } else {
        html += `
            <div class="pagination-item">
                <span class="pagination-link disabled">
                    <i class="fas fa-chevron-right"></i>
                </span>
            </div>
        `;
    }
    
    html += '</div>';
    
    paginationContainer.innerHTML = html;
    
    // 绑定分页事件
    const pageLinks = document.querySelectorAll('.pagination-link:not(.disabled):not(.active)');
    pageLinks.forEach(link => {
        link.addEventListener('click', function() {
            const page = parseInt(this.getAttribute('data-page'));
            currentPage = page;
            renderClassificationList();
            renderPagination();
        });
    });
}

// 处理搜索
function handleSearch() {
    const searchInput = document.getElementById('searchInput');
    currentFilters.search = searchInput.value.trim();
    currentPage = 1;
    
    // 应用筛选和排序
    classifications = filterAndSortClassifications(classifications);
    
    // 计算分页
    totalPages = Math.ceil(classifications.length / pageSize);
    
    // 渲染列表和分页
    renderClassificationList();
    renderPagination();
}

// 处理筛选条件变化
function handleFilterChange() {
    const sortBy = document.getElementById('sortBy');
    currentFilters.sortBy = sortBy.value;
    currentPage = 1;
    
    // 应用筛选和排序
    classifications = filterAndSortClassifications(classifications);
    
    // 计算分页
    totalPages = Math.ceil(classifications.length / pageSize);
    
    // 渲染列表和分页
    renderClassificationList();
    renderPagination();
}

// 显示添加职位分类模态框
function showAddClassificationModal() {
    // 清空表单
    document.getElementById('classificationForm').reset();
    document.getElementById('classificationId').value = '';
    
    // 设置标题
    document.getElementById('modalTitle').textContent = '新增职位分类';
    
    // 显示模态框
    const modal = document.getElementById('classificationModal');
    modal.style.display = 'block';
    // 使用requestAnimationFrame确保DOM更新后再添加show类
    requestAnimationFrame(() => {
        modal.classList.add('show');
    });
}

// 打开编辑职位分类模态框
async function openEditClassificationModal(id) {
    try {
        // 设置标题
        document.getElementById('modalTitle').textContent = '编辑职位分类';
        
        // 显示加载状态
        document.getElementById('classificationForm').reset();
        
        // 获取职位分类详情
        const token = localStorage.getItem('accessToken');
        const response = await fetch(`/job_classifications/${id}`,{
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        const result = await response.json();
        
        if (result.code === 200 && result.data) {
            const classification = result.data;
            
            // 填充表单
            document.getElementById('classificationId').value = classification.id;
            document.getElementById('className').value = classification.class_name || '';
            document.getElementById('classDescription').value = classification.class_description || '';
            document.getElementById('virtualHrId').value = classification.virtual_hr_id || '';
            
            // 显示模态框
            const modal = document.getElementById('classificationModal');
            modal.style.display = 'block';
            requestAnimationFrame(() => {
                modal.classList.add('show');
            });
        } else {
            showNotification('获取职位分类详情失败', 'error');
        }
    } catch (error) {
        console.error('打开编辑模态框失败:', error);
        showNotification('获取职位分类详情失败', 'error');
    }
}

// 保存职位分类
async function saveClassification() {
    // 获取表单数据
    const form = document.getElementById('classificationForm');
    
    // 表单验证
    if (form.hasAttribute('data-validate') && !form.checkValidity()) {
        form.reportValidity();
        return;
    }
    
    // 构建数据对象
    const classificationData = {
        class_name: document.getElementById('className').value,
        class_description: document.getElementById('classDescription').value,
        virtual_hr_id: document.getElementById('virtualHrId').value
    };
    
    // 获取ID，判断是新增还是编辑
    const id = document.getElementById('classificationId').value;
    
    try {
        let response;
        let result;
        
        if (id) {
            // 编辑现有职位分类
            const token = localStorage.getItem('accessToken');
            response = await fetch(`/job_classification/${id}`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(classificationData)
            });
            result = await response.json();
            
            if (result.code === 200) {
                showNotification('职位分类更新成功', 'success');
                
                // 更新本地数据
                const index = classifications.findIndex(c => c.id === id);
                if (index !== -1) {
                    classifications[index] = { ...classifications[index], ...classificationData };
                }
            } else {
                showNotification('职位分类更新失败: ' + (result.message || '未知错误'), 'error');
            }
        } else {
            // 添加新的职位分类
            const token = localStorage.getItem('accessToken');
            response = await fetch('/job_classification', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(classificationData)
            });
            result = await response.json();
            
            if (result.code === 200) {
                showNotification('职位分类添加成功', 'success');
            } else {
                showNotification('职位分类添加失败: ' + (result.message || '未知错误'), 'error');
            }
        }
        
        // 关闭模态框
        closeAllModals();
        
        // 重新加载数据
        await loadClassifications();
    } catch (error) {
        console.error('保存职位分类失败:', error);
        showNotification('保存职位分类失败', 'error');
    }
}

// 确认删除职位分类
function confirmDeleteClassification(id) {
    if (confirm('确定要删除这个职位分类吗？此操作不可撤销。')) {
        deleteClassification(id);
    }
}

// 删除职位分类
async function deleteClassification(id) {
    try {
        const token = localStorage.getItem('accessToken');
        const response = await fetch(`/job_classification/${id}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        const result = await response.json();
        
        if (result.code === 200) {
            showNotification('职位分类删除成功', 'success');
            
            // 从本地数据中移除
            classifications = classifications.filter(c => c.id !== id);
            
            // 重新渲染列表
            totalPages = Math.ceil(classifications.length / pageSize);
            renderClassificationList();
            renderPagination();
        } else {
            showNotification('职位分类删除失败: ' + (result.message || '未知错误'), 'error');
        }
    } catch (error) {
        console.error('删除职位分类失败:', error);
        showNotification('删除职位分类失败', 'error');
    }
}

// 显示职位分类详情
async function showClassificationDetail(id) {
    try {
        // 从API获取职位分类综合数据
        const token = localStorage.getItem('accessToken');
        const response = await fetch(`/job_classifications/${id}/comprehensive`,{
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        const result = await response.json();

        if (!result.data) {
            showNotification('获取职位分类详情失败', 'error');
            return;
        }

        const data = result.data;
        const classification = data.basic_info;

        // 设置模态框标题
        document.getElementById('detailModalTitle').textContent = classification.class_name + ' 详情';

        // 渲染基本信息
        renderBasicInfo(classification, data.virtual_hr);

        // 渲染各个部分的数据
        renderPositions(data.positions);
        renderScenes(data.scenes, data.scene_questions);
        renderUsers(data.users);
        renderVirtualHR(data.virtual_hr);
        renderScheduledAnalyses(data.scheduled_analyses);
        renderQAList(data.qa_list);

        // 绑定折叠展开事件
        bindSectionToggleEvents();

        // 设置数据ID
        const classificationDetails = document.getElementById('classificationDetails');
        classificationDetails.setAttribute('data-id', classification.id);

        // 显示模态框
        const modal = document.getElementById('classificationDetailModal');
        modal.style.display = 'block';
        requestAnimationFrame(() => {
            modal.classList.add('show');
        });
    } catch (error) {
        console.error('获取职位分类详情失败:', error);
        showNotification('获取职位分类详情失败', 'error');
    }
}

// 格式化日期
function formatDate(dateString) {
    if (!dateString) return '未知';
    
    try {
        const date = new Date(dateString);
        
        if (isNaN(date.getTime())) {
            return dateString;
        }
        
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    } catch (error) {
        return dateString;
    }
}

// 关闭所有模态框
function closeAllModals() {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
    });
}

// 显示通知
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;

    // 添加到页面
    document.body.appendChild(notification);

    // 3秒后自动移除
    setTimeout(() => {
        notification.classList.add('notification-hide');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// 渲染基本信息
function renderBasicInfo(classification, virtualHr) {
    const virtualHrName = virtualHr ? virtualHr.name : '未设置';

    const basicInfoHTML = `
        <div class="detail-content">
            <div class="detail-row">
                <span class="detail-label">分类名称:</span>
                <span class="detail-value">${classification.class_name || '未命名'}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">描述:</span>
                <span class="detail-value">${classification.class_description || '无描述'}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">创建时间:</span>
                <span class="detail-value">${formatDate(classification.createdAt)}</span>
            </div>
            <div class="virtual-hr-badge">
                <i class="fas fa-robot"></i>
                <span>${virtualHrName}</span>
            </div>
        </div>
    `;

    document.getElementById('basicInfoContent').innerHTML = basicInfoHTML;
}

// 渲染职位列表
function renderPositions(positions) {
    const count = positions.length;

    if (count === 0) {
        document.getElementById('positionsContent').innerHTML = '<div class="empty-state">暂无职位数据</div>';
        return;
    }

    let positionsHTML = '<div class="data-list">';
    positions.forEach(position => {
        positionsHTML += `
            <div class="data-item">
                <div class="data-item-header">
                    <i class="fas fa-briefcase"></i>
                    <span class="data-item-title">${position.positionName || '未命名职位'}</span>
                </div>
                <div class="data-item-content">
                    <div class="data-item-info">
                        <span class="info-label">城市:</span>
                        <span class="info-value">${position.city || '未设置'}</span>
                    </div>
                    <div class="data-item-info">
                        <span class="info-label">创建时间:</span>
                        <span class="info-value">${formatDate(position.createdAt)}</span>
                    </div>
                </div>
            </div>
        `;
    });
    positionsHTML += '</div>';

    document.getElementById('positionsContent').innerHTML = positionsHTML;
}

// 渲染场景列表
function renderScenes(scenes, sceneQuestions) {
    const count = scenes.length;

    if (count === 0) {
        document.getElementById('scenesContent').innerHTML = '<div class="empty-state">暂无场景数据</div>';
        return;
    }

    let scenesHTML = '<div class="data-list">';
    scenes.forEach(scene => {
        const questions = sceneQuestions[scene.sceneId] || [];
        const questionsCount = questions.length;

        scenesHTML += `
            <div class="data-item">
                <div class="data-item-header">
                    <i class="fas fa-tasks"></i>
                    <span class="data-item-title">${scene.sceneName || '未命名场景'}</span>
                    ${scene.isDefault ? '<span class="badge badge-primary">默认</span>' : ''}
                </div>
                <div class="data-item-content">
                    <div class="data-item-info">
                        <span class="info-label">分数范围:</span>
                        <span class="info-value">${scene.scoreMin || 0} - ${scene.scoreMax || 100}</span>
                    </div>
                    <div class="data-item-info">
                        <span class="info-label">问题数量:</span>
                        <span class="info-value">${questionsCount} 个</span>
                    </div>
                    ${questionsCount > 0 ? `
                        <div class="scene-questions">
                            <div class="questions-toggle" data-scene-id="${scene.sceneId}">
                                <i class="fas fa-chevron-down"></i>
                                <span>查看问题</span>
                            </div>
                            <div class="questions-list collapsed" id="questions-${scene.sceneId}">
                                ${questions.map(q => `
                                    <div class="question-item">
                                        <div class="question-text">${q.questionContent || '未设置问题'}</div>
                                        <div class="question-score">分数: ${q.questionScore || 0}</div>
                                        <div class="question-seq">序号: ${q.seq || 1}</div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    });
    scenesHTML += '</div>';

    document.getElementById('scenesContent').innerHTML = scenesHTML;

    // 绑定问题展开事件
    bindQuestionToggleEvents();
}

// 渲染用户列表
function renderUsers(users) {
    const count = users.length;

    if (count === 0) {
        document.getElementById('usersContent').innerHTML = '<div class="empty-state">暂无候选人数据</div>';
        return;
    }

    let usersHTML = '<div class="user-cards-grid">';
    users.forEach(user => {
        const userName = user.real_name || user.name || '未命名用户';
        const userPhone = user.phone || '未设置';
        const userAvatar = userName.charAt(0).toUpperCase();
        const createdDate = user.createdAt ? new Date(user.createdAt).toLocaleDateString('zh-CN') : '未知';

        usersHTML += `
            <div class="user-card">
                <div class="user-avatar">
                    <span class="avatar-text">${userAvatar}</span>
                </div>
                <div class="user-info">
                    <div class="user-name" title="${userName}">${userName}</div>
                    <div class="user-phone" title="${userPhone}">
                        <i class="fas fa-phone"></i>
                        ${userPhone}
                    </div>
                    <div class="user-date">
                        <i class="fas fa-calendar"></i>
                        ${createdDate}
                    </div>
                </div>
            </div>
        `;
    });
    usersHTML += '</div>';

    document.getElementById('usersContent').innerHTML = usersHTML;
}

// 渲染虚拟HR信息
function renderVirtualHR(virtualHr) {
    if (!virtualHr) {
        document.getElementById('virtualhrContent').innerHTML = '<div class="empty-state">未设置虚拟HR</div>';
        return;
    }

    const virtualHrHTML = `
        <div class="detail-content">
            <div class="virtual-hr-card">
                <div class="virtual-hr-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="virtual-hr-info">
                    <div class="detail-row">
                        <span class="detail-label">名称:</span>
                        <span class="detail-value">${virtualHr.name || '未命名'}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">描述:</span>
                        <span class="detail-value">${virtualHr.description || '无描述'}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">创建时间:</span>
                        <span class="detail-value">${formatDate(virtualHr.createdAt)}</span>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('virtualhrContent').innerHTML = virtualHrHTML;
}

// 渲染定时分析配置
function renderScheduledAnalyses(analyses) {
    const count = analyses.length;

    if (count === 0) {
        document.getElementById('analysisContent').innerHTML = '<div class="empty-state">暂无定时分析配置</div>';
        return;
    }

    let analysesHTML = '<div class="data-list">';
    analyses.forEach(analysis => {
        analysesHTML += `
            <div class="data-item">
                <div class="data-item-header">
                    <i class="fas fa-chart-line"></i>
                    <span class="data-item-title">${analysis.name || '未命名分析'}</span>
                    <span class="badge ${analysis.is_auto_enabled ? 'badge-success' : 'badge-secondary'}">
                        ${analysis.is_auto_enabled ? '已启用' : '已禁用'}
                    </span>
                </div>
                <div class="data-item-content">
                    <div class="data-item-info">
                        <span class="info-label">频率:</span>
                        <span class="info-value">${analysis.cron_expression || '未设置'}</span>
                    </div>
                    <div class="data-item-info">
                        <span class="info-label">创建时间:</span>
                        <span class="info-value">${formatDate(analysis.createdAt)}</span>
                    </div>
                </div>
            </div>
        `;
    });
    analysesHTML += '</div>';

    document.getElementById('analysisContent').innerHTML = analysesHTML;
}

// 渲染QA问题对
function renderQAList(qaList) {
    const count = qaList.length;

    if (count === 0) {
        document.getElementById('qaContent').innerHTML = '<div class="empty-state">暂无QA问题对</div>';
        return;
    }

    let qaHTML = '<div class="data-list">';
    qaList.forEach(qa => {
        qaHTML += `
            <div class="data-item">
                <div class="data-item-header">
                    <i class="fas fa-question-circle"></i>
                    <span class="data-item-title">问题对 #${qa.id || ''}</span>
                </div>
                <div class="data-item-content">
                    <div class="qa-pair">
                        <div class="qa-question">
                            <span class="qa-label">Q:</span>
                            <span class="qa-text">${qa.question || '未设置问题'}</span>
                        </div>
                        <div class="qa-answer">
                            <span class="qa-label">A:</span>
                            <span class="qa-text">${qa.answer || '未设置答案'}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    qaHTML += '</div>';

    document.getElementById('qaContent').innerHTML = qaHTML;
}

// 绑定折叠展开事件
function bindSectionToggleEvents() {
    // 先移除所有现有的事件监听器
    const sectionHeaders = document.querySelectorAll('.detail-section-header');
    sectionHeaders.forEach(header => {
        // 克隆节点来移除所有事件监听器
        const newHeader = header.cloneNode(true);
        header.parentNode.replaceChild(newHeader, header);
    });

    // 重新获取元素并绑定事件
    const newSectionHeaders = document.querySelectorAll('.detail-section-header');
    newSectionHeaders.forEach(header => {
        header.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const section = this.getAttribute('data-section');
            const content = document.getElementById(section + 'Content');
            const toggleIcon = this.querySelector('.toggle-icon');

            if (!content || !toggleIcon) return;

            if (content.classList.contains('collapsed')) {
                content.classList.remove('collapsed');
                toggleIcon.classList.remove('fa-chevron-down');
                toggleIcon.classList.add('fa-chevron-up');
            } else {
                content.classList.add('collapsed');
                toggleIcon.classList.remove('fa-chevron-up');
                toggleIcon.classList.add('fa-chevron-down');
            }
        });
    });
}

// 绑定问题展开事件
function bindQuestionToggleEvents() {
    const questionToggles = document.querySelectorAll('.questions-toggle');
    questionToggles.forEach(toggle => {
        toggle.addEventListener('click', function() {
            const sceneId = this.getAttribute('data-scene-id');
            const questionsList = document.getElementById('questions-' + sceneId);
            const toggleIcon = this.querySelector('i');

            if (questionsList.classList.contains('collapsed')) {
                questionsList.classList.remove('collapsed');
                toggleIcon.classList.remove('fa-chevron-down');
                toggleIcon.classList.add('fa-chevron-up');
                this.querySelector('span').textContent = '收起问题';
            } else {
                questionsList.classList.add('collapsed');
                toggleIcon.classList.remove('fa-chevron-up');
                toggleIcon.classList.add('fa-chevron-down');
                this.querySelector('span').textContent = '查看问题';
            }
        });
    });
}