// 全局常量
const API_BASE_URL = '';  // 后端API基础URL

// DOM加载完成后执行
document.addEventListener('DOMContentLoaded', () => {
    // 初始化侧边栏菜单
    initSidebar();
    
    // 侧边栏切换按钮点击事件
    const toggleButton = document.querySelector('.topbar-toggle');
    if (toggleButton) {
        toggleButton.addEventListener('click', () => {
            const sidebar = document.querySelector('.sidebar');
            const mainContent = document.querySelector('.main-content');
            
            sidebar.classList.toggle('sidebar-collapsed');
            mainContent.classList.toggle('main-content-expanded');
        });
    }
    
    // 初始化全局模态框功能
    initModalFunctionality();
});

// 初始化模态框功能
function initModalFunctionality() {
    // 为所有模态框的关闭按钮添加事件
    document.querySelectorAll('.modal-close, [data-dismiss="modal"]').forEach(button => {
        button.addEventListener('click', function() {
            const modal = this.closest('.modal');
            if (modal) {
                closeModal(modal);
            }
        });
    });
    
    // 点击模态框背景关闭模态框 - 已禁用，只允许通过关闭按钮关闭
    /*
    document.querySelectorAll('.modal').forEach(modal => {
        modal.addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal(this);
            }
        });
    });
    */
}

// 打开模态框
function openModal(modal) {
    if (typeof modal === 'string') {
        modal = document.getElementById(modal);
    }
    if (!modal) return;
    
    modal.style.display = 'block';
    // 使用setTimeout确保样式变化有效
    setTimeout(() => {
        modal.classList.add('show');
    }, 10);
}

// 关闭模态框
function closeModal(modal) {
    if (typeof modal === 'string') {
        modal = document.getElementById(modal);
    }
    if (!modal) return;
    
    modal.classList.remove('show');
    setTimeout(() => {
        modal.style.display = 'none';
    }, 300);
}

// 初始化侧边栏菜单
function initSidebar() {
    // 侧边栏菜单项点击事件
    document.querySelectorAll('.sidebar-menu-item.has-submenu').forEach(item => {
        item.addEventListener('click', function(e) {
            // 阻止默认行为和冒泡
            e.preventDefault();
            e.stopPropagation();
            
            // 切换子菜单显示状态，不论当前页面是否激活
            const submenu = this.nextElementSibling;
            if (submenu && submenu.classList.contains('sidebar-submenu')) {
                submenu.classList.toggle('show');
                this.classList.toggle('open');
            }
            
            // 用户设置菜单不需要导航到页面，仅展开子菜单
            const hasSettingsIcon = this.querySelector('i.fa-cog');
            if (!hasSettingsIcon) {
                // 其他菜单可能需要导航，取决于具体实现
                const link = this.getAttribute('data-link');
                if (link) {
                    window.location.href = `/${link}`;
                }
            }
            
            return false;
        });
    });
    
    // 为没有子菜单的项目添加点击事件
    document.querySelectorAll('.sidebar-menu-item:not(.has-submenu)').forEach(item => {
        if (item.closest('.sidebar-submenu')) {
            // 子菜单中的项目
            item.addEventListener('click', function(e) {
                e.stopPropagation(); // 防止触发父菜单的点击事件
                
                const link = this.getAttribute('data-link');
                if (link) {
                    window.location.href = `/${link}`;
                }
            });
        } else {
            // 主菜单中没有子菜单的项目
            item.addEventListener('click', function() {
                const link = this.getAttribute('data-link');
                if (link) {
                    window.location.href = `/${link}`;
                }
            });
        }
    });
    
    // 设置当前活动菜单项
    setActiveMenuItem();
}

// 设置当前活动菜单项
function setActiveMenuItem() {
    const currentPath = window.location.pathname.slice(1) || 'index';
    
    // 移除所有活动状态
    document.querySelectorAll('.sidebar-menu-item').forEach(item => {
        item.classList.remove('active');
    });
    
    // 关闭所有子菜单
    document.querySelectorAll('.sidebar-submenu').forEach(submenu => {
        submenu.classList.remove('show');
    });
    document.querySelectorAll('.sidebar-menu-item.has-submenu').forEach(item => {
        item.classList.remove('open');
    });
    
    // 激活当前菜单项
    let activeItem = document.querySelector(`.sidebar-menu-item[data-link="${currentPath}"]`);
    
    // 特殊处理 user_management07082 路径
    if (currentPath === 'user_management07082' && !activeItem) {
        activeItem = document.querySelector(`.sidebar-menu-item[data-link="user_management07082"]`);
    }
    
    // 特殊处理渠道管理和全局配置页面
    if (currentPath === 'channel_management' || currentPath === 'global_settings') {
        const userSettingsMenu = document.querySelector('.sidebar-menu-item.has-submenu i.fa-cog').closest('.sidebar-menu-item');
        if (userSettingsMenu) {
            userSettingsMenu.classList.add('open'); // 只添加open，不添加active
            const submenu = userSettingsMenu.nextElementSibling;
            if (submenu && submenu.classList.contains('sidebar-submenu')) {
                submenu.classList.add('show');
            }
        }
    }
    
    if (activeItem) {
        activeItem.classList.add('active');
        
        // 如果是子菜单项，展开父菜单但不高亮父菜单
        const parentSubmenu = activeItem.closest('.sidebar-submenu');
        if (parentSubmenu) {
            parentSubmenu.classList.add('show');
            const parentItem = parentSubmenu.previousElementSibling;
            if (parentItem && parentItem.classList.contains('has-submenu')) {
                parentItem.classList.add('open'); // 只添加open，不添加active
            }
        }
    } else if (currentPath !== 'index') {
        // 处理嵌套路由的情况，例如 '/positions/123'
        // 可以根据具体的路由规则调整
        document.querySelectorAll('.sidebar-menu-item').forEach(item => {
            const link = item.getAttribute('data-link');
            if (link && currentPath.startsWith(link)) {
                item.classList.add('active');
                
                // 如果是子菜单项，展开父菜单但不高亮父菜单
                const parentSubmenu = item.closest('.sidebar-submenu');
                if (parentSubmenu) {
                    parentSubmenu.classList.add('show');
                    const parentItem = parentSubmenu.previousElementSibling;
                    if (parentItem && parentItem.classList.contains('has-submenu')) {
                        parentItem.classList.add('open'); // 只添加open，不添加active
                    }
                }
            }
        });
    }
}

// 表单验证
function initFormValidation() {
    const forms = document.querySelectorAll('form[data-validate="true"]');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(event) {
            let isValid = true;
            
            // 检查所有必填字段
            const requiredFields = form.querySelectorAll('[required]');
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    showFieldError(field, '此字段为必填项');
                } else {
                    clearFieldError(field);
                }
            });
            
            if (!isValid) {
                event.preventDefault();
            }
        });
    });
}

// 显示字段错误
function showFieldError(field, message) {
    // 清除已有的错误消息
    clearFieldError(field);
    
    // 创建错误消息元素
    const errorDiv = document.createElement('div');
    errorDiv.className = 'form-error';
    errorDiv.textContent = message;
    
    // 将错误消息添加到字段后面
    field.parentNode.appendChild(errorDiv);
    field.classList.add('form-control-error');
}

// 清除字段错误
function clearFieldError(field) {
    const parent = field.parentNode;
    const errorDiv = parent.querySelector('.form-error');
    
    if (errorDiv) {
        parent.removeChild(errorDiv);
    }
    
    field.classList.remove('form-control-error');
}

// 通用API调用函数
async function apiRequest(url, method = 'GET', data = null) {
    var options = {
        method: method,
        headers: {
            'Content-Type': 'application/json'
        }
    };

    const token = localStorage.getItem('accessToken');
    if (token) {
        options.headers['Authorization'] = `Bearer ${token}`;
    }
    
    if (data && (method === 'POST' || method === 'PUT')) {
        options.body = JSON.stringify(data);
    }
    
    try {
        const response = await fetch(API_BASE_URL + url, options);
        const result = await response.json();
        
        // 检查响应状态
        if (result.code && result.code !== 200) {
            // 服务器返回了错误信息
            const error = new Error(result.message || '请求失败');
            error.code = result.code;
            throw error;
        }
        
        if (!response.ok) {
            throw new Error('请求失败，服务器返回状态码: ' + response.status);
        }
        
        return result;
    } catch (error) {
        console.error('API请求错误:', error);
        throw error;
    }
}

// 显示通知
function showNotification(message, type = 'info') {
    // 移除现有通知
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => {
        notification.remove();
    });
    
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // 为错误通知添加样式
    if (type === 'error') {
        notification.style.backgroundColor = '#f44336';
        notification.style.color = 'white';
        notification.style.fontWeight = 'bold';
        notification.style.padding = '15px 20px';
        notification.style.zIndex = '9999';
    }
    
    document.body.appendChild(notification);
    
    // 错误消息显示时间更长
    const displayTime = type === 'error' ? 5000 : 2000;
    
    // 自动移除通知
    setTimeout(() => {
        notification.classList.add('notification-hide');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, displayTime);
}

// 格式化日期
function formatDate(dateString) {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// 确认对话框
function confirmAction(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

// 获取URL参数
function getUrlParam(param) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(param);
}

// 加载中效果
function showLoading(element) {
    const loadingDiv = document.createElement('div');
    loadingDiv.className = 'loading-spinner';
    element.appendChild(loadingDiv);
    element.classList.add('loading');
}

function hideLoading(element) {
    const loadingDiv = element.querySelector('.loading-spinner');
    if (loadingDiv) {
        element.removeChild(loadingDiv);
    }
    element.classList.remove('loading');
} 