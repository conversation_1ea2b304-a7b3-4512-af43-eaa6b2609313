// 分类器页面脚本
document.addEventListener('DOMContentLoaded', function() {
    // 初始化
    loadClassifiers();
    loadClassifierPrompts();
    initEventListeners();
});

// 全局变量
let semanticClassifiers = [];
let semanticCurrentPage = 1;
let semanticItemsPerPage = 10;
let semanticSearchTerm = '';
let answerClassifiers = [];
let answerCurrentPage = 1;
let answerItemsPerPage = 10;
let answerSearchTerm = '';
let currentQuestions = [];
let currentClassifier = null;

// 初始化事件监听
function initEventListeners() {
    // 语义分类器事件
    document.getElementById('addSemanticBtn').addEventListener('click', showSemanticModal);
    document.getElementById('saveSemanticBtn').addEventListener('click', function(event) {
        if (event) {
            event.preventDefault();
            event.stopPropagation();
        }
        saveSemanticClassifier();
    });
    document.getElementById('cancelSemanticBtn').addEventListener('click', hideSemanticModal);
    document.getElementById('saveSemanticPromptBtn').addEventListener('click', saveSemanticPrompt);
    
    // 语义分类器内容事件
    document.getElementById('addQuestionBtn').addEventListener('click', showQuestionEditModal);
    document.getElementById('saveQuestionBtn').addEventListener('click', function(event) {
        if (event) {
            event.preventDefault();
            event.stopPropagation();
        }
        saveQuestion();
    });
    document.getElementById('cancelQuestionBtn').addEventListener('click', hideQuestionEditModal);
    document.getElementById('closeQuestionsBtn').addEventListener('click', hideQuestionModal);
    
    // 答案分类器事件
    document.getElementById('addAnswerBtn').addEventListener('click', function(event) {
        if (event) {
            event.preventDefault();
        }
        showAnswerModal();
    });
    
    // 为保存答案分类器按钮添加事件监听器
    const saveAnswerBtn = document.getElementById('saveAnswerBtn');
    if (saveAnswerBtn) {
        saveAnswerBtn.onclick = function(event) {
            if (event) {
                event.preventDefault();
            }
            // 直接调用不带参数的函数
            saveAnswerClassifier();
            return false;
        };
    }
    
    document.getElementById('cancelAnswerBtn').addEventListener('click', hideAnswerModal);
    document.getElementById('saveAnswerPromptBtn').addEventListener('click', saveAnswerPrompt);
    
    // 搜索框事件
    document.getElementById('semanticSearchInput').addEventListener('input', function(e) {
        semanticSearchTerm = e.target.value;
        semanticCurrentPage = 1;
        renderSemanticClassifiers();
    });
    
    document.getElementById('answerSearchInput').addEventListener('input', function(e) {
        answerSearchTerm = e.target.value;
        answerCurrentPage = 1;
        renderAnswerClassifiers();
    });
    
    // 关闭模态框的x按钮
    const closeButtons = document.querySelectorAll('.modal-close');
    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const modal = this.closest('.modal');
            switch (modal.id) {
                case 'semanticModal':
                    hideSemanticModal();
                    break;
                case 'semanticQuestionModal':
                    hideQuestionModal();
                    break;
                case 'questionEditModal':
                    hideQuestionEditModal();
                    break;
                case 'answerModal':
                    hideAnswerModal();
                    break;
            }
        });
    });
}

// 加载所有分类器
async function loadClassifiers() {
    try {
        // 加载语义分类器
        const semanticResponse = await apiRequest('/semantic-classifiers');
        if (semanticResponse.code === 200) {
            semanticClassifiers = semanticResponse.data || [];
            renderSemanticClassifiers();
            updateSemanticPagination();
        } else {
            showNotification('error', '加载语义分类器失败: ' + semanticResponse.message);
        }
        
        // 加载答案分类器
        const answerResponse = await apiRequest('/answer-classifiers');
        if (answerResponse.code === 200) {
            answerClassifiers = answerResponse.data || [];
            renderAnswerClassifiers();
            updateAnswerPagination();
        } else {
            showNotification('error', '加载答案分类器失败: ' + answerResponse.message);
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('error', '加载分类器失败，请查看控制台了解详情');
    }
}

// 加载分类器提示词
async function loadClassifierPrompts() {
    try {
        const response = await apiRequest('/classifier-prompts');
        if (response.code === 200) {
            const prompts = response.data || {};
            
            // 设置语义分类器提示词
            const semanticPromptTextarea = document.getElementById('semantic_prompt');
            if (semanticPromptTextarea && prompts.semantic_prompt) {
                semanticPromptTextarea.value = prompts.semantic_prompt;
            }
            
            // 设置答案分类器提示词
            const answerPromptTextarea = document.getElementById('answer_prompt');
            if (answerPromptTextarea && prompts.answer_prompt) {
                answerPromptTextarea.value = prompts.answer_prompt;
            }
        } else {
            showNotification('error', '加载分类器提示词失败: ' + response.message);
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('error', '加载分类器提示词失败');
    }
}

// 渲染语义分类器表格
function renderSemanticClassifiers() {
    const tbody = document.querySelector('#semanticClassifierTable tbody');
    tbody.innerHTML = '';
    
    // 过滤数据
    let filteredClassifiers = semanticClassifiers;
    if (semanticSearchTerm) {
        const searchLower = semanticSearchTerm.toLowerCase();
        filteredClassifiers = semanticClassifiers.filter(classifier => 
            classifier.name.toLowerCase().includes(searchLower) ||
            (classifier.description && classifier.description.toLowerCase().includes(searchLower))
        );
    }
    
    // 计算分页
    const startIndex = (semanticCurrentPage - 1) * semanticItemsPerPage;
    const paginatedClassifiers = filteredClassifiers.slice(startIndex, startIndex + semanticItemsPerPage);
    
    if (filteredClassifiers.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = '<td colspan="5" class="text-center">暂无数据</td>';
        tbody.appendChild(row);
        
        // 填充空行，保持固定高度
        for (let i = 1; i < semanticItemsPerPage; i++) {
            const emptyRow = document.createElement('tr');
            emptyRow.classList.add('empty-row');
            tbody.appendChild(emptyRow);
        }
        return;
    }

    paginatedClassifiers.forEach((classifier, index) => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${startIndex + index + 1}</td>
            <td>${escapeHTML(classifier.name || '')}</td>
            <td>${classifier.question_count || 0}</td>
            <td>${formatDate(classifier.createdAt)}</td>
            <td>
                <button class="btn btn-action btn-questions" data-id="${classifier.id}">
                    <i class="fas fa-list"></i> 内容管理
                </button>
                <button class="btn btn-action btn-edit" data-id="${classifier.id}">
                    <i class="fas fa-edit"></i> 编辑
                </button>
                <button class="btn btn-action btn-delete" data-id="${classifier.id}">
                    <i class="fas fa-trash"></i> 删除
                </button>
            </td>
        `;

        // 绑定按钮事件
        const questionsBtn = row.querySelector('.btn-questions');
        const editBtn = row.querySelector('.btn-edit');
        const deleteBtn = row.querySelector('.btn-delete');
        
        questionsBtn.addEventListener('click', function() {
            const classifierId = this.getAttribute('data-id');
            openQuestionManager(classifierId);
        });
        
        editBtn.addEventListener('click', function() {
            const classifierId = this.getAttribute('data-id');
            editSemanticClassifier(classifierId);
        });
        
        deleteBtn.addEventListener('click', function() {
            const classifierId = this.getAttribute('data-id');
            deleteSemanticClassifier(classifierId);
        });

        tbody.appendChild(row);
    });
    
    // 填充剩余空行
    const remainingRows = semanticItemsPerPage - paginatedClassifiers.length;
    for (let i = 0; i < remainingRows; i++) {
        const emptyRow = document.createElement('tr');
        emptyRow.classList.add('empty-row');
        tbody.appendChild(emptyRow);
    }
}

// 更新语义分类器分页
function updateSemanticPagination() {
    const paginationContainer = document.getElementById('semanticPagination');
    if (!paginationContainer) return;
    
    // 过滤数据
    let filteredClassifiers = semanticClassifiers;
    if (semanticSearchTerm) {
        const searchLower = semanticSearchTerm.toLowerCase();
        filteredClassifiers = semanticClassifiers.filter(classifier => 
            classifier.name.toLowerCase().includes(searchLower) ||
            (classifier.description && classifier.description.toLowerCase().includes(searchLower))
        );
    }
    
    const totalPages = Math.ceil(filteredClassifiers.length / semanticItemsPerPage) || 1;
    
    // 确保当前页在有效范围内
    if (semanticCurrentPage > totalPages) {
        semanticCurrentPage = totalPages;
        renderSemanticClassifiers();
    }
    
    // 生成分页HTML
    const totalItems = filteredClassifiers.length;
    const startItem = Math.min(totalItems, (semanticCurrentPage - 1) * semanticItemsPerPage + 1);
    const endItem = Math.min(totalItems, semanticCurrentPage * semanticItemsPerPage);
    
    paginationContainer.innerHTML = `
        <div class="pagination-info">
            显示 ${startItem}-${endItem} 条，共 ${totalItems} 条
        </div>
        <div class="pagination-controls">
            <button class="btn btn-sm" ${semanticCurrentPage === 1 ? 'disabled' : ''} data-page="prev">
                <i class="fas fa-chevron-left"></i>
            </button>
            <span class="pagination-current">${semanticCurrentPage} / ${totalPages}</span>
            <button class="btn btn-sm" ${semanticCurrentPage === totalPages ? 'disabled' : ''} data-page="next">
                <i class="fas fa-chevron-right"></i>
            </button>
            <select class="form-select form-select-sm items-per-page">
                <option value="10" ${semanticItemsPerPage === 10 ? 'selected' : ''}>10条/页</option>
                <option value="20" ${semanticItemsPerPage === 20 ? 'selected' : ''}>20条/页</option>
                <option value="50" ${semanticItemsPerPage === 50 ? 'selected' : ''}>50条/页</option>
            </select>
        </div>
    `;
    
    // 添加分页事件
    const prevBtn = paginationContainer.querySelector('[data-page="prev"]');
    const nextBtn = paginationContainer.querySelector('[data-page="next"]');
    const itemsSelect = paginationContainer.querySelector('.items-per-page');
    
    if (prevBtn) {
        prevBtn.addEventListener('click', () => {
            if (semanticCurrentPage > 1) {
                semanticCurrentPage--;
                renderSemanticClassifiers();
                updateSemanticPagination();
            }
        });
    }
    
    if (nextBtn) {
        nextBtn.addEventListener('click', () => {
            if (semanticCurrentPage < totalPages) {
                semanticCurrentPage++;
                renderSemanticClassifiers();
                updateSemanticPagination();
            }
        });
    }
    
    if (itemsSelect) {
        itemsSelect.addEventListener('change', () => {
            semanticItemsPerPage = parseInt(itemsSelect.value);
            semanticCurrentPage = 1;
            renderSemanticClassifiers();
            updateSemanticPagination();
        });
    }
}

// 渲染答案分类器表格
function renderAnswerClassifiers() {
    const tbody = document.querySelector('#answerClassifierTable tbody');
    tbody.innerHTML = '';
    
    // 过滤数据
    let filteredClassifiers = answerClassifiers;
    if (answerSearchTerm) {
        const searchLower = answerSearchTerm.toLowerCase();
        filteredClassifiers = answerClassifiers.filter(classifier => 
            (classifier.name && classifier.name.toLowerCase().includes(searchLower)) ||
            (classifier.description && classifier.description.toLowerCase().includes(searchLower))
        );
    }
    
    // 计算分页
    const startIndex = (answerCurrentPage - 1) * answerItemsPerPage;
    const paginatedClassifiers = filteredClassifiers.slice(startIndex, startIndex + answerItemsPerPage);
    
    if (filteredClassifiers.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = '<td colspan="6" class="text-center">暂无数据</td>';
        tbody.appendChild(row);
        
        // 填充空行，保持固定高度
        for (let i = 1; i < answerItemsPerPage; i++) {
            const emptyRow = document.createElement('tr');
            emptyRow.classList.add('empty-row');
            tbody.appendChild(emptyRow);
        }
        return;
    }

    paginatedClassifiers.forEach((classifier, index) => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${startIndex + index + 1}</td>
            <td>${escapeHTML(classifier.name || '')}</td>
            <td>${classifier.active_multiplier || 0}</td>
            <td>${classifier.neutral_multiplier || 0}</td>
            <td>${classifier.negative_multiplier || 0}</td>
            <td>
                <button class="btn btn-action btn-edit" data-id="${classifier.id}">
                    <i class="fas fa-edit"></i> 编辑
                </button>
                <button class="btn btn-action btn-delete" data-id="${classifier.id}">
                    <i class="fas fa-trash"></i> 删除
                </button>
            </td>
        `;

        // 绑定按钮事件
        const editBtn = row.querySelector('.btn-edit');
        const deleteBtn = row.querySelector('.btn-delete');
        
        editBtn.addEventListener('click', function(e) {
            e.preventDefault();
            const classifierId = this.getAttribute('data-id');
            if (classifierId) {
                editAnswerClassifier(classifierId);
            }
        });
        
        deleteBtn.addEventListener('click', function(e) {
            e.preventDefault();
            const classifierId = this.getAttribute('data-id');
            if (classifierId) {
                deleteAnswerClassifier(classifierId);
            }
        });

        tbody.appendChild(row);
    });
    
    // 填充剩余空行
    const remainingRows = answerItemsPerPage - paginatedClassifiers.length;
    for (let i = 0; i < remainingRows; i++) {
        const emptyRow = document.createElement('tr');
        emptyRow.classList.add('empty-row');
        tbody.appendChild(emptyRow);
    }
}

// 更新答案分类器分页
function updateAnswerPagination() {
    const paginationContainer = document.getElementById('answerPagination');
    if (!paginationContainer) return;
    
    // 过滤数据
    let filteredClassifiers = answerClassifiers;
    if (answerSearchTerm) {
        const searchLower = answerSearchTerm.toLowerCase();
        filteredClassifiers = answerClassifiers.filter(classifier => 
            (classifier.name && classifier.name.toLowerCase().includes(searchLower)) ||
            (classifier.description && classifier.description.toLowerCase().includes(searchLower))
        );
    }
    
    const totalPages = Math.ceil(filteredClassifiers.length / answerItemsPerPage) || 1;
    
    // 确保当前页在有效范围内
    if (answerCurrentPage > totalPages) {
        answerCurrentPage = totalPages;
        renderAnswerClassifiers();
    }
    
    // 生成分页HTML
    const totalItems = filteredClassifiers.length;
    const startItem = Math.min(totalItems, (answerCurrentPage - 1) * answerItemsPerPage + 1);
    const endItem = Math.min(totalItems, answerCurrentPage * answerItemsPerPage);
    
    paginationContainer.innerHTML = `
        <div class="pagination-info">
            显示 ${startItem}-${endItem} 条，共 ${totalItems} 条
        </div>
        <div class="pagination-controls">
            <button class="btn btn-sm" ${answerCurrentPage === 1 ? 'disabled' : ''} data-page="prev">
                <i class="fas fa-chevron-left"></i>
            </button>
            <span class="pagination-current">${answerCurrentPage} / ${totalPages}</span>
            <button class="btn btn-sm" ${answerCurrentPage === totalPages ? 'disabled' : ''} data-page="next">
                <i class="fas fa-chevron-right"></i>
            </button>
            <select class="form-select form-select-sm items-per-page">
                <option value="10" ${answerItemsPerPage === 10 ? 'selected' : ''}>10条/页</option>
                <option value="20" ${answerItemsPerPage === 20 ? 'selected' : ''}>20条/页</option>
                <option value="50" ${answerItemsPerPage === 50 ? 'selected' : ''}>50条/页</option>
            </select>
        </div>
    `;
    
    // 添加分页事件
    const prevBtn = paginationContainer.querySelector('[data-page="prev"]');
    const nextBtn = paginationContainer.querySelector('[data-page="next"]');
    const itemsSelect = paginationContainer.querySelector('.items-per-page');
    
    if (prevBtn) {
        prevBtn.addEventListener('click', () => {
            if (answerCurrentPage > 1) {
                answerCurrentPage--;
                renderAnswerClassifiers();
                updateAnswerPagination();
            }
        });
    }
    
    if (nextBtn) {
        nextBtn.addEventListener('click', () => {
            if (answerCurrentPage < totalPages) {
                answerCurrentPage++;
                renderAnswerClassifiers();
                updateAnswerPagination();
            }
        });
    }
    
    if (itemsSelect) {
        itemsSelect.addEventListener('change', () => {
            answerItemsPerPage = parseInt(itemsSelect.value);
            answerCurrentPage = 1;
            renderAnswerClassifiers();
            updateAnswerPagination();
        });
    }
}

// 语义分类器模态框操作
function showSemanticModal() {
    const modal = document.getElementById('semanticModal');
    document.getElementById('semanticModalTitle').textContent = '新增语义分类器';
    document.getElementById('semanticForm').reset();
    document.getElementById('semanticId').value = '';
    modal.style.display = 'flex';
}

function hideSemanticModal() {
    const modal = document.getElementById('semanticModal');
    modal.style.display = 'none';
}

async function saveSemanticClassifier() {
    const form = document.getElementById('semanticForm');
    
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }
    
    const id = document.getElementById('semanticId').value;
    const name = document.getElementById('semantic_classifier_name').value;
    const description = document.getElementById('semantic_classifier_description').value;
    
    const classifier = {
        name,
        description
    };
    
    try {
        let response;
        
        if (id) {
            // 更新
            const token = localStorage.getItem('accessToken');
            response = await apiRequest(`/semantic-classifier/${id}`, 'PUT', classifier,{
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            if (response.code === 200) {
                showNotification('success', '语义分类器更新成功');
                await loadClassifiers();
                hideSemanticModal();
            } else {
                showNotification('error', '更新失败: ' + response.message);
            }
        } else {
            // 新增
            response = await apiRequest('/semantic-classifier', 'POST', classifier);
            if (response.code === 200) {
                showNotification('success', '语义分类器创建成功');
                await loadClassifiers();
                hideSemanticModal();
            } else {
                showNotification('error', '创建失败: ' + response.message);
            }
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('error', '操作失败: ' + error.message);
    }
}

async function editSemanticClassifier(id) {
    try {
        const response = await apiRequest(`/semantic-classifier/${id}`);
        if (response.code === 200) {
            const classifier = response.data;
            
            document.getElementById('semanticModalTitle').textContent = '编辑语义分类器';
            document.getElementById('semanticId').value = classifier.id;
            document.getElementById('semantic_classifier_name').value = classifier.name || '';
            document.getElementById('semantic_classifier_description').value = classifier.description || '';
            
            const modal = document.getElementById('semanticModal');
            modal.style.display = 'flex';
        } else {
            showNotification('error', '获取分类器详情失败: ' + response.message);
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('error', '获取分类器详情失败: ' + error.message);
    }
}

async function deleteSemanticClassifier(id) {
    if (confirm('确定要删除这个语义分类器吗？相关的内容也会被删除。')) {
        try {
            const token = localStorage.getItem('accessToken');
            const response = await apiRequest(`/semantic-classifier/${id}`, 'DELETE',{
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            if (response.code === 200) {
                showNotification('success', '语义分类器删除成功');
                await loadClassifiers();
            } else {
                showNotification('error', '删除失败: ' + response.message);
            }
        } catch (error) {
            console.error('Error:', error);
            showNotification('error', '删除失败: ' + error.message);
        }
    }
}

// 语义分类器内容管理
async function openQuestionManager(classifierId) {
    try {
        // 获取分类器详情
        const token = localStorage.getItem('accessToken');
        const classifierResponse = await apiRequest(`/semantic-classifier/${classifierId}`);
        if (classifierResponse.code !== 200) {
            showNotification('error', '获取分类器详情失败: ' + classifierResponse.message);
            return;
        }
        
        currentClassifier = classifierResponse.data;
        
        // 获取分类器内容列表
        const questionsResponse = await apiRequest(`/semantic-classifier/${classifierId}/questions`);
        if (questionsResponse.code !== 200) {
            showNotification('error', '获取分类器内容失败: ' + questionsResponse.message);
            return;
        }
        
        currentQuestions = questionsResponse.data || [];
        
        // 设置模态框标题和分类器信息
        document.getElementById('semanticQuestionModalTitle').textContent = `分类器内容管理 - ${currentClassifier.name}`;
        document.getElementById('currentClassifierName').textContent = currentClassifier.name;
        document.getElementById('currentClassifierDesc').textContent = currentClassifier.description || '暂无说明';
        
        // 渲染内容列表
        renderQuestionsList();
        
        // 显示模态框
        const modal = document.getElementById('semanticQuestionModal');
        modal.style.display = 'flex';
    } catch (error) {
        console.error('Error:', error);
        showNotification('error', '打开内容管理失败: ' + error.message);
    }
}

function renderQuestionsList() {
    const tbody = document.querySelector('#questionTable tbody');
    tbody.innerHTML = '';
    
    if (!currentQuestions || currentQuestions.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = '<td colspan="4" class="text-center">暂无内容数据</td>';
        tbody.appendChild(row);
        
        // 填充空行，保持固定高度
        for (let i = 1; i < 5; i++) {
            const emptyRow = document.createElement('tr');
            emptyRow.classList.add('empty-row');
            tbody.appendChild(emptyRow);
        }
        
        return;
    }
    
    currentQuestions.forEach((question, index) => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${index + 1}</td>
            <td>${escapeHTML(question.content || '')}</td>
            <td>${question.score}</td>
            <td>
                <button class="btn btn-action btn-edit" data-id="${question.id}">
                    <i class="fas fa-edit"></i> 编辑
                </button>
                <button class="btn btn-action btn-delete" data-id="${question.id}">
                    <i class="fas fa-trash"></i> 删除
                </button>
            </td>
        `;
        
        // 绑定按钮事件
        const editBtn = row.querySelector('.btn-edit');
        const deleteBtn = row.querySelector('.btn-delete');
        
        editBtn.addEventListener('click', function() {
            const questionId = this.getAttribute('data-id');
            editQuestion(questionId);
        });
        
        deleteBtn.addEventListener('click', function() {
            const questionId = this.getAttribute('data-id');
            deleteQuestion(questionId);
        });
        
        tbody.appendChild(row);
    });
    
    // 计算需要填充的空行数，确保表格高度一致
    const maxVisibleRows = 10; // 最大可见行数
    const remainingRows = Math.max(0, maxVisibleRows - currentQuestions.length);
    
    // 填充空行
    for (let i = 0; i < remainingRows; i++) {
        const emptyRow = document.createElement('tr');
        emptyRow.classList.add('empty-row');
        tbody.appendChild(emptyRow);
    }
}

function hideQuestionModal() {
    const modal = document.getElementById('semanticQuestionModal');
    modal.style.display = 'none';
    currentQuestions = [];
    currentClassifier = null;
}

// 内容编辑模态框操作
function showQuestionEditModal(questionId = null) {
    if (!currentClassifier) {
        showNotification('error', '未选择分类器');
        return;
    }
    
    const modal = document.getElementById('questionEditModal');
    document.getElementById('questionForm').reset();
    
    // Set modal title and ID field based on whether we're editing or adding
    const idField = document.getElementById('questionId');
    if (idField) {
        if (questionId && typeof questionId === 'string') {
            document.getElementById('questionEditModalTitle').textContent = '编辑内容';
            idField.value = questionId;
        } else {
            document.getElementById('questionEditModalTitle').textContent = '添加内容';
            idField.value = '';
        }
    }
    
    // Always set the classifier ID
    document.getElementById('questionClassifierId').value = currentClassifier.id;
    
    // Show the modal
    modal.style.display = 'flex';
}

function hideQuestionEditModal() {
    const modal = document.getElementById('questionEditModal');
    modal.style.display = 'none';
}

function editQuestion(questionId) {
    // Find the question by ID
    const question = currentQuestions.find(q => q.id === questionId);
    if (!question) {
        showNotification('error', '未找到内容数据');
        return;
    }
    
    // Set form values
    const idField = document.getElementById('questionId');
    if (idField) {
        idField.value = question.id || '';
    }
    
    document.getElementById('question_content').value = question.content || '';
    document.getElementById('question_score').value = question.score || 0;
    
    // Show modal with question ID
    showQuestionEditModal(question.id);
}

async function saveQuestion() {
    const form = document.getElementById('questionForm');
    
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }
    
    // Get values from form
    const idField = document.getElementById('questionId');
    const classifierId = document.getElementById('questionClassifierId').value;
    const content = document.getElementById('question_content').value;
    const score = parseFloat(document.getElementById('question_score').value);
    
    // Check if we have a valid ID string
    const id = idField && idField.value && typeof idField.value === 'string' && idField.value.trim() !== '' 
        ? idField.value.trim() 
        : null;
    
    const question = {
        classifier_id: classifierId,
        content,
        score
    };
    
    try {
        let response;
        let url;
        let method;
        
        if (id) {
            // Update existing question
            url = `/semantic-classifier-question/${id}`;
            method = 'PUT';
        } else {
            // Create new question
            url = '/semantic-classifier-question';
            method = 'POST';
        }
        
        const token = localStorage.getItem('accessToken');
        response = await apiRequest(url, method, question,{
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (response.code === 200) {
            showNotification('success', id ? '内容更新成功' : '内容创建成功');
            await refreshQuestions();
            hideQuestionEditModal();
        } else {
            showNotification('error', (id ? '更新' : '创建') + '失败: ' + response.message);
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('error', '操作失败: ' + error.message);
    }
}

async function deleteQuestion(questionId) {
    if (confirm('确定要删除这个内容吗？')) {
        try {
            const token = localStorage.getItem('accessToken');
            const response = await apiRequest(`/semantic-classifier-question/${questionId}`, 'DELETE',{
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            if (response.code === 200) {
                showNotification('success', '内容删除成功');
                await refreshQuestions();
            } else {
                showNotification('error', '删除失败: ' + response.message);
            }
        } catch (error) {
            console.error('Error:', error);
            showNotification('error', '删除失败: ' + error.message);
        }
    }
}

async function refreshQuestions() {
    if (!currentClassifier) return;
    
    try {
        const response = await apiRequest(`/semantic-classifier/${currentClassifier.id}/questions`);
        if (response.code === 200) {
            currentQuestions = response.data || [];
            renderQuestionsList();
            
            // 刷新语义分类器列表以更新内容数量
            await loadClassifiers();
        } else {
            showNotification('error', '刷新内容列表失败: ' + response.message);
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('error', '刷新内容列表失败: ' + error.message);
    }
}

// 答案分类器模态框操作
function showAnswerModal(id = null) {
    console.log("showAnswerModal called with id:", id);
    
    const modal = document.getElementById('answerModal');
    if (!modal) {
        console.error("Modal not found");
        return;
    }
    
    const form = document.getElementById('answerForm');
    if (!form) {
        console.error("Form not found");
        return;
    }
    
    // 重置表单
    form.reset();
    
    // 获取 ID 字段
    const idField = document.getElementById('answerId');
    if (idField) {
        // 如果提供了 ID，设置它；否则清空
        if (id && typeof id === 'string') {
            console.log("Setting ID field to:", id);
            idField.value = id;
        } else {
            console.log("Clearing ID field");
            idField.value = '';
        }
    } else {
        console.error("ID field not found");
    }
    
    // 设置模态框标题
    const titleElement = document.getElementById('answerModalTitle');
    if (titleElement) {
        titleElement.textContent = id ? '编辑答案分类器' : '新增答案分类器';
    }
    
    // 设置默认值
    const activeField = document.getElementById('active_multiplier');
    const neutralField = document.getElementById('neutral_multiplier');
    const negativeField = document.getElementById('negative_multiplier');
    
    if (activeField) activeField.value = "1.0";
    if (neutralField) neutralField.value = "1.0";
    if (negativeField) negativeField.value = "1.0";
    
    // 显示模态框
    modal.style.display = 'flex';
}

function hideAnswerModal() {
    const modal = document.getElementById('answerModal');
    modal.style.display = 'none';
}

async function editAnswerClassifier(id) {
    console.log("editAnswerClassifier called with id:", id);
    
    // 验证 ID
    if (!id || typeof id !== 'string') {
        console.error("Invalid ID:", id);
        showNotification('error', '无效的分类器ID');
        return;
    }
    
    try {
        // 获取分类器详情
        const token = localStorage.getItem('accessToken');
        const response = await apiRequest(`/answer-classifier/${id}`);
        console.log("API response:", response);
        
        if (response.code === 200) {
            const classifier = response.data;
            
            // 显示模态框
            showAnswerModal();
            
            // 设置表单字段
            setTimeout(() => {
                const idField = document.getElementById('answerId');
                if (idField) {
                    idField.value = classifier.id || '';
                }
                
                const nameField = document.getElementById('answer_name');
                if (nameField) {
                    nameField.value = classifier.name || '';
                }
                
                const descField = document.getElementById('answer_description');
                if (descField) {
                    descField.value = classifier.description || '';
                }
                
                const activeField = document.getElementById('active_multiplier');
                if (activeField) {
                    activeField.value = classifier.active_multiplier || 0;
                }
                
                const neutralField = document.getElementById('neutral_multiplier');
                if (neutralField) {
                    neutralField.value = classifier.neutral_multiplier || 0;
                }
                
                const negativeField = document.getElementById('negative_multiplier');
                if (negativeField) {
                    negativeField.value = classifier.negative_multiplier || 0;
                }
                
                // 更新模态框标题
                const titleElement = document.getElementById('answerModalTitle');
                if (titleElement) {
                    titleElement.textContent = '编辑答案分类器';
                }
            }, 100);
        } else {
            showNotification('error', '获取答案分类器详情失败: ' + response.message);
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('error', '获取答案分类器详情失败: ' + error.message);
    }
}

// 保存答案分类器
async function saveAnswerClassifier() {
    console.log("saveAnswerClassifier called");
    
    // 获取表单
    const form = document.getElementById('answerForm');
    if (!form) {
        console.error("Form not found");
        showNotification('error', '表单不存在');
        return;
    }
    
    // 验证表单
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }
    
    // 获取表单字段
    const idField = document.getElementById('answerId');
    const nameField = document.getElementById('answer_name');
    const descField = document.getElementById('answer_description');
    const activeField = document.getElementById('active_multiplier');
    const neutralField = document.getElementById('neutral_multiplier');
    const negativeField = document.getElementById('negative_multiplier');
    
    // 验证必要字段存在
    if (!nameField || !activeField || !neutralField || !negativeField) {
        console.error("Required fields not found");
        showNotification('error', '表单字段不完整');
        return;
    }
    
    // 获取表单值
    const id = idField && idField.value ? String(idField.value).trim() : '';
    const name = nameField.value;
    const description = descField ? descField.value : '';
    const activeMultiplier = parseFloat(activeField.value);
    const neutralMultiplier = parseFloat(neutralField.value);
    const negativeMultiplier = parseFloat(negativeField.value);
    
    // 创建分类器对象
    const classifier = {
        name,
        description,
        active_multiplier: activeMultiplier,
        neutral_multiplier: neutralMultiplier,
        negative_multiplier: negativeMultiplier
    };
    
    console.log("Saving classifier:", classifier);
    console.log("ID:", id);
    
    try {
        let url, method;
        
        // 根据是否有ID决定是创建还是更新
        if (id) {
            url = `/answer-classifier/${id}`;
            method = 'PUT';
            console.log("Updating classifier with ID:", id);
        } else {
            url = '/answer-classifier';
            method = 'POST';
            console.log("Creating new classifier");
        }
        
        // 发送请求
        const token = localStorage.getItem('accessToken');
        const response = await apiRequest(url, method, classifier,{
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        console.log("API response:", response);
        
        if (response.code === 200) {
            showNotification('success', id ? '答案分类器更新成功' : '答案分类器创建成功');
            await loadClassifiers();
            hideAnswerModal();
        } else {
            showNotification('error', (id ? '更新' : '创建') + '失败: ' + response.message);
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('error', '操作失败: ' + error.message);
    }
}

async function deleteAnswerClassifier(id) {
    // 确保 ID 是有效的字符串
    if (!id || typeof id !== 'string') {
        showNotification('error', '无效的分类器ID');
        return;
    }
    
    if (confirm('确定要删除这个答案分类器吗？')) {
        try {
            const token = localStorage.getItem('accessToken');
            const response = await apiRequest(`/answer-classifier/${id}`, 'DELETE',{
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            if (response.code === 200) {
                showNotification('success', '答案分类器删除成功');
                await loadClassifiers();
            } else {
                showNotification('error', '删除失败: ' + response.message);
            }
        } catch (error) {
            console.error('Error:', error);
            showNotification('error', '删除失败: ' + error.message);
        }
    }
}

// 保存语义分类器提示词
async function saveSemanticPrompt() {
    const prompt = document.getElementById('semantic_prompt').value;
    
    try {
        const response = await apiRequest('/update-config', 'POST', {
            semantic_prompt: prompt
        });
        
        if (response.code === 200) {
            showNotification('success', '语义分类器提示词保存成功');
        } else {
            showNotification('error', '保存失败: ' + response.message);
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('error', '保存失败: ' + error.message);
    }
}

// 保存答案分类器提示词
async function saveAnswerPrompt() {
    const prompt = document.getElementById('answer_prompt').value;
    
    try {
        const response = await apiRequest('/update-config', 'POST', {
            answer_prompt: prompt
        });
        
        if (response.code === 200) {
            showNotification('success', '答案分类器提示词保存成功');
        } else {
            showNotification('error', '保存失败: ' + response.message);
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('error', '保存失败: ' + error.message);
    }
}

// 辅助函数
function formatDate(dateString) {
    if (!dateString) return '';
    
    try {
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (e) {
        return dateString;
    }
}

function escapeHTML(str) {
    if (!str) return '';
    return String(str)
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#039;');
}

// API请求辅助函数
async function apiRequest(url, method = 'GET', data = null) {
    var options = {
        method: method,
        headers: {
            'Content-Type': 'application/json'
        }
    };
    const token = localStorage.getItem('accessToken');
    if (token) {
        options.headers['Authorization'] = `Bearer ${token}`;
    }
    
    if (data && (method === 'POST' || method === 'PUT')) {
        options.body = JSON.stringify(data);
    }
    
    try {
        const response = await fetch(url, options);
        const result = await response.json();
        
        if (!response.ok) {
            throw new Error('请求失败，服务器返回状态码: ' + response.status);
        }
        
        return result;
    } catch (error) {
        console.error('API请求错误:', error);
        throw error;
    }
}

// 通知提示函数
function showNotification(type, message) {
    // 如果页面有通知组件则使用
    if (typeof showToast === 'function') {
        showToast(message, type);
        return;
    }
    
    // 否则使用简单的alert
    console.log(`[${type}] ${message}`);
    alert(message);
} 