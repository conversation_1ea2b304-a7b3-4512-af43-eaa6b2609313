// APIKey创建和管理页面逻辑

document.addEventListener('DOMContentLoaded', function() {
    // 获取DOM元素
    const createApikeyForm = document.getElementById('createApikeyForm');
    const apiKeyTable = document.getElementById('apiKeyTable');
    const apiKeyList = document.getElementById('apiKeyList');
    const apiKeyListLoading = document.getElementById('apiKeyListLoading');
    const apiKeyListEmpty = document.getElementById('apiKeyListEmpty');
    
    // 加载API Key列表
    loadApiKeys();
    
    // 绑定创建表单提交事件
    if (createApikeyForm) {
        createApikeyForm.addEventListener('submit', function(e) {
            e.preventDefault();
            createApiKey();
        });
    }
    
    // 加载APIKey列表
    function loadApiKeys() {
        // 显示加载状态
        apiKeyListLoading.style.display = 'flex';
        apiKeyTable.style.display = 'none';
        apiKeyListEmpty.style.display = 'none';
        
        // 调用API
        API.apikey.getAll().then(response => {
            // 隐藏加载状态
            apiKeyListLoading.style.display = 'none';
            
            if (response.success && response.data && response.data.length > 0) {
                // 显示表格
                apiKeyTable.style.display = 'table';
                
                // 清空现有列表
                apiKeyList.innerHTML = '';
                
                // 添加APIKey到列表
                response.data.forEach(apikey => {
                    const row = document.createElement('tr');
                    
                    // 名称列
                    const nameCell = document.createElement('td');
                    nameCell.textContent = apikey.name;
                    row.appendChild(nameCell);
                    
                    // APIKey列
                    const keyCell = document.createElement('td');
                    const keySpan = document.createElement('span');
                    keySpan.className = 'apikey-display';
                    keySpan.textContent = apikey.displayed_key || '********';
                    keyCell.appendChild(keySpan);
                    row.appendChild(keyCell);
                    
                    // 创建时间列
                    const createdAtCell = document.createElement('td');
                    createdAtCell.textContent = formatDate(apikey.createdAt);
                    row.appendChild(createdAtCell);
                    
                    // 操作列
                    const actionCell = document.createElement('td');
                    const deleteBtn = document.createElement('button');
                    deleteBtn.className = 'btn btn-danger btn-sm';
                    deleteBtn.innerHTML = '<i class="fas fa-trash-alt"></i> 删除';
                    deleteBtn.onclick = function() {
                        confirmDeleteApiKey(apikey.id, apikey.name);
                    };
                    actionCell.appendChild(deleteBtn);
                    row.appendChild(actionCell);
                    
                    apiKeyList.appendChild(row);
                });
            } else {
                // 显示空列表提示
                apiKeyListEmpty.style.display = 'flex';
            }
        }).catch(error => {
            console.error('获取APIKey列表失败:', error);
            apiKeyListLoading.style.display = 'none';
            apiKeyListEmpty.style.display = 'flex';
            apiKeyListEmpty.innerHTML = '<i class="fas fa-exclamation-circle"></i> 获取APIKey列表失败，请刷新页面重试';
        });
    }
    
    // 创建APIKey
    function createApiKey() {
        const nameInput = document.getElementById('apiKeyName');
        const name = nameInput.value.trim();
        
        if (!name) {
            alert('请输入APIKey名称');
            return;
        }
        
        // 禁用提交按钮
        const submitBtn = createApikeyForm.querySelector('button[type="submit"]');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 创建中...';
        
        // 调用API
        API.apikey.create(name).then(response => {
            if (response.success) {
                showSuccessModal(response.data);
                nameInput.value = ''; // 清空输入框
                loadApiKeys(); // 重新加载列表
            } else {
                alert('创建APIKey失败: ' + (response.message || '未知错误'));
            }
        }).catch(error => {
            console.error('创建APIKey失败:', error);
            alert('创建APIKey失败，请重试');
        }).finally(() => {
            // 恢复按钮状态
            submitBtn.disabled = false;
            submitBtn.innerHTML = '创建APIKey';
        });
    }
    
    // 确认删除APIKey
    function confirmDeleteApiKey(id, name) {
        if (confirm(`确定要删除APIKey "${name}" 吗？此操作不可逆，会导致使用该APIKey的应用无法正常工作。`)) {
            deleteApiKey(id);
        }
    }
    
    // 删除APIKey
    function deleteApiKey(id) {
        API.apikey.delete(id).then(response => {
            if (response.success) {
                loadApiKeys(); // 重新加载列表
                alert('APIKey删除成功');
            } else {
                alert('删除APIKey失败: ' + (response.message || '未知错误'));
            }
        }).catch(error => {
            console.error('删除APIKey失败:', error);
            alert('删除APIKey失败，请重试');
        });
    }
    
    // 显示APIKey创建成功模态框
    function showSuccessModal(apiKeyData) {
        // 如果有SweetAlert2库可以使用更漂亮的模态框
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'APIKey创建成功',
                html: `
                    <p>请保存您的APIKey，它只会显示一次：</p>
                    <div style="background:#f5f5f5;padding:10px;border-radius:4px;margin:10px 0;word-break:break-all;font-family:monospace;">
                        ${apiKeyData.apikey}
                    </div>
                    <p>请将此密钥保存在安全的地方。</p>
                `,
                icon: 'success',
                confirmButtonText: '我已保存'
            });
        } else {
            alert(`APIKey创建成功！请保存您的APIKey(只显示一次)：${apiKeyData.apikey}`);
        }
    }
    
    // 日期格式化
    function formatDate(dateString) {
        try {
            const date = new Date(dateString);
            return date.toLocaleString();
        } catch (e) {
            return dateString;
        }
    }
}); 