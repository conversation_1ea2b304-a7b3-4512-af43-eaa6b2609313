// 候选人管理页面脚本
document.addEventListener('DOMContentLoaded', function() {
    // 初始化候选人列表
    initCandidatePage();
});

// 状态变量
let candidates = [];
let positions = [];
let currentPage = 1;
let pageSize = 10;
let totalPages = 1;
let currentFilters = {
    search: '',
    position: '',
    sortBy: 'newest'
};

// 初始化候选人页面
async function initCandidatePage() {
    // 绑定事件监听
    bindEvents();
    
    // 加载职位数据（用于筛选器和表单）
    await loadPositions();
    
    // 加载候选人数据
    await loadCandidates();
}

// 绑定事件监听
function bindEvents() {
    // 新增候选人按钮
    const addCandidateBtn = document.getElementById('addCandidateBtn');
    addCandidateBtn.addEventListener('click', showAddCandidateModal);

    // 搜索按钮
    const searchBtn = document.getElementById('searchBtn');
    searchBtn.addEventListener('click', handleSearch);

    // 搜索输入框回车事件
    const searchInput = document.getElementById('searchInput');
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            handleSearch();
        }
    });

    // 添加痛点按钮
    const addConcernBtn = document.getElementById('addConcernBtn');
    if (addConcernBtn) {
        addConcernBtn.addEventListener('click', addConcern);
    }

    // 痛点输入框回车事件
    const concernsInput = document.getElementById('concernsInput');
    if (concernsInput) {
        concernsInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                addConcern();
            }
        });
    }
    
    // 职位筛选器变化事件
    const positionFilter = document.getElementById('positionFilter');
    positionFilter.addEventListener('change', handleFilterChange);
    
    // 排序方式变化事件
    const sortBy = document.getElementById('sortBy');
    sortBy.addEventListener('change', handleFilterChange);
    
    // 模态框关闭按钮
    const modalCloseButtons = document.querySelectorAll('.modal-close');
    modalCloseButtons.forEach(button => {
        button.addEventListener('click', closeAllModals);
    });
    
    // 取消按钮
    const cancelBtn = document.getElementById('cancelBtn');
    cancelBtn.addEventListener('click', closeAllModals);
    
    // 保存候选人按钮
    const saveCandidateBtn = document.getElementById('saveCandidateBtn');
    saveCandidateBtn.addEventListener('click', saveCandidate);
    
    // 详情模态框关闭按钮
    const closeDetailBtn = document.getElementById('closeDetailBtn');
    closeDetailBtn.addEventListener('click', closeAllModals);
    
    // 编辑候选人按钮
    const editCandidateBtn = document.getElementById('editCandidateBtn');
    editCandidateBtn.addEventListener('click', function() {
        // 获取当前候选人ID
        const candidateId = document.getElementById('candidateDetails').getAttribute('data-id');
        if (candidateId) {
            // 先关闭详情模态框，再打开编辑模态框
            document.getElementById('candidateDetailModal').style.display = 'none';
            openEditCandidateModal(candidateId);
        }
    });
    
    // 开始面试按钮
    // const startInterviewBtn = document.getElementById('startInterviewBtn');
    // startInterviewBtn.addEventListener('click', function() {
    //     const candidateId = document.getElementById('candidateDetails').getAttribute('data-id');
    //     if (candidateId) {
    //         startInterview(candidateId);
    //     }
    // });
}

// 加载职位数据
async function loadPositions() {
    try {
        // 从API获取职位数据
        positions = await API.position.getAll() || [];
        
        // 填充职位筛选器
        populatePositionFilter();
        
        // 填充职位选择器（添加/编辑表单用）
        populatePositionSelect();
    } catch (error) {
        console.error('加载职位数据失败:', error);
        showNotification('加载职位数据失败', 'error');
    }
}

// 填充职位筛选器
function populatePositionFilter() {
    const positionFilter = document.getElementById('positionFilter');
    
    // 清空现有选项（保留"全部"选项）
    while (positionFilter.options.length > 1) {
        positionFilter.remove(1);
    }
    
    // 添加职位选项
    positions.forEach(position => {
        const option = document.createElement('option');
        option.value = position.dataId;
        option.textContent = position.positionName || '未命名职位';
        positionFilter.appendChild(option);
    });
}

// 填充职位选择器（表单用）
function populatePositionSelect() {
    const positionSelect = document.getElementById('positionId');
    
    // 清空现有选项（保留"请选择"选项）
    while (positionSelect.options.length > 1) {
        positionSelect.remove(1);
    }
    
    // 添加职位选项
    positions.forEach(position => {
        const option = document.createElement('option');
        option.value = position.dataId;
        option.textContent = position.positionName || '未命名职位';
        positionSelect.appendChild(option);
    });
}

// 加载候选人数据
async function loadCandidates() {
    try {
        const candidateList = document.getElementById('candidateList');
        
        // 显示加载状态
        candidateList.innerHTML = `
            <div class="candidate-list-loading">
                <p>加载中...</p>
            </div>
        `;
        
        // 从API获取候选人数据
        const allCandidates = await API.user.getAll();
        
        // 如果没有候选人数据，显示空状态
        if (!allCandidates || allCandidates.length === 0) {
            candidateList.innerHTML = `
                <div class="candidate-empty">
                    <i class="fas fa-user-tie"></i>
                    <p>暂无候选人数据</p>
                    <button class="btn btn-primary mt-10" id="emptyAddBtn">
                        <i class="fas fa-plus"></i> 添加候选人
                    </button>
                </div>
            `;
            
            // 绑定空状态下的添加按钮
            const emptyAddBtn = document.getElementById('emptyAddBtn');
            if (emptyAddBtn) {
                emptyAddBtn.addEventListener('click', showAddCandidateModal);
            }
            
            return;
        }
        
        // 应用筛选和排序
        candidates = filterAndSortCandidates(allCandidates);
        
        // 计算分页
        totalPages = Math.ceil(candidates.length / pageSize);
        
        // 渲染候选人列表
        renderCandidateList();
        
        // 渲染分页
        renderPagination();
    } catch (error) {
        console.error('加载候选人数据失败:', error);
        
        const candidateList = document.getElementById('candidateList');
        candidateList.innerHTML = `
            <div class="candidate-empty">
                <i class="fas fa-exclamation-circle"></i>
                <p>加载候选人数据失败</p>
                <button class="btn btn-primary mt-10" id="retryBtn">
                    <i class="fas fa-redo"></i> 重试
                </button>
            </div>
        `;
        
        // 绑定重试按钮
        const retryBtn = document.getElementById('retryBtn');
        if (retryBtn) {
            retryBtn.addEventListener('click', loadCandidates);
        }
    }
}

// 筛选和排序候选人
function filterAndSortCandidates(candidateList) {
    // 复制数组以避免修改原数组
    let filtered = [...candidateList];
    
    // 应用搜索筛选
    if (currentFilters.search) {
        const searchLower = currentFilters.search.toLowerCase();
        filtered = filtered.filter(candidate => 
            (candidate.name && candidate.name.toLowerCase().includes(searchLower)) ||
            (candidate.phone && candidate.phone.includes(searchLower)) ||
            (candidate.email && candidate.email.toLowerCase().includes(searchLower))
        );
    }
    
    // 应用职位筛选
    if (currentFilters.position) {
        filtered = filtered.filter(candidate => 
            candidate.positionId === currentFilters.position
        );
    }
    
    // 应用排序
    switch (currentFilters.sortBy) {
        case 'name':
            filtered.sort((a, b) => (a.name || '').localeCompare(b.name || ''));
            break;
        case 'score':
            filtered.sort((a, b) => (b.score || 0) - (a.score || 0));
            break;
        case 'newest':
        default:
            filtered.sort((a, b) => new Date(b.createdAt || 0) - new Date(a.createdAt || 0));
            break;
    }
    
    return filtered;
}

// 渲染候选人列表
function renderCandidateList() {
    const candidateList = document.getElementById('candidateList');
    
    // 计算当前页的候选人
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, candidates.length);
    const currentPageCandidates = candidates.slice(startIndex, endIndex);
    
    // 清空列表
    candidateList.innerHTML = '';
    
    // 渲染每个候选人
    currentPageCandidates.forEach(candidate => {
        // 获取职位名称
        let positionName = '未知职位';
        if (candidate.positionId) {
            const position = positions.find(p => p.dataId === candidate.positionId);
            if (position) {
                positionName = position.positionName || '未知职位';
            }
        }
        
        // 解析来源
        let sourceText = getSourceLabel(candidate.source);
        
        // 创建候选人卡片
        const candidateCard = document.createElement('div');
        candidateCard.className = 'candidate-card';
        candidateCard.setAttribute('data-id', candidate.userid);
        
        // 获取首字母作为头像占位符
        const initial = (candidate.name && candidate.name.charAt(0)) || '?';
        
        candidateCard.innerHTML = `
            <div class="candidate-card-inner">
                <div class="candidate-source-tag" data-source="${candidate.source || 'other'}">${sourceText}</div>
                <div class="candidate-avatar">
                    <div class="avatar-placeholder">${initial}</div>
                </div>
                <div class="candidate-info">
                    <div class="candidate-header">
                        <div class="candidate-name">
                            ${candidate.name || '未知姓名'}
                            ${candidate.title ? `<span class="candidate-title-tag">${candidate.title}</span>` : ''}
                            ${candidate.real_name ? `<span class="candidate-realname-tag">实名: ${candidate.real_name}</span>` : ''}
                        </div>
                        <div class="candidate-meta">
                            <span class="candidate-gender-age">
                                ${candidate.gender ? `<i class="fas fa-${candidate.gender === '男' ? 'mars' : 'venus'}"></i> ${candidate.gender}` : ''}
                                ${candidate.age ? `${candidate.gender ? ' · ' : ''}<i class="fas fa-user"></i> ${candidate.age}岁` : ''}
                            </span>
                            <span class="candidate-city">
                                ${candidate.city ? `<i class="fas fa-map-marker-alt"></i> ${candidate.city}` : ''}
                            </span>
                        </div>
                        <div class="candidate-details">
                            <div class="candidate-detail-item">
                                <i class="fas fa-id-card"></i>
                                <span>实名: ${candidate.real_name || '未填写'}</span>
                            </div>
                            <div class="candidate-detail-item">
                                <i class="fas fa-phone"></i>
                                <span>${candidate.phone || '未填写'}</span>
                            </div>
                            <div class="candidate-detail-item">
                                <i class="fas fa-comment"></i>
                                <span>${candidate.wechat_nickname || candidate.wechat || '未填写'}</span>
                            </div>
                            <div class="candidate-detail-item">
                                <i class="fas fa-graduation-cap"></i>
                                <span>${candidate.education_experience ? `${candidate.education_experience}${candidate.graduate_school ? ` - ${candidate.graduate_school}` : ''}` : '未填写'}</span>
                            </div>
                            <div class="candidate-detail-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>${candidate.city || '未填写'}</span>
                            </div>
                            <div class="candidate-detail-item">
                                <i class="fas fa-home"></i>
                                <span>${candidate.address || '未填写'}</span>
                            </div>
                        </div>
                    </div>
                    <div class="candidate-tag-container">
                        <div class="candidate-tag candidate-position">
                            <i class="fas fa-briefcase"></i>
                            <span>${positionName}</span>
                        </div>
                        <div class="candidate-tag candidate-source">
                            <i class="fas fa-tag"></i>
                            <span>${getSourceLabel(candidate.source || 'other')}</span>
                        </div>
                    </div>
                    <div class="candidate-creation-time">
                        <i class="fas fa-calendar-alt"></i>
                        <span>创建时间: ${formatDate(candidate.createdAt)}</span>
                    </div>
                </div>
                <div class="candidate-actions">
                    <div class="candidate-score-display">
                        <div class="candidate-score">${candidate.score || '-'}</div>
                        <div class="candidate-score-label">评分</div>
                    </div>
                    <div class="candidate-buttons">
                        <button class="btn btn-sm btn-light action-btn action-btn-view" data-id="${candidate.userid}">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-light action-btn action-btn-edit" data-id="${candidate.userid}">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-light action-btn action-btn-delete" data-id="${candidate.userid}">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        // 添加事件监听
        const viewBtn = candidateCard.querySelector('.action-btn-view');
        viewBtn.addEventListener('click', function(e) {
            e.preventDefault();
            showCandidateDetail(this.getAttribute('data-id'));
        });
        
        const editBtn = candidateCard.querySelector('.action-btn-edit');
        editBtn.addEventListener('click', function(e) {
            e.preventDefault();
            openEditCandidateModal(this.getAttribute('data-id'));
        });
        
        const deleteBtn = candidateCard.querySelector('.action-btn-delete');
        deleteBtn.addEventListener('click', function(e) {
            e.preventDefault();
            confirmDeleteCandidate(this.getAttribute('data-id'));
        });
        
        candidateList.appendChild(candidateCard);
    });
}

// 渲染分页
function renderPagination() {
    const paginationContainer = document.getElementById('pagination');
    
    // 如果总页数为1，不显示分页
    if (totalPages <= 1) {
        paginationContainer.innerHTML = '';
        return;
    }
    
    // 创建分页HTML
    let paginationHTML = '<ul class="pagination">';
    
    // 上一页按钮
    paginationHTML += `
        <li class="pagination-item">
            <a class="pagination-link ${currentPage === 1 ? 'disabled' : ''}" id="prevPage">
                <i class="fas fa-chevron-left"></i>
            </a>
        </li>
    `;
    
    // 页码按钮
    const maxPageButtons = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxPageButtons / 2));
    let endPage = Math.min(totalPages, startPage + maxPageButtons - 1);
    
    if (endPage - startPage + 1 < maxPageButtons) {
        startPage = Math.max(1, endPage - maxPageButtons + 1);
    }
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `
            <li class="pagination-item">
                <a class="pagination-link ${i === currentPage ? 'active' : ''}" data-page="${i}">${i}</a>
            </li>
        `;
    }
    
    // 下一页按钮
    paginationHTML += `
        <li class="pagination-item">
            <a class="pagination-link ${currentPage === totalPages ? 'disabled' : ''}" id="nextPage">
                <i class="fas fa-chevron-right"></i>
            </a>
        </li>
    `;
    
    paginationHTML += '</ul>';
    
    // 渲染分页
    paginationContainer.innerHTML = paginationHTML;
    
    // 绑定分页事件
    const pageLinks = document.querySelectorAll('.pagination-link[data-page]');
    pageLinks.forEach(link => {
        link.addEventListener('click', function() {
            const page = parseInt(this.getAttribute('data-page'));
            if (page !== currentPage) {
                currentPage = page;
                renderCandidateList();
                renderPagination();
                // 滚动到顶部
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }
        });
    });
    
    // 绑定上一页按钮
    const prevPageBtn = document.getElementById('prevPage');
    if (prevPageBtn && currentPage > 1) {
        prevPageBtn.addEventListener('click', function() {
            currentPage--;
            renderCandidateList();
            renderPagination();
            // 滚动到顶部
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });
    }
    
    // 绑定下一页按钮
    const nextPageBtn = document.getElementById('nextPage');
    if (nextPageBtn && currentPage < totalPages) {
        nextPageBtn.addEventListener('click', function() {
            currentPage++;
            renderCandidateList();
            renderPagination();
            // 滚动到顶部
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });
    }
}

// 处理搜索
function handleSearch() {
    const searchInput = document.getElementById('searchInput');
    currentFilters.search = searchInput.value.trim();
    currentPage = 1; // 重置为第一页
    
    // 重新筛选和排序
    candidates = filterAndSortCandidates(candidates);
    
    // 重新计算分页
    totalPages = Math.ceil(candidates.length / pageSize);
    
    // 重新渲染列表和分页
    renderCandidateList();
    renderPagination();
}

// 处理筛选条件变化
function handleFilterChange() {
    const positionFilter = document.getElementById('positionFilter');
    const sortBy = document.getElementById('sortBy');
    
    currentFilters.position = positionFilter.value;
    currentFilters.sortBy = sortBy.value;
    currentPage = 1; // 重置为第一页
    
    // 重新加载候选人
    loadCandidates();
}

// 显示添加候选人模态框
function showAddCandidateModal() {
    // 重置表单
    const candidateForm = document.getElementById('candidateForm');
    candidateForm.reset();

    // 设置模态框标题
    document.getElementById('modalTitle').textContent = '新增候选人';

    // 清除隐藏ID字段
    document.getElementById('candidateId').value = '';

    // 清空痛点列表
    renderConcernsList([]);

    // 显示模态框
    const modal = document.getElementById('candidateModal');
    modal.style.display = 'block';
}

function processResumeText(resumeText) {
    if (typeof resumeText === 'object' && resumeText !== null) {
      // 如果是对象或数组，将其 JSON 字符串化
      return JSON.stringify(resumeText);
    } else {
      // 否则，直接返回原始值（纯文本、数字、布尔值等）
      return resumeText;
    }
  }
  
  // 示例用法同上

// 打开编辑候选人模态框
async function openEditCandidateModal(candidateId) {
    try {
        // 获取候选人数据
        const candidate = await API.user.getById(candidateId);
        
        if (!candidate) {
            showNotification('获取候选人数据失败', 'error');
            return;
        }
        
        // 设置模态框标题
        document.getElementById('modalTitle').textContent = '编辑候选人';
        
        // 填充表单数据
        document.getElementById('candidateId').value = candidate.userid;
        document.getElementById('positionId').value = candidate.positionId || '';
        document.getElementById('name').value = candidate.name || '';
        document.getElementById('real_name').value = candidate.real_name || '';
        document.getElementById('title').value = candidate.title || '';
        document.getElementById('gender').value = candidate.gender || '';
        document.getElementById('age').value = candidate.age || '';
        document.getElementById('phone').value = candidate.phone || '';
        document.getElementById('wechat').value = candidate.wechat || '';
        document.getElementById('wechat_nickname').value = candidate.wechat_nickname || '';
        document.getElementById('city').value = candidate.city || '';
        document.getElementById('address').value = candidate.address || '';
        document.getElementById('resume').value = candidate.resume || '';
        document.getElementById('resume_text').value = processResumeText(candidate.resume_text) || '';
        document.getElementById('education_experience').value = candidate.education_experience || '';
        document.getElementById('graduate_school').value = candidate.graduate_school || '';
        document.getElementById('source').value = candidate.source || 'web';
        document.getElementById('remarks').value = candidate.remarks || '';

        // 处理痛点数据
        let concerns = [];
        if (candidate.concerns) {
            try {
                if (typeof candidate.concerns === 'string') {
                    concerns = JSON.parse(candidate.concerns);
                } else if (Array.isArray(candidate.concerns)) {
                    concerns = candidate.concerns;
                }
            } catch (e) {
                console.warn('解析痛点数据失败:', e);
                concerns = [];
            }
        }
        renderConcernsList(concerns);

        // 显示模态框
        const modal = document.getElementById('candidateModal');
        modal.style.display = 'block';
    } catch (error) {
        console.error('获取候选人数据失败:', error);
        showNotification('获取候选人数据失败', 'error');
    }
}

// 保存候选人
async function saveCandidate() {
    // 获取表单数据
    const candidateForm = document.getElementById('candidateForm');
    
    // 表单验证
    if (!candidateForm.checkValidity()) {
        // 触发浏览器的默认表单验证
        candidateForm.reportValidity();
        return;
    }
    
    // 收集表单数据
    const candidateId = document.getElementById('candidateId').value;
    const candidateData = {
        userid: candidateId || generateUniqueId(),
        positionId: document.getElementById('positionId').value,
        name: document.getElementById('name').value || '',
        real_name: document.getElementById('real_name').value || '',
        title: document.getElementById('title').value || '',
        gender: document.getElementById('gender').value || '',
        age: document.getElementById('age').value ? parseInt(document.getElementById('age').value) : null,
        phone: document.getElementById('phone').value || '',
        resume: document.getElementById('resume').value || '',
        resume_text: document.getElementById('resume_text').value || '',
        wechat: document.getElementById('wechat').value || '',
        wechat_nickname: document.getElementById('wechat_nickname').value || '',
        city: document.getElementById('city').value || '',
        address: document.getElementById('address').value || '',
        education_experience: document.getElementById('education_experience').value || '',
        graduate_school: document.getElementById('graduate_school').value || '',
        source: document.getElementById('source').value || 'web',
        remarks: document.getElementById('remarks').value || '',
        concerns: JSON.stringify(getCurrentConcerns())
    };
    
    // 显示加载状态
    const saveBtn = document.getElementById('saveCandidateBtn');
    const originalText = saveBtn.textContent;
    saveBtn.textContent = '保存中...';
    saveBtn.disabled = true;
    
    try {
        // 是新增还是编辑
        let result;
        if (candidateId) {
            // 编辑候选人
            result = await API.user.update(candidateId, candidateData);
            showNotification('候选人更新成功', 'success');
        } else {
            // 新增候选人
            result = await API.user.add(candidateData);
            showNotification('候选人添加成功', 'success');
        }
        
        // 关闭模态框
        closeAllModals();
        
        // 重新加载候选人数据
        await loadCandidates();
    } catch (error) {
        console.error('保存候选人失败:', error);
        
        // 显示详细错误信息
        let errorMessage = '保存候选人失败';
        
        // 获取更详细的错误信息
        if (error.message) {
            errorMessage = error.message;
        }
        
        // 特殊处理场景未设置的错误
        if (errorMessage.includes('请先设置场景')) {
            errorMessage = '该职位分类下未设置场景，请先在"面试场景"中为此职位分类添加场景。';
        }
        
        // 显示错误通知
        showNotification(errorMessage, 'error');
    } finally {
        // 恢复按钮状态
        saveBtn.textContent = originalText;
        saveBtn.disabled = false;
    }
}

// 确认删除候选人
function confirmDeleteCandidate(candidateId) {
    confirmAction('确定要删除此候选人吗？此操作不可恢复。', async () => {
        try {
            await API.user.delete(candidateId);
            showNotification('候选人删除成功', 'success');
            
            // 重新加载候选人数据
            await loadCandidates();
        } catch (error) {
            console.error('删除候选人失败:', error);
            showNotification(error.message || '删除候选人失败', 'error');
        }
    });
}

// 显示候选人详情
async function showCandidateDetail(candidateId) {
    try {
        // 获取候选人数据
        const candidate = await API.user.getById(candidateId);
        
        if (!candidate) {
            showNotification('获取候选人详情失败', 'error');
            return;
        }
        
        // 获取职位名称
        let positionName = '未知职位';
        if (candidate.positionId) {
            const position = positions.find(p => p.dataId === candidate.positionId);
            if (position) {
                positionName = position.positionName || '未知职位';
            }
        }
        
        // 获取场景名称
        let sceneName = '未知场景';
        if (candidate.sceneId && candidate.positionId) {
            try {
                // 获取该职位的所有场景
                const scenes = await API.scene.getByPosition(candidate.positionId);
                if (scenes && Array.isArray(scenes)) {
                    // 查找当前场景
                    const currentScene = scenes.find(s => s.sceneId === candidate.sceneId);
                    if (currentScene) {
                        sceneName = currentScene.sceneName || candidate.sceneId;
                    }
                }
            } catch (error) {
                console.error('获取场景名称失败:', error);
                sceneName = candidate.sceneId;
            }
        } else if (candidate.sceneId) {
            sceneName = candidate.sceneId;
        } else {
            sceneName = '<span class="empty">未设置</span>';
        }
        
        // 设置模态框标题
        document.getElementById('detailModalTitle').textContent = (candidate.name || '未知姓名') + ' 的详细信息';
        
        // 获取首字母作为头像占位符
        const initial = (candidate.name && candidate.name.charAt(0)) || '?';
        
        // 构建详情HTML
        const detailsHTML = `
            <div class="candidate-profile" data-id="${candidate.userid}">
                <div class="candidate-profile-avatar">${initial}</div>
                <div class="candidate-profile-info">
                    <div class="candidate-profile-name">
                        ${candidate.name || '未知姓名'}
                        ${candidate.title ? `<span class="candidate-profile-title-tag">${candidate.title}</span>` : ''}
                    </div>
                    <div class="candidate-profile-subtitle">${candidate.real_name ? '真实姓名: ' + candidate.real_name : ''}</div>
                    <div class="candidate-profile-title">${positionName}</div>
                    <div class="candidate-profile-details">
                        <div class="candidate-profile-detail">
                            <i class="fas fa-phone"></i>
                            <span>${candidate.phone || '未填写'}</span>
                        </div>
                        <div class="candidate-profile-detail">
                            <i class="fas fa-envelope"></i>
                            <span>${candidate.email || '未填写'}</span>
                        </div>
                        <div class="candidate-profile-detail">
                            <i class="fas fa-user"></i>
                            <span>${candidate.age ? candidate.age + '岁' : '未填写'}</span>
                        </div>
                        <div class="candidate-profile-detail">
                            <i class="fas fa-venus-mars"></i>
                            <span>${candidate.gender || '未填写'}</span>
                        </div>
                        <div class="candidate-profile-detail">
                            <i class="fas fa-comment"></i>
                            <span>${candidate.wechat_nickname || candidate.wechat || '未填写'}</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="detail-section">
                <h4>基本信息</h4>
                <div class="detail-grid">
                    <div class="detail-column">
                        <div class="detail-item">
                            <div class="detail-label">应聘职位</div>
                            <div class="detail-value">${positionName}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">姓名</div>
                            <div class="detail-value">${candidate.name || '<span class="empty">未填写</span>'}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">真实姓名</div>
                            <div class="detail-value">${candidate.real_name || '<span class="empty">未填写</span>'}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">称呼</div>
                            <div class="detail-value">${candidate.title || '<span class="empty">未填写</span>'}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">手机号码</div>
                            <div class="detail-value">${candidate.phone || '<span class="empty">未填写</span>'}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">电子邮箱</div>
                            <div class="detail-value">${candidate.email || '<span class="empty">未填写</span>'}</div>
                        </div>
                    </div>
                    <div class="detail-column">
                        <div class="detail-item">
                            <div class="detail-label">年龄</div>
                            <div class="detail-value">${candidate.age ? candidate.age + '岁' : '<span class="empty">未填写</span>'}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">性别</div>
                            <div class="detail-value">${candidate.gender || '<span class="empty">未填写</span>'}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">微信</div>
                            <div class="detail-value">${candidate.wechat || '<span class="empty">未填写</span>'}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">微信昵称</div>
                            <div class="detail-value">${candidate.wechat_nickname || '<span class="empty">未填写</span>'}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">城市</div>
                            <div class="detail-value">${candidate.city || '<span class="empty">未填写</span>'}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">地址</div>
                            <div class="detail-value">${candidate.address || '<span class="empty">未填写</span>'}</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="detail-section">
                <h4>教育信息</h4>
                <div class="detail-grid">
                    <div class="detail-column">
                        <div class="detail-item">
                            <div class="detail-label">最高学历</div>
                            <div class="detail-value">${candidate.education_experience || '<span class="empty">未填写</span>'}</div>
                        </div>
                    </div>
                    <div class="detail-column">
                        <div class="detail-item">
                            <div class="detail-label">毕业院校</div>
                            <div class="detail-value">${candidate.graduate_school || '<span class="empty">未填写</span>'}</div>
                        </div>
                    </div>
                </div>
            </div>
            
            ${candidate.resume || candidate.resume_text ? `
            <div class="detail-section">
                <h4>简历信息</h4>
                ${candidate.resume ? `
                <div class="detail-item">
                    <div class="detail-label">简历链接</div>
                    <div class="detail-value resume-link">
                        <a href="${candidate.resume}" target="_blank">
                            <i class="fas fa-external-link-alt"></i> 查看简历
                        </a>
                    </div>
                </div>
                ` : ''}
                
                ${candidate.resume_text ? `
                <div class="detail-item">
                    <div class="detail-label">文本简历</div>
                    <div class="detail-value resume-text-preview">
                        <pre>${processResumeText(candidate.resume_text)}</pre>
                    </div>
                </div>
                ` : ''}
            </div>
            ` : ''}
            
            ${candidate.remarks ? `
            <div class="detail-section">
                <h4>备注</h4>
                <div class="detail-item">
                    <div class="detail-value">${candidate.remarks.replace(/\n/g, '<br>')}</div>
                </div>
            </div>
            ` : ''}

            ${(() => {
                let concerns = [];
                if (candidate.concerns) {
                    try {
                        if (typeof candidate.concerns === 'string') {
                            concerns = JSON.parse(candidate.concerns);
                        } else if (Array.isArray(candidate.concerns)) {
                            concerns = candidate.concerns;
                        }
                    } catch (e) {
                        concerns = [];
                    }
                }

                if (concerns && concerns.length > 0) {
                    return `
                    <div class="detail-section">
                        <h4>用户痛点</h4>
                        <div class="detail-item">
                            <div class="detail-value">
                                <div class="concerns-display">
                                    ${concerns.map(concern => `
                                        <span class="concern-tag">${escapeHtml(concern)}</span>
                                    `).join('')}
                                </div>
                            </div>
                        </div>
                    </div>
                    `;
                } else {
                    return '';
                }
            })()}
            
            <div class="detail-section">
                <h4>其他信息</h4>
                <div class="detail-grid">
                    <div class="detail-column">
                        <div class="detail-item">
                            <div class="detail-label">来源</div>
                            <div class="detail-value">
                                <span class="source-tag" data-source="${candidate.source || 'other'}">${candidate.source || '其他'}</span>
                            </div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">创建时间</div>
                            <div class="detail-value">${formatDate(candidate.createdAt)}</div>
                        </div>
                    </div>
                    <div class="detail-column">
                        <div class="detail-item">
                            <div class="detail-label">评分</div>
                            <div class="detail-value">${candidate.score || '<span class="empty">未评分</span>'}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">更新时间</div>
                            <div class="detail-value">${formatDate(candidate.updatedAt)}</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="detail-section">
                <h4>面试场景</h4>
                <div class="scene-display-container07081" style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 15px;">
                    <div class="detail-item" style="margin: 0; flex-grow: 1;">
                        <div class="detail-label">当前场景</div>
                        <div class="detail-value" id="currentSceneValue07081" style="font-weight: 500;">${sceneName}</div>
                    </div>
                    <button class="btn btn-sm btn-outline-primary" id="editSceneBtn07081" style="margin-left: 15px;">
                        <i class="fas fa-edit"></i> 修改
                    </button>
                </div>
                <div class="scene-edit-container07081" style="display:none; background-color: #f8f9fa; border-radius: 5px; padding: 15px; margin-top: 10px;">
                    <div class="detail-item" style="margin-bottom: 15px;">
                        <div class="detail-label" style="margin-bottom: 8px; font-weight: 500;">选择场景</div>
                        <div class="detail-value">
                            <select id="sceneSelect07081" class="form-control" style="width: 100%; border-radius: 4px; border: 1px solid #ced4da; padding: 6px 12px;"></select>
                        </div>
                    </div>
                    <div class="button-group" style="display: flex; justify-content: flex-end; gap: 10px;">
                        <button class="btn btn-sm btn-light" id="cancelSceneBtn07081">取消</button>
                        <button class="btn btn-sm btn-primary" id="saveSceneBtn07081">保存</button>
                    </div>
                </div>
            </div>

            <div class="detail-section">
                <h4>候选人评分</h4>
                <div class="score-display-container07081" style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 15px;">
                    <div class="detail-item" style="margin: 0; flex-grow: 1;">
                        <div class="detail-label">当前分数</div>
                        <div class="detail-value" id="currentScoreValue07081" style="font-weight: 500; font-size: 1.1em;">${candidate.score || '<span class="empty">未评分</span>'}</div>
                    </div>
                    <button class="btn btn-sm btn-outline-primary" id="editScoreBtn07081" style="margin-left: 15px;">
                        <i class="fas fa-edit"></i> 修改
                    </button>
                </div>
                <div class="score-edit-container07081" style="display:none; background-color: #f8f9fa; border-radius: 5px; padding: 15px; margin-top: 10px;">
                    <div class="detail-item" style="margin-bottom: 15px;">
                        <div class="detail-label" style="margin-bottom: 8px; font-weight: 500;">分数 (0-100)</div>
                        <div class="detail-value">
                            <input type="number" id="scoreInput07081" class="form-control" min="0" max="100" value="${candidate.score || 0}" style="width: 100%; border-radius: 4px; border: 1px solid #ced4da; padding: 6px 12px;">
                        </div>
                    </div>
                    <div class="button-group" style="display: flex; justify-content: flex-end; gap: 10px;">
                        <button class="btn btn-sm btn-light" id="cancelScoreBtn07081">取消</button>
                        <button class="btn btn-sm btn-primary" id="saveScoreBtn07081">保存</button>
                    </div>
                </div>
            </div>
        `;
        
        // 设置详情内容
        const candidateDetails = document.getElementById('candidateDetails');
        candidateDetails.innerHTML = detailsHTML;
        candidateDetails.setAttribute('data-id', candidate.userid);
        
        // 显示模态框
        const modal = document.getElementById('candidateDetailModal');
        modal.style.display = 'block';
        
        // 加载该职位下的所有场景
        if (candidate.positionId) {
            try {
                const scenes = await API.scene.getByPosition(candidate.positionId);
                if (scenes && Array.isArray(scenes)) {
                    const sceneSelect = document.getElementById('sceneSelect07081');
                    if (sceneSelect) { // Add null check
                        sceneSelect.innerHTML = '';
                        
                        scenes.forEach(scene => {
                            const option = document.createElement('option');
                            option.value = scene.sceneId;
                            option.textContent = scene.sceneName || scene.sceneId;
                            if (scene.sceneId === candidate.sceneId) {
                                option.selected = true;
                            }
                            sceneSelect.appendChild(option);
                        });
                    } else {
                        console.error('场景选择元素未找到');
                    }
                }
            } catch (error) {
                console.error('加载场景数据失败:', error);
            }
        }
        
        // 绑定修改分数按钮事件
        const editScoreBtn = document.getElementById('editScoreBtn07081');
        if (editScoreBtn) {
            editScoreBtn.addEventListener('click', function() {
                document.querySelector('.score-display-container07081').style.display = 'none';
                document.querySelector('.score-edit-container07081').style.display = 'block';
            });
        }
        
        // 绑定保存分数按钮事件
        const saveScoreBtn = document.getElementById('saveScoreBtn07081');
        if (saveScoreBtn) {
            saveScoreBtn.addEventListener('click', async function() {
                const score = parseInt(document.getElementById('scoreInput07081').value);
                if (isNaN(score) || score < 0 || score > 100) {
                    alert('请输入0-100之间的有效分数');
                    return;
                }
                
                try {
                    const result = await API.user.updateScore07081(candidate.userid, score);
                    if (result && result.code === 200) {
                        document.getElementById('currentScoreValue07081').textContent = score;
                        document.querySelector('.score-display-container07081').style.display = 'flex';
                        document.querySelector('.score-edit-container07081').style.display = 'none';
                        showNotification('分数更新成功', 'success');
                        
                        // 更新列表中的分数显示
                        const scoreDisplay = document.querySelector(`.candidate-card[data-id="${candidate.userid}"] .candidate-score`);
                        if (scoreDisplay) {
                            scoreDisplay.textContent = score;
                        }
                    } else {
                        showNotification('分数更新失败', 'error');
                    }
                } catch (error) {
                    console.error('更新分数失败:', error);
                    showNotification('分数更新失败', 'error');
                }
            });
        }
        
        // 绑定取消修改分数按钮事件
        const cancelScoreBtn = document.getElementById('cancelScoreBtn07081');
        if (cancelScoreBtn) {
            cancelScoreBtn.addEventListener('click', function() {
                document.querySelector('.score-display-container07081').style.display = 'flex';
                document.querySelector('.score-edit-container07081').style.display = 'none';
            });
        }
        
        // 绑定修改场景按钮事件
        const editSceneBtn = document.getElementById('editSceneBtn07081');
        if (editSceneBtn) {
            editSceneBtn.addEventListener('click', function() {
                document.querySelector('.scene-display-container07081').style.display = 'none';
                document.querySelector('.scene-edit-container07081').style.display = 'block';
            });
        }
        
        // 绑定保存场景按钮事件
        const saveSceneBtn = document.getElementById('saveSceneBtn07081');
        if (saveSceneBtn) {
            saveSceneBtn.addEventListener('click', async function() {
                const sceneSelect = document.getElementById('sceneSelect07081');
                const sceneId = sceneSelect.value;
                const sceneName = sceneSelect.options[sceneSelect.selectedIndex].text;
                
                if (!sceneId) {
                    alert('请选择一个场景');
                    return;
                }
                
                try {
                    const result = await API.user.updateScene07081(candidate.userid, sceneId);
                    if (result && result.code === 200) {
                        document.getElementById('currentSceneValue07081').textContent = sceneName;
                        document.querySelector('.scene-display-container07081').style.display = 'flex';
                        document.querySelector('.scene-edit-container07081').style.display = 'none';
                        showNotification('场景更新成功', 'success');
                    } else {
                        showNotification('场景更新失败', 'error');
                    }
                } catch (error) {
                    console.error('更新场景失败:', error);
                    showNotification('场景更新失败', 'error');
                }
            });
        }
        
        // 绑定取消修改场景按钮事件
        const cancelSceneBtn = document.getElementById('cancelSceneBtn07081');
        if (cancelSceneBtn) {
            cancelSceneBtn.addEventListener('click', function() {
                document.querySelector('.scene-display-container07081').style.display = 'flex';
                document.querySelector('.scene-edit-container07081').style.display = 'none';
            });
        }
    } catch (error) {
        console.error('获取候选人详情失败:', error);
        showNotification('获取候选人详情失败', 'error');
    }
}

// 开始面试
function startInterview(candidateId) {
    // 跳转到面试页面
    window.location.href = `/interview/start?candidateId=${candidateId}`;
}

// 关闭所有模态框
function closeAllModals() {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.style.display = 'none';
    });
}

// 生成唯一ID
function generateUniqueId() {
    return 'user_' + Date.now() + '_' + Math.floor(Math.random() * 1000000).toString();
} 

// Helper function to get a readable label for the source
function getSourceLabel(source) {
    switch(source) {
        case 'boss':
            return 'Boss直聘';
        case 'wechat':
            return '微信';
        default:
            return '其他';
    }
}

// 渲染痛点列表
function renderConcernsList(concerns) {
    const concernsList = document.getElementById('concernsList');
    if (!concernsList) return;

    if (!concerns || concerns.length === 0) {
        concernsList.innerHTML = '<div class="concerns-empty">暂无用户痛点</div>';
        return;
    }

    concernsList.innerHTML = concerns.map((concern, index) => `
        <div class="concern-item" data-index="${index}">
            <span class="concern-text">${escapeHtml(concern)}</span>
            <button type="button" class="remove-concern" onclick="removeConcern(${index})">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `).join('');
}

// 添加痛点
function addConcern() {
    const concernsInput = document.getElementById('concernsInput');
    if (!concernsInput) return;

    const concernText = concernsInput.value.trim();
    if (!concernText) {
        showNotification('请输入痛点内容', 'warning');
        return;
    }

    const currentConcerns = getCurrentConcerns();

    // 检查是否已存在相同痛点
    if (currentConcerns.includes(concernText)) {
        showNotification('该痛点已存在', 'warning');
        return;
    }

    currentConcerns.push(concernText);
    renderConcernsList(currentConcerns);
    concernsInput.value = '';
}

// 移除痛点
function removeConcern(index) {
    const currentConcerns = getCurrentConcerns();
    currentConcerns.splice(index, 1);
    renderConcernsList(currentConcerns);
}

// 获取当前痛点列表
function getCurrentConcerns() {
    const concernsList = document.getElementById('concernsList');
    if (!concernsList) return [];

    const concernItems = concernsList.querySelectorAll('.concern-item');
    return Array.from(concernItems).map(item => {
        return item.querySelector('.concern-text').textContent;
    });
}

// HTML转义函数
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}