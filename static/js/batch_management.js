// 批量管理页面JavaScript

// 全局变量
let currentPage = 1;
let pageSize = 10;
let totalRecords = 0;
let allRecords = [];
let filteredRecords = [];
let currentFilters = {
    search: ''
};

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
});

// 初始化页面
function initializePage() {
    bindEvents();
    loadRecords();
}

// 绑定事件监听
function bindEvents() {
    // 新增记录按钮
    const addRecordBtn = document.getElementById('addRecordBtn');
    if (addRecordBtn) {
        addRecordBtn.addEventListener('click', showAddRecordModal);
        console.log('新增记录按钮事件已绑定');
    } else {
        console.error('找不到新增记录按钮');
    }
    
    // 导入按钮
    const importBtn = document.getElementById('importBtn');
    importBtn.addEventListener('click', showImportModal);
    
    // 导出按钮
    const exportBtn = document.getElementById('exportBtn');
    exportBtn.addEventListener('click', exportData);
    
    // 搜索按钮
    const searchBtn = document.getElementById('searchBtn');
    searchBtn.addEventListener('click', handleSearch);
    
    // 搜索输入框回车事件
    const searchInput = document.getElementById('searchInput');
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            handleSearch();
        }
    });
    
    // 全选复选框
    const selectAll = document.getElementById('selectAll');
    selectAll.addEventListener('change', handleSelectAll);
    
    // 模态框关闭事件
    bindModalEvents();
    
    // 文件上传事件
    bindFileUploadEvents();
}

// 绑定模态框事件
function bindModalEvents() {
    // 记录模态框事件
    const recordModal = document.getElementById('recordModal');
    const cancelBtn = document.getElementById('cancelBtn');
    const saveRecordBtn = document.getElementById('saveRecordBtn');
    
    // 关闭按钮
    const closeButtons = document.querySelectorAll('.modal-close');
    closeButtons.forEach(btn => {
        btn.addEventListener('click', closeAllModals);
    });
    
    // 取消按钮
    cancelBtn.addEventListener('click', closeAllModals);
    
    // 保存按钮
    saveRecordBtn.addEventListener('click', saveRecord);
    
    // 点击模态框外部关闭
    window.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal')) {
            closeAllModals();
        }
    });
    
    // 导入模态框事件
    const cancelImportBtn = document.getElementById('cancelImportBtn');
    const startImportBtn = document.getElementById('startImportBtn');
    
    cancelImportBtn.addEventListener('click', closeAllModals);
    startImportBtn.addEventListener('click', startImport);
}

// 绑定文件上传事件
function bindFileUploadEvents() {
    const fileUploadArea = document.getElementById('fileUploadArea');
    const fileInput = document.getElementById('fileInput');
    const removeFileBtn = document.getElementById('removeFileBtn');
    
    // 点击上传区域
    fileUploadArea.addEventListener('click', () => {
        fileInput.click();
    });
    
    // 文件选择
    fileInput.addEventListener('change', handleFileSelect);
    
    // 拖拽事件
    fileUploadArea.addEventListener('dragover', handleDragOver);
    fileUploadArea.addEventListener('dragleave', handleDragLeave);
    fileUploadArea.addEventListener('drop', handleFileDrop);
    
    // 移除文件
    removeFileBtn.addEventListener('click', removeSelectedFile);
}

// 加载记录数据
async function loadRecords() {
    try {
        showLoadingState();

        // 从API获取数据
        const response = await API.batchManagement.getAll();
        console.log('API响应:', response); // 调试信息

        // 处理API响应格式
        if (response && response.data) {
            allRecords = response.data || [];
        } else if (Array.isArray(response)) {
            allRecords = response;
        } else {
            allRecords = [];
        }

        console.log('处理后的记录:', allRecords); // 调试信息

        applyFilters();
        renderTable();
        renderPagination();

    } catch (error) {
        console.error('加载数据失败:', error);
        // 如果是网络错误或API错误，显示友好的错误信息
        if (error.message && error.message.includes('Failed to fetch')) {
            showNotification('网络连接失败，请检查网络连接', 'error');
        } else {
            showNotification('加载数据失败: ' + (error.message || '未知错误'), 'error');
        }
        showEmptyState();
    }
}

// 应用筛选条件
function applyFilters() {
    filteredRecords = allRecords.filter(record => {
        // 搜索筛选
        if (currentFilters.search) {
            const searchTerm = currentFilters.search.toLowerCase();
            return (
                (record.city && record.city.toLowerCase().includes(searchTerm)) ||
                (record.job_category && record.job_category.toLowerCase().includes(searchTerm)) ||
                (record.work_content && record.work_content.toLowerCase().includes(searchTerm)) ||
                (record.recruitment_area && record.recruitment_area.toLowerCase().includes(searchTerm))
            );
        }
        return true;
    });
    
    totalRecords = filteredRecords.length;
    currentPage = 1; // 重置到第一页
}

// 渲染表格
function renderTable() {
    const tableBody = document.getElementById('batchTableBody');

    if (!tableBody) {
        console.error('找不到表格body元素');
        return;
    }

    if (filteredRecords.length === 0) {
        showEmptyState();
        // 清空分页
        const paginationContainer = document.getElementById('pagination');
        if (paginationContainer) {
            paginationContainer.innerHTML = '';
        }
        return;
    }
    
    // 计算当前页数据
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const currentPageRecords = filteredRecords.slice(startIndex, endIndex);
    
    // 生成表格行
    const rows = currentPageRecords.map(record => {
        return `
            <tr data-id="${record.id}">
                <td>
                    <input type="checkbox" class="record-checkbox" value="${record.id}">
                </td>
                <td title="${record.city || ''}">${record.city || '-'}</td>
                <td title="${record.job_category || ''}">${record.job_category || '-'}</td>
                <td title="${record.work_content || ''}" class="expandable">${truncateText(record.work_content, 20)}</td>
                <td title="${record.recruitment_requirements || ''}" class="expandable">${truncateText(record.recruitment_requirements, 20)}</td>
                <td title="${record.work_time || ''}">${record.work_time || '-'}</td>
                <td title="${record.salary_benefits || ''}" class="expandable">${truncateText(record.salary_benefits, 15)}</td>
                <td title="${record.interview_time || ''}">${record.interview_time || '-'}</td>
                <td title="${record.training_time || ''}">${record.training_time || '-'}</td>
                <td title="${record.recruitment_area || ''}">${record.recruitment_area || '-'}</td>
                <td title="${record.interview_address || ''}" class="expandable">${truncateText(record.interview_address, 15)}</td>
                <td title="${record.remarks || ''}" class="expandable">${truncateText(record.remarks, 15)}</td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-edit" onclick="editRecord('${record.id}')">
                            <i class="fas fa-edit"></i> 编辑
                        </button>
                        <button class="btn btn-sm btn-delete" onclick="confirmDeleteRecord('${record.id}')">
                            <i class="fas fa-trash"></i> 删除
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
    
    tableBody.innerHTML = rows;
    
    // 绑定复选框事件
    const checkboxes = tableBody.querySelectorAll('.record-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectAllState);
    });
}

// 渲染分页
function renderPagination() {
    const paginationContainer = document.getElementById('pagination');
    
    if (totalRecords === 0) {
        paginationContainer.innerHTML = '';
        return;
    }
    
    const totalPages = Math.ceil(totalRecords / pageSize);
    const startRecord = (currentPage - 1) * pageSize + 1;
    const endRecord = Math.min(currentPage * pageSize, totalRecords);
    
    let paginationHTML = `
        <div class="pagination-info">
            显示 ${startRecord}-${endRecord} 条，共 ${totalRecords} 条记录
        </div>
        <ul class="pagination">
    `;
    
    // 上一页
    paginationHTML += `
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">上一页</a>
        </li>
    `;
    
    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `
            <li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
            </li>
        `;
    }
    
    // 下一页
    paginationHTML += `
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">下一页</a>
        </li>
    `;
    
    paginationHTML += '</ul>';
    paginationContainer.innerHTML = paginationHTML;
}

// 显示加载状态
function showLoadingState() {
    const tableBody = document.getElementById('batchTableBody');
    tableBody.innerHTML = `
        <tr>
            <td colspan="13" class="loading-state">
                <div class="loading-spinner"></div>
                <p>加载中...</p>
            </td>
        </tr>
    `;
}

// 显示空状态
function showEmptyState() {
    const tableBody = document.getElementById('batchTableBody');
    if (!tableBody) return;

    tableBody.innerHTML = `
        <tr>
            <td colspan="13" class="empty-state">
                <div class="empty-content">
                    <i class="fas fa-folder-open"></i>
                    <h3>暂无数据</h3>
                    <p>还没有任何记录，点击下方按钮开始添加数据</p>
                </div>
            </td>
        </tr>
    `;
}

// 截断文本
function truncateText(text, maxLength) {
    if (!text) return '-';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
}

// 处理搜索
function handleSearch() {
    const searchInput = document.getElementById('searchInput');
    currentFilters.search = searchInput.value.trim();
    applyFilters();
    renderTable();
    renderPagination();
}

// 处理全选
function handleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.record-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

// 更新全选状态
function updateSelectAllState() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.record-checkbox');
    const checkedBoxes = document.querySelectorAll('.record-checkbox:checked');
    
    if (checkedBoxes.length === 0) {
        selectAll.indeterminate = false;
        selectAll.checked = false;
    } else if (checkedBoxes.length === checkboxes.length) {
        selectAll.indeterminate = false;
        selectAll.checked = true;
    } else {
        selectAll.indeterminate = true;
        selectAll.checked = false;
    }
}

// 切换页面
function changePage(page) {
    const totalPages = Math.ceil(totalRecords / pageSize);
    if (page < 1 || page > totalPages) return;
    
    currentPage = page;
    renderTable();
    renderPagination();
}

// 显示新增记录模态框
function showAddRecordModal() {
    console.log('显示新增记录模态框'); // 调试信息

    // 重置表单
    const recordForm = document.getElementById('recordForm');
    if (recordForm) {
        recordForm.reset();
    } else {
        console.error('找不到表单元素');
        return;
    }

    // 设置模态框标题
    const modalTitle = document.getElementById('modalTitle');
    if (modalTitle) {
        modalTitle.textContent = '新增记录';
    }

    // 清除隐藏ID字段
    const recordId = document.getElementById('recordId');
    if (recordId) {
        recordId.value = '';
    }

    // 显示模态框
    const modal = document.getElementById('recordModal');
    if (modal) {
        modal.style.display = 'block';
        console.log('模态框已显示');
    } else {
        console.error('找不到模态框元素');
    }
}

// 编辑记录
async function editRecord(recordId) {
    try {
        // 获取记录数据
        const response = await API.batchManagement.getById(recordId);

        // 处理API响应格式
        let record;
        if (response && response.data) {
            record = response.data;
        } else if (response && response.id) {
            record = response;
        } else {
            record = null;
        }

        if (!record) {
            showNotification('获取记录数据失败', 'error');
            return;
        }
        
        // 设置模态框标题
        document.getElementById('modalTitle').textContent = '编辑记录';
        
        // 填充表单数据
        document.getElementById('recordId').value = record.id;
        document.getElementById('city').value = record.city || '';
        document.getElementById('job_category').value = record.job_category || '';
        document.getElementById('work_content').value = record.work_content || '';
        document.getElementById('recruitment_requirements').value = record.recruitment_requirements || '';
        document.getElementById('work_time').value = record.work_time || '';
        document.getElementById('salary_benefits').value = record.salary_benefits || '';
        document.getElementById('interview_time').value = record.interview_time || '';
        document.getElementById('training_time').value = record.training_time || '';
        document.getElementById('recruitment_area').value = record.recruitment_area || '';
        document.getElementById('interview_address').value = record.interview_address || '';
        document.getElementById('remarks').value = record.remarks || '';
        
        // 显示模态框
        const modal = document.getElementById('recordModal');
        modal.style.display = 'block';
        
    } catch (error) {
        console.error('获取记录数据失败:', error);
        showNotification('获取记录数据失败', 'error');
    }
}

// 保存记录
async function saveRecord() {
    // 获取表单数据
    const recordForm = document.getElementById('recordForm');
    
    // 表单验证
    if (!recordForm.checkValidity()) {
        recordForm.reportValidity();
        return;
    }
    
    // 收集表单数据
    const recordId = document.getElementById('recordId').value;
    const recordData = {
        city: document.getElementById('city').value || '',
        job_category: document.getElementById('job_category').value || '',
        work_content: document.getElementById('work_content').value || '',
        recruitment_requirements: document.getElementById('recruitment_requirements').value || '',
        work_time: document.getElementById('work_time').value || '',
        salary_benefits: document.getElementById('salary_benefits').value || '',
        interview_time: document.getElementById('interview_time').value || '',
        training_time: document.getElementById('training_time').value || '',
        recruitment_area: document.getElementById('recruitment_area').value || '',
        interview_address: document.getElementById('interview_address').value || '',
        remarks: document.getElementById('remarks').value || ''
    };
    
    // 显示加载状态
    const saveBtn = document.getElementById('saveRecordBtn');
    const originalText = saveBtn.textContent;
    saveBtn.textContent = '保存中...';
    saveBtn.disabled = true;
    
    try {
        // 是新增还是编辑
        if (recordId) {
            // 编辑记录
            await API.batchManagement.update(recordId, recordData);
            showNotification('记录更新成功', 'success');
        } else {
            // 新增记录
            await API.batchManagement.add(recordData);
            showNotification('记录添加成功', 'success');
        }
        
        // 关闭模态框
        closeAllModals();
        
        // 重新加载数据
        await loadRecords();
        
    } catch (error) {
        console.error('保存记录失败:', error);
        showNotification(error.message || '保存记录失败', 'error');
    } finally {
        // 恢复按钮状态
        saveBtn.textContent = originalText;
        saveBtn.disabled = false;
    }
}

// 确认删除记录
function confirmDeleteRecord(recordId) {
    confirmAction('确定要删除此记录吗？此操作不可恢复。', async () => {
        try {
            await API.batchManagement.delete(recordId);
            showNotification('记录删除成功', 'success');
            
            // 重新加载数据
            await loadRecords();
        } catch (error) {
            console.error('删除记录失败:', error);
            showNotification(error.message || '删除记录失败', 'error');
        }
    });
}

// 关闭所有模态框
function closeAllModals() {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.style.display = 'none';
    });
}

// 显示导入模态框
function showImportModal() {
    // 重置导入表单
    resetImportForm();

    // 显示模态框
    const modal = document.getElementById('importModal');
    modal.style.display = 'block';
}

// 重置导入表单
function resetImportForm() {
    const fileInput = document.getElementById('fileInput');
    const selectedFile = document.getElementById('selectedFile');
    const fileUploadArea = document.getElementById('fileUploadArea');
    const startImportBtn = document.getElementById('startImportBtn');

    fileInput.value = '';
    selectedFile.style.display = 'none';
    fileUploadArea.style.display = 'block';
    startImportBtn.disabled = true;

    // 重置复选框
    document.getElementById('skipFirstRow').checked = true;
    document.getElementById('updateExisting').checked = false;
}

// 处理文件选择
function handleFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
        displaySelectedFile(file);
    }
}

// 处理拖拽悬停
function handleDragOver(event) {
    event.preventDefault();
    event.currentTarget.classList.add('dragover');
}

// 处理拖拽离开
function handleDragLeave(event) {
    event.currentTarget.classList.remove('dragover');
}

// 处理文件拖拽
function handleFileDrop(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('dragover');

    const files = event.dataTransfer.files;
    if (files.length > 0) {
        const file = files[0];
        displaySelectedFile(file);
    }
}

// 显示选中的文件
function displaySelectedFile(file) {
    // 验证文件类型
    const allowedTypes = ['.xlsx', '.xls', '.csv'];
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase();

    if (!allowedTypes.includes(fileExtension)) {
        showNotification('请选择 Excel (.xlsx, .xls) 或 CSV 文件', 'error');
        return;
    }

    // 验证文件大小 (最大10MB)
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
        showNotification('文件大小不能超过 10MB', 'error');
        return;
    }

    // 显示选中的文件
    const fileUploadArea = document.getElementById('fileUploadArea');
    const selectedFile = document.getElementById('selectedFile');
    const fileName = selectedFile.querySelector('.file-name');
    const startImportBtn = document.getElementById('startImportBtn');

    fileUploadArea.style.display = 'none';
    selectedFile.style.display = 'flex';
    fileName.textContent = file.name;
    startImportBtn.disabled = false;

    // 存储文件引用
    selectedFile.fileData = file;
}

// 移除选中的文件
function removeSelectedFile() {
    const fileInput = document.getElementById('fileInput');
    const selectedFile = document.getElementById('selectedFile');
    const fileUploadArea = document.getElementById('fileUploadArea');
    const startImportBtn = document.getElementById('startImportBtn');

    fileInput.value = '';
    selectedFile.style.display = 'none';
    fileUploadArea.style.display = 'block';
    startImportBtn.disabled = true;

    delete selectedFile.fileData;
}

// 开始导入
async function startImport() {
    const selectedFile = document.getElementById('selectedFile');
    const file = selectedFile.fileData;

    if (!file) {
        showNotification('请先选择文件', 'error');
        return;
    }

    const skipFirstRow = document.getElementById('skipFirstRow').checked;
    const updateExisting = document.getElementById('updateExisting').checked;

    // 显示加载状态
    const startImportBtn = document.getElementById('startImportBtn');
    const originalText = startImportBtn.textContent;
    startImportBtn.textContent = '导入中...';
    startImportBtn.disabled = true;

    try {
        // 创建FormData
        const formData = new FormData();
        formData.append('file', file);
        formData.append('skipFirstRow', skipFirstRow);
        formData.append('updateExisting', updateExisting);

        // 调用导入API
        const result = await API.batchManagement.import(formData);

        // 处理导入结果
        if (result.code === 200) {
            let message = result.message;
            if (result.error_details && result.error_details.length > 0) {
                message += '\n\n错误详情：\n' + result.error_details.join('\n');
            }

            if (result.errors > 0) {
                showNotification(message, 'warning');
            } else {
                showNotification(message, 'success');
            }
        } else {
            throw new Error(result.message || '导入失败');
        }

        // 关闭模态框
        closeAllModals();

        // 重新加载数据
        await loadRecords();

    } catch (error) {
        console.error('导入失败:', error);
        showNotification(error.message || '导入失败', 'error');
    } finally {
        // 恢复按钮状态
        startImportBtn.textContent = originalText;
        startImportBtn.disabled = false;
    }
}

// 导出数据
async function exportData() {
    // 显示导出按钮加载状态
    const exportBtn = document.getElementById('exportBtn');
    const originalText = exportBtn.innerHTML;
    exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 导出中...';
    exportBtn.disabled = true;

    try {
        // 获取选中的记录ID
        const checkedBoxes = document.querySelectorAll('.record-checkbox:checked');
        const selectedIds = Array.from(checkedBoxes).map(cb => cb.value);

        // 获取当前搜索条件
        const searchInput = document.getElementById('searchInput');
        const searchTerm = searchInput ? searchInput.value.trim() : '';

        // 确定导出的记录数量
        let exportCount = 0;
        if (selectedIds.length > 0) {
            exportCount = selectedIds.length;
        } else if (searchTerm) {
            exportCount = filteredRecords.length;
        } else {
            exportCount = allRecords.length;
        }

        if (exportCount === 0) {
            showNotification('没有数据可以导出', 'warning');
            return;
        }

        let response;
        if (selectedIds.length > 0) {
            // 导出选中的记录
            showNotification(`正在导出 ${selectedIds.length} 条选中记录...`, 'info');
            response = await fetch('/api/batch_management/export', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
                },
                body: JSON.stringify({
                    record_ids: selectedIds,
                    search_term: searchTerm
                })
            });
        } else {
            // 导出所有记录（根据搜索条件）
            if (searchTerm) {
                showNotification(`正在导出搜索结果...`, 'info');
                response = await fetch('/api/batch_management/export', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
                    },
                    body: JSON.stringify({
                        record_ids: [],
                        search_term: searchTerm
                    })
                });
            } else {
                showNotification('正在导出所有记录...', 'info');
                response = await fetch('/api/batch_management/export', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
                    }
                });
            }
        }

        if (!response.ok) {
            throw new Error(`导出失败: ${response.status}`);
        }

        // 获取文件名
        const contentDisposition = response.headers.get('Content-Disposition');
        let filename = `批量管理数据_${new Date().toISOString().split('T')[0]}.xlsx`;
        if (contentDisposition) {
            const filenameMatch = contentDisposition.match(/filename=(.+)/);
            if (filenameMatch) {
                filename = filenameMatch[1];
            }
        }

        // 创建下载链接
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);

        showNotification('导出成功', 'success');

    } catch (error) {
        console.error('导出失败:', error);
        showNotification(error.message || '导出失败', 'error');
    } finally {
        // 恢复按钮状态
        exportBtn.innerHTML = originalText;
        exportBtn.disabled = false;
    }
}

// 生成唯一ID
function generateUniqueId() {
    return 'batch_' + Date.now() + '_' + Math.floor(Math.random() * 1000000).toString();
}

// 简单的通知函数（如果main.js中没有定义）
if (typeof showNotification === 'undefined') {
    function showNotification(message, type = 'info') {
        console.log(`[${type.toUpperCase()}] ${message}`);
        alert(message); // 简单的fallback
    }
}

// 简单的确认函数（如果main.js中没有定义）
if (typeof confirmAction === 'undefined') {
    function confirmAction(message, callback) {
        if (confirm(message)) {
            callback();
        }
    }
}
