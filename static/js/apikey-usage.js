// APIKey用量统计页面逻辑

document.addEventListener('DOMContentLoaded', function() {
    // 获取DOM元素
    const dateFilterForm = document.getElementById('dateFilterForm');
    const usageSummary = document.getElementById('usageSummary');
    const usageSummaryLoading = document.getElementById('usageSummaryLoading');
    const apikeyUsageTable = document.getElementById('apikeyUsageTable');
    const apikeyUsageList = document.getElementById('apikeyUsageList');
    const apikeyUsageLoading = document.getElementById('apikeyUsageLoading');
    const apikeyUsageEmpty = document.getElementById('apikeyUsageEmpty');
    const dailyUsageChart = document.getElementById('dailyUsageChart');
    const dailyUsageLoading = document.getElementById('dailyUsageLoading');
    const dailyUsageEmpty = document.getElementById('dailyUsageEmpty');
    
    // 分时统计元素
    const hourlyUsageForm = document.getElementById('hourlyUsageForm');
    const apikeySelect = document.getElementById('apikeySelect');
    const hourlyStartDate = document.getElementById('hourlyStartDate');
    const hourlyUsageChart = document.getElementById('hourlyUsageChart');
    const hourlyUsageLoading = document.getElementById('hourlyUsageLoading');
    const hourlyUsageEmpty = document.getElementById('hourlyUsageEmpty');
    
    // Chart.js 图表对象
    let usageChart = null;
    let hourlyChart = null;
    
    // 设置默认日期范围为过去30天
    setDefaultDateRange();
    
    // 加载初始数据
    loadUsageData();
    
    // 加载APIKey选项
    loadApiKeys();
    
    // 绑定日期筛选表单提交事件
    if (dateFilterForm) {
        dateFilterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            loadUsageData();
        });
    }
    
    // 绑定分时用量表单提交事件
    if (hourlyUsageForm) {
        hourlyUsageForm.addEventListener('submit', function(e) {
            e.preventDefault();
            loadHourlyData();
        });
    }
    
    // 设置默认日期范围
    function setDefaultDateRange() {
        const today = new Date();
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(today.getDate() - 30);
        
        const startDateInput = document.getElementById('startDate');
        const endDateInput = document.getElementById('endDate');
        
        if (startDateInput && endDateInput) {
            startDateInput.value = formatDateInput(thirtyDaysAgo);
            endDateInput.value = formatDateInput(today);
        }
        
        if (hourlyStartDate) {
            hourlyStartDate.value = formatDateInput(today);
        }
    }
    
    // 加载APIKey选项
    async function loadApiKeys() {
        if (!apikeySelect) return;
        
        try {
            const response = await API.apikey.getAll();
            
            if (response.success && response.data && response.data.length > 0) {
                apikeySelect.innerHTML = '<option value="">请选择APIKey</option>';
                
                response.data.forEach(apikey => {
                    const option = document.createElement('option');
                    option.value = apikey.apikey;
                    option.textContent = apikey.name;
                    apikeySelect.appendChild(option);
                });
            }
        } catch (error) {
            console.error('加载APIKey选项失败:', error);
        }
    }
    
    // 加载用量数据
    function loadUsageData() {
        const startDateInput = document.getElementById('startDate');
        const endDateInput = document.getElementById('endDate');
        
        let startDate = startDateInput ? startDateInput.value : null;
        let endDate = endDateInput ? endDateInput.value : null;
        
        // 显示所有加载状态
        usageSummaryLoading.style.display = 'flex';
        usageSummary.style.display = 'none';
        
        apikeyUsageLoading.style.display = 'flex';
        apikeyUsageTable.style.display = 'none';
        apikeyUsageEmpty.style.display = 'none';
        
        dailyUsageLoading.style.display = 'flex';
        dailyUsageChart.style.display = 'none';
        dailyUsageEmpty.style.display = 'none';
        
        // 调用API
        API.apikey.getTokenUsage(startDate, endDate).then(response => {
            // 处理返回数据
            if (response.success && response.data) {
                const data = response.data;
                
                // 更新总体用量统计
                updateUsageSummary(data.summary);
                
                // 更新APIKey用量列表
                updateApikeyUsageList(data.by_apikey);
                
                // 更新每日用量图表
                updateDailyUsageChart(data.by_date);
            } else {
                showNoDataMessage();
            }
        }).catch(error => {
            console.error('获取Token用量统计失败:', error);
            showNoDataMessage();
        });
    }
    
    // 加载分时用量数据
    function loadHourlyData() {
        if (!apikeySelect || !hourlyStartDate) return;
        
        const apikeyId = apikeySelect.value;
        const date = hourlyStartDate.value;
        
        if (!apikeyId) {
            alert('请选择APIKey');
            return;
        }
        
        if (!date) {
            alert('请选择日期');
            return;
        }
        
        // 显示加载状态
        hourlyUsageLoading.style.display = 'flex';
        hourlyUsageChart.style.display = 'none';
        hourlyUsageEmpty.style.display = 'none';
        
        // 调用API
        API.apikey.getHourlyTokenUsage(apikeyId, date).then(response => {
            hourlyUsageLoading.style.display = 'none';
            
            if (response.success && response.data && response.data.length > 0) {
                hourlyUsageChart.style.display = 'block';
                updateHourlyUsageChart(response.data);
            } else {
                hourlyUsageEmpty.style.display = 'flex';
            }
        }).catch(error => {
            console.error('获取分时Token用量统计失败:', error);
            hourlyUsageLoading.style.display = 'none';
            hourlyUsageEmpty.style.display = 'flex';
        });
    }
    
    // 更新总体用量统计
    function updateUsageSummary(summary) {
        if (!summary) {
            usageSummaryLoading.style.display = 'none';
            return;
        }
        
        const totalUpTokens = document.getElementById('totalUpTokens');
        const totalDownTokens = document.getElementById('totalDownTokens');
        const totalTokens = document.getElementById('totalTokens');
        
        if (totalUpTokens) {
            totalUpTokens.textContent = formatNumber(summary.total_uptoken || 0);
        }
        
        if (totalDownTokens) {
            totalDownTokens.textContent = formatNumber(summary.total_downtoken || 0);
        }
        
        if (totalTokens) {
            totalTokens.textContent = formatNumber(summary.total_tokens || 0);
        }
        
        usageSummaryLoading.style.display = 'none';
        usageSummary.style.display = 'flex';
    }
    
    // 更新APIKey用量列表
    function updateApikeyUsageList(usageData) {
        apikeyUsageLoading.style.display = 'none';
        
        if (!usageData || usageData.length === 0) {
            apikeyUsageEmpty.style.display = 'flex';
            return;
        }
        
        // 显示表格并清空现有内容
        apikeyUsageTable.style.display = 'table';
        apikeyUsageList.innerHTML = '';
        
        // 添加每个APIKey的用量数据
        usageData.forEach(item => {
            const row = document.createElement('tr');
            
            // 名称列
            const nameCell = document.createElement('td');
            nameCell.textContent = item.name;
            row.appendChild(nameCell);
            
            // 输入Token列
            const upTokenCell = document.createElement('td');
            upTokenCell.textContent = formatNumber(item.uptoken || 0);
            row.appendChild(upTokenCell);
            
            // 输出Token列
            const downTokenCell = document.createElement('td');
            downTokenCell.textContent = formatNumber(item.downtoken || 0);
            row.appendChild(downTokenCell);
            
            // 总Token列
            const totalTokenCell = document.createElement('td');
            totalTokenCell.textContent = formatNumber(item.total || 0);
            row.appendChild(totalTokenCell);
            
            apikeyUsageList.appendChild(row);
        });
    }
    
    // 更新每日用量图表
    function updateDailyUsageChart(dailyData) {
        dailyUsageLoading.style.display = 'none';
        
        if (!dailyData || dailyData.length === 0) {
            dailyUsageEmpty.style.display = 'flex';
            return;
        }
        
        dailyUsageChart.style.display = 'block';
        
        // 准备图表数据
        const dates = [];
        const upTokens = [];
        const downTokens = [];
        const totalTokens = [];
        
        dailyData.forEach(day => {
            dates.push(day.date);
            upTokens.push(day.uptoken || 0);
            downTokens.push(day.downtoken || 0);
            totalTokens.push(day.total || 0);
        });
        
        // 创建图表
        const ctx = document.getElementById('usageChart').getContext('2d');
        
        // 如果图表已存在，先销毁
        if (usageChart) {
            usageChart.destroy();
        }
        
        usageChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: dates,
                datasets: [
                    {
                        label: '输入Token',
                        data: upTokens,
                        borderColor: 'rgba(54, 162, 235, 1)',
                        backgroundColor: 'rgba(54, 162, 235, 0.2)',
                        borderWidth: 2,
                        tension: 0.1
                    },
                    {
                        label: '输出Token',
                        data: downTokens,
                        borderColor: 'rgba(255, 99, 132, 1)',
                        backgroundColor: 'rgba(255, 99, 132, 0.2)',
                        borderWidth: 2,
                        tension: 0.1
                    },
                    {
                        label: '总Token',
                        data: totalTokens,
                        borderColor: 'rgba(75, 192, 192, 1)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        borderWidth: 2,
                        tension: 0.1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Token数量'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '日期'
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false
                    }
                }
            }
        });
    }
    
    // 更新分时用量图表
    function updateHourlyUsageChart(hourlyData) {
        // 准备图表数据
        const hours = [];
        const upTokens = [];
        const downTokens = [];
        const totalTokens = [];
        
        // 确保24小时数据完整
        for (let i = 0; i < 24; i++) {
            const hour = i.toString().padStart(2, '0') + ':00';
            hours.push(hour);
            
            // 查找该小时的数据，如果不存在则为0
            const hourData = hourlyData.find(item => parseInt(item.hour) === i);
            
            upTokens.push(hourData ? hourData.uptoken || 0 : 0);
            downTokens.push(hourData ? hourData.downtoken || 0 : 0);
            totalTokens.push(hourData ? hourData.total || 0 : 0);
        }
        
        // 创建图表
        const ctx = document.getElementById('hourlyChart').getContext('2d');
        
        // 如果图表已存在，先销毁
        if (hourlyChart) {
            hourlyChart.destroy();
        }
        
        // 获取选中的APIKey名称
        const apikeyName = apikeySelect.options[apikeySelect.selectedIndex].text;
        const selectedDate = hourlyStartDate.value;
        
        hourlyChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: hours,
                datasets: [
                    {
                        label: '输入Token',
                        data: upTokens,
                        backgroundColor: 'rgba(54, 162, 235, 0.6)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    },
                    {
                        label: '输出Token',
                        data: downTokens,
                        backgroundColor: 'rgba(255, 99, 132, 0.6)',
                        borderColor: 'rgba(255, 99, 132, 1)',
                        borderWidth: 1
                    },
                    {
                        label: '总Token',
                        data: totalTokens,
                        backgroundColor: 'rgba(75, 192, 192, 0.6)',
                        borderColor: 'rgba(75, 192, 192, 1)',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Token数量'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '小时'
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false
                    },
                    title: {
                        display: true,
                        text: `${apikeyName} - ${selectedDate} 分时用量统计`
                    }
                }
            }
        });
    }
    
    // 显示无数据提示
    function showNoDataMessage() {
        usageSummaryLoading.style.display = 'none';
        usageSummary.style.display = 'none';
        
        apikeyUsageLoading.style.display = 'none';
        apikeyUsageTable.style.display = 'none';
        apikeyUsageEmpty.style.display = 'flex';
        
        dailyUsageLoading.style.display = 'none';
        dailyUsageChart.style.display = 'none';
        dailyUsageEmpty.style.display = 'flex';
    }
    
    // 格式化数字，添加千分位分隔符
    function formatNumber(num) {
        return num.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,');
    }
    
    // 格式化日期为YYYY-MM-DD格式
    function formatDateInput(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }
}); 