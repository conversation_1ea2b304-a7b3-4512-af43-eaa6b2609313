/**
 * 通用认证检查脚本
 * 在页面加载时自动检查用户是否已登录，如果未登录则提示并跳转到登录页
 */

document.addEventListener('DOMContentLoaded', function() {
    // 检查是否已引入auth07082.js
    if (typeof isAuthenticated !== 'function') {
        console.error('auth07082.js 未引入，无法进行认证检查');
        return;
    }

    // 检查用户是否已登录
    if (!isAuthenticated()) {
        if (typeof Swal !== 'undefined') {
            // 如果有SweetAlert2，使用它显示提示
            Swal.fire({
                title: '需要登录',
                text: '请先登录系统',
                icon: 'warning',
                confirmButtonText: '去登录'
            }).then(() => {
                window.location.href = '/login07082';
            });
        } else {
            // 否则使用普通的alert
            alert('请先登录系统');
            window.location.href = '/login07082';
        }
        return;
    }
    
    // 检查是否是管理员专用页面
    const isAdminOnlyPage = document.body.classList.contains('admin-only-page') || 
                           window.location.pathname.includes('user_management07082');
    
    if (isAdminOnlyPage && !isAdmin()) {
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: '访问受限',
                text: '您需要管理员权限才能访问此页面',
                icon: 'error',
                confirmButtonText: '返回首页'
            }).then(() => {
                window.location.href = '/index';
            });
        } else {
            alert('您需要管理员权限才能访问此页面');
            window.location.href = '/index';
        }
        return;
    }
    
    // 获取当前用户信息
    const currentUser = getCurrentUser();
    
    // 显示用户信息（如果页面上有相应元素）
    if (currentUser) {
        const userNicknameElement = document.getElementById('userNickname');
        if (userNicknameElement) {
            userNicknameElement.textContent = currentUser.nickname || currentUser.account;
        }
        
        const userTypeDisplayElement = document.getElementById('userTypeDisplay');
        if (userTypeDisplayElement) {
            userTypeDisplayElement.textContent = currentUser.userType === 'admin' ? '管理员' : '租户';
        }
    }
    
    // 处理管理员专用功能
    const adminOnlyElements = document.querySelectorAll('.admin-only');
    if (adminOnlyElements.length > 0) {
        if (!isAdmin()) {
            // 如果不是管理员，隐藏管理员专用功能
            adminOnlyElements.forEach(element => {
                element.style.display = 'none';
            });
        } else {
            // 如果是管理员，确保管理员专用功能可见
            adminOnlyElements.forEach(element => {
                element.style.display = '';
            });
        }
    }
    
    // 绑定退出按钮事件
    const logoutBtn = document.getElementById('logoutBtn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function() {
            logout();
        });
    }
}); 