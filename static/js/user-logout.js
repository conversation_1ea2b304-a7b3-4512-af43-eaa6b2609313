/**
 * 用户信息和退出功能
 * 在侧边栏底部添加当前登录用户信息和退出按钮
 */
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否已登录
    const token = localStorage.getItem('accessToken');
    if (!token) {
        // 如果没有令牌，则重定向到登录页面（除非当前已在登录页面）
        if (!window.location.pathname.includes('login07082')) {
            window.location.href = '/login07082';
        }
        return;
    }

    // 获取用户信息
    const userType = localStorage.getItem('userType') || '';
    const userNickname = localStorage.getItem('userNickname') || '';
    const userAccount = localStorage.getItem('userAccount') || '';

    // 创建用户信息和退出区域
    const userInfoElement = document.createElement('div');
    userInfoElement.className = 'sidebar-user-info';
    userInfoElement.innerHTML = `
        <div class="user-info-content">
            <div class="user-avatar">
                <i class="fas ${userType === 'admin' ? 'fa-user-shield' : 'fa-user'}"></i>
            </div>
            <div class="user-details">
                <div class="user-name">${userNickname || userAccount}</div>
                <div class="user-role">${userType === 'admin' ? '管理员' : '租户'}</div>
            </div>
        </div>
        <button class="logout-btn">
            <i class="fas fa-sign-out-alt"></i> 退出登录
        </button>
    `;

    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
        .sidebar-user-info {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            padding: 15px;
            background-color: rgba(0,0,0,0.2);
            color: white;
            border-top: 1px solid rgba(255,255,255,0.1);
        }
        
        .user-info-content {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: rgba(255,255,255,0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
        }
        
        .user-avatar i {
            font-size: 18px;
            color: rgba(255,255,255,0.9);
        }
        
        .user-details {
            flex: 1;
            overflow: hidden;
        }
        
        .user-name {
            font-weight: 600;
            font-size: 14px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .user-role {
            font-size: 12px;
            color: rgba(255,255,255,0.7);
        }
        
        .logout-btn {
            width: 100%;
            padding: 8px 0;
            border: none;
            background-color: rgba(255,255,255,0.1);
            color: white;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 13px;
        }
        
        .logout-btn:hover {
            background-color: rgba(255,255,255,0.2);
        }
        
        /* 调整侧边栏内容的高度，为用户信息腾出空间 */
        .sidebar-menu {
            max-height: calc(100vh - 180px);
            overflow-y: auto;
        }
    `;
    
    document.head.appendChild(style);

    // 添加到侧边栏
    const sidebar = document.querySelector('.sidebar');
    if (sidebar) {
        sidebar.appendChild(userInfoElement);
        
        // 为退出按钮添加事件
        const logoutBtn = userInfoElement.querySelector('.logout-btn');
        logoutBtn.addEventListener('click', function() {
            // 清除存储的令牌和用户信息
            localStorage.removeItem('accessToken');
            localStorage.removeItem('userType');
            localStorage.removeItem('userId');
            localStorage.removeItem('userAccount');
            localStorage.removeItem('userNickname');
            
            // 重定向到登录页
            window.location.href = '/login07082';
        });
    }
}); 