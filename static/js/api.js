// API调用封装
const API = {
    // 职位相关API
    position: {
        // 获取所有职位
        getAll: async function() {
            return await apiRequest('/positions/all');
        },
        
        // 获取单个职位详情
        getById: async function(dataId) {
            return await apiRequest(`/web_position/${dataId}`);
        },
        
        // 添加新职位
        add: async function(positionData) {
            return await apiRequest('/web_position', 'POST', positionData);
        },
        
        // 更新职位信息
        update: async function(dataId, positionData) {
            return await apiRequest(`/web_position/${dataId}`, 'PUT', positionData);
        },
        
        // 删除职位
        delete: async function(dataId) {
            return await apiRequest(`/web_position/${dataId}`, 'DELETE');
        }
    },
    
    // 用户相关API
    user: {
        // 获取所有用户
        getAll: async function() {
            return await apiRequest('/users/all');
        },
        
        // 获取单个用户详情
        getById: async function(userid) {
            return await apiRequest(`/user/${userid}`);
        },
        
        // 添加新用户
        add: async function(userData) {
            return await apiRequest('/web_create_user', 'POST', userData);
        },
        
        // 更新用户信息
        update: async function(userid, userData) {
            return await apiRequest(`/web_user/${userid}`, 'PUT', userData);
        },
        
        // 更新用户分数
        updateScore07081: async function(userid, score) {
            return await apiRequest(`/user/${userid}/score`, 'PUT', { score });
        },
        
        // 更新用户场景
        updateScene07081: async function(userid, sceneId) {
            return await apiRequest(`/user/${userid}/scene`, 'PUT', { sceneId });
        },
        
        // 删除用户
        delete: async function(userid) {
            return await apiRequest(`/user/${userid}`, 'DELETE');
        }
    },
    
    // 聊天记录相关API
    chatlog: {
        // 获取用户的所有聊天记录
        getByUser: async function(userid) {
            return await apiRequest(`/chatlogs/user/${userid}`);
        },
        
        // 获取场景的所有聊天记录
        getByScene: async function(sceneId) {
            return await apiRequest(`/chatlogs/scene/${sceneId}`);
        },
        
        // 添加新聊天记录
        add: async function(chatlogData) {
            return await apiRequest('/chatlog', 'POST', chatlogData);
        },
        
        // 删除聊天记录
        delete: async function(id) {
            return await apiRequest(`/chatlog/${id}`, 'DELETE');
        }
    },
    
    // 场景相关API
    scene: {
        // 获取所有场景
        getAll: async function() {
            return await apiRequest('/scenes');
        },
        
        // 获取单个场景详情
        getById: async function(sceneId) {
            return await apiRequest(`/scene/${sceneId}`);
        },
        
        // 获取职位的所有场景
        getByPosition: async function(positionId) {
            return await apiRequest(`/scenes/job_classification/position/${positionId}`);
        },
        
        // 获取职位的默认场景
        getDefaultByPosition: async function(positionId) {
            return await apiRequest(`/scene/default/${positionId}`);
        },
        
        // 添加新场景
        add: async function(sceneData) {
            return await apiRequest('/scene', 'POST', sceneData);
        },
        
        // 更新场景信息
        update: async function(sceneId, sceneData) {
            return await apiRequest(`/scene/${sceneId}`, 'PUT', sceneData);
        },
        
        // 删除场景
        delete: async function(sceneId) {
            return await apiRequest(`/scene/${sceneId}`, 'DELETE');
        }
    },
    
    // 场景问题相关API
    question: {
        // 获取单个问题详情
        getById: async function(questionId) {
            return await apiRequest(`/question/${questionId}`);
        },
        
        // 获取场景的所有问题
        getByScene: async function(sceneId) {
            return await apiRequest(`/questions/scene/${sceneId}`);
        },
        
        // 添加新问题
        add: async function(questionData) {
            return await apiRequest('/question', 'POST', questionData);
        },
        
        // 更新问题信息
        update: async function(questionId, questionData) {
            return await apiRequest(`/question/${questionId}`, 'PUT', questionData);
        },
        
        // 删除问题
        delete: async function(questionId) {
            return await apiRequest(`/question/${questionId}`, 'DELETE');
        }
    },

    // 场景切换器相关API
    sceneSwitcher: {
        // 获取职位分类的所有场景切换器
        getByJobClassification: async function(jobClassificationId) {
            return await apiRequest(`/scene_switchers/job_classification/${jobClassificationId}`);
        },

        // 获取单个场景切换器详情
        getById: async function(switcherId) {
            return await apiRequest(`/scene_switcher/${switcherId}`);
        },

        // 添加新场景切换器
        add: async function(switcherData) {
            return await apiRequest('/scene_switcher', 'POST', switcherData);
        },

        // 更新场景切换器信息
        update: async function(switcherId, switcherData) {
            return await apiRequest(`/scene_switcher/${switcherId}`, 'PUT', switcherData);
        },

        // 删除场景切换器
        delete: async function(switcherId) {
            return await apiRequest(`/scene_switcher/${switcherId}`, 'DELETE');
        }
    },
    
    // 评分相关API
    score: {
        // 获取用户的所有评分
        getByUser: async function(userid) {
            return await apiRequest(`/scores/user/${userid}`);
        },
        
        // 获取场景的所有评分
        getByScene: async function(sceneId) {
            return await apiRequest(`/scores/scene/${sceneId}`);
        },
        
        // 添加新评分
        add: async function(scoreData) {
            return await apiRequest('/score', 'POST', scoreData);
        },
        
        // 更新评分信息
        update: async function(id, scoreData) {
            return await apiRequest(`/score/${id}`, 'PUT', scoreData);
        },
        
        // 删除评分
        delete: async function(id) {
            return await apiRequest(`/score/${id}`, 'DELETE');
        }
    },
    
    // 虚拟HR相关API
    virtualHR: {
        // 获取所有虚拟HR
        getAll: async function() {
            return await apiRequest('/virtual-hrs');
        },
        
        // 获取单个虚拟HR详情
        getById: async function(id) {
            return await apiRequest(`/virtual-hr/${id}`);
        },
        
        // 添加新虚拟HR
        add: async function(hrData) {
            return await apiRequest('/virtual-hr', 'POST', hrData);
        },
        
        // 更新虚拟HR信息
        update: async function(id, hrData) {
            return await apiRequest(`/virtual-hr/${id}`, 'PUT', hrData);
        },
        
        // 删除虚拟HR
        delete: async function(id) {
            return await apiRequest(`/virtual-hr/${id}`, 'DELETE');
        }
    },
    
    // 职位分类相关API
    jobClassification: {
        // 获取所有职位分类
        getAll: async function() {
            return await apiRequest('/web_job_classifications');
        },
        
        // 获取单个职位分类详情
        getById: async function(id) {
            return await apiRequest(`/job_classification/${id}`);
        },
        
        // 添加新职位分类
        add: async function(classificationData) {
            return await apiRequest('/job_classification', 'POST', classificationData);
        },
        
        // 更新职位分类信息
        update: async function(id, classificationData) {
            return await apiRequest(`/job_classification/${id}`, 'PUT', classificationData);
        },
        
        // 删除职位分类
        delete: async function(id) {
            return await apiRequest(`/job_classification/${id}`, 'DELETE');
        }
    },
    
    // APIKey相关API
    apikey: {
        // 获取所有APIKey
        getAll: async function() {
            return await authenticatedApiRequest('/api/tenant_apikey/apikeys');
        },
        
        // 创建新的APIKey
        create: async function(name) {
            return await authenticatedApiRequest('/api/tenant/apikey', 'POST', { name });
        },
        
        // 删除APIKey
        delete: async function(id) {
            return await authenticatedApiRequest(`/api/tenant/apikey/${id}`, 'DELETE');
        },
        
        // 获取Token使用量统计
        getTokenUsage: async function(startDate, endDate) {
            let url = '/api/tenant_apikey/token-usage';
            if (startDate && endDate) {
                url += `?start_date=${startDate}&end_date=${endDate}`;
            }
            return await authenticatedApiRequest(url);
        },
        
        // 获取分时Token使用量统计
        getHourlyTokenUsage: async function(apikey, date) {
            let url = '/api/tenant_apikey/hourly-token-usage';
            if (apikey && date) {
                url += `?apikey=${apikey}&date=${date}`;
            }
            return await authenticatedApiRequest(url);
        }
    },

    // 批量管理相关API
    batchManagement: {
        // 获取所有批量管理记录
        getAll: async function() {
            return await apiRequest('/api/batch_management');
        },

        // 获取单个批量管理记录详情
        getById: async function(recordId) {
            return await apiRequest(`/api/batch_management/${recordId}`);
        },

        // 添加新批量管理记录
        add: async function(recordData) {
            return await apiRequest('/api/batch_management', 'POST', recordData);
        },

        // 更新批量管理记录信息
        update: async function(recordId, recordData) {
            return await apiRequest(`/api/batch_management/${recordId}`, 'PUT', recordData);
        },

        // 删除批量管理记录
        delete: async function(recordId) {
            return await apiRequest(`/api/batch_management/${recordId}`, 'DELETE');
        },

        // 导入批量管理记录
        import: async function(formData) {
            try {
                const response = await authenticatedFetch('/api/batch_management/import', {
                    method: 'POST',
                    body: formData // FormData对象，不设置Content-Type
                });
                return await response.json();
            } catch (error) {
                console.error('导入批量管理记录失败:', error);
                throw error;
            }
        }
    }
};

// 通用API请求函数
async function apiRequest(url, method = 'GET', data = null) {
    try {
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json'
            }
        };
        
        // 添加认证令牌到请求头
        const token = localStorage.getItem('accessToken');
        if (token) {
            options.headers['Authorization'] = `Bearer ${token}`;
        }
        
        if (data && (method === 'POST' || method === 'PUT')) {
            options.body = JSON.stringify(data);
        }
        
        const response = await fetch(url, options);
        const result = await response.json();
        
        // 检查响应中是否包含错误码
        if (result && result.code && result.code !== 200) {
            const error = new Error(result.message || '请求失败');
            error.code = result.code;
            error.data = result.data;
            throw error;
        }
        
        // 检查HTTP状态码
        if (!response.ok) {
            throw new Error('请求失败，服务器返回状态码: ' + response.status);
        }
        
        return result;
    } catch (error) {
        console.error('API请求失败:', error);
        throw error;
    }
}

// 使用authenticatedFetch的API请求函数
async function authenticatedApiRequest(url, method = 'GET', data = null) {
    try {
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json'
            }
        };
        
        if (data && (method === 'POST' || method === 'PUT')) {
            options.body = JSON.stringify(data);
        }
        
        // 使用authenticatedFetch函数，它会自动添加认证头
        const response = await authenticatedFetch(url, options);
        return await response.json();
    } catch (error) {
        console.error('认证API请求失败:', error);
        throw error;
    }
} 