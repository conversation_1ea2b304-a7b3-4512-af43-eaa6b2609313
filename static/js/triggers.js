// 确保页面有可用的API请求函数
if (typeof apiRequest !== 'function') {
    async function apiRequest(url, method = 'GET', data = null) {
        console.log(`API Request: ${method} ${url}`, data);
        var options = {
            method: method,
            headers: {
                'Content-Type': 'application/json'
            }
        };
        const token = localStorage.getItem('accessToken');
        if (token) {
            options.headers['Authorization'] = `Bearer ${token}`;
        }
        
        if (data && (method === 'POST' || method === 'PUT')) {
            options.body = JSON.stringify(data);
        }
        
        try {
            const response = await fetch(url, options);
            const result = await response.json();
            
            console.log(`API Response from ${url}:`, result);
            
            if (!response.ok) {
                throw new Error('请求失败，服务器返回状态码: ' + response.status);
            }
            
            return result;
        } catch (error) {
            console.error('API请求错误:', error);
            throw error;
        }
    }
}

// 确保有可用的通知函数
if (typeof showNotification !== 'function') {
    function showNotification(message, type = 'info') {
        console.log(`[${type}] ${message}`);
        alert(`${message}`);
    }
}

// 确保有可用的日期格式化函数
if (typeof formatDate !== 'function') {
    function formatDate(dateString) {
        if (!dateString) return '';
        
        try {
            const date = new Date(dateString);
            return date.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        } catch (e) {
            console.error("日期格式化错误:", e);
            return dateString;
        }
    }
}

// 确保有可用的确认操作函数
if (typeof confirmAction !== 'function') {
    function confirmAction(message, callback) {
        if (confirm(message)) {
            callback();
        }
    }
}

// 触发器管理页面脚本
document.addEventListener('DOMContentLoaded', () => {
    // 初始化页面
    console.log("触发器页面初始化...");
    initTriggersPage();
});

// 触发器数据
let scoreTriggers = [];
let semanticTriggers = [];
let isEditingScore = false;
let isEditingSemantic = false;
let config = {};

// 分页设置
let scoreCurrentPage = 1;
let scoreItemsPerPage = 10;
let semanticCurrentPage = 1;
let semanticItemsPerPage = 10;

// 搜索设置
let scoreSearchTerm = '';
let semanticSearchTerm = '';

// 初始化页面
async function initTriggersPage() {
    try {
        // 加载触发器数据
        await Promise.all([loadScoreTriggers(), loadSemanticTriggers(), loadConfig()]);
        
        // 初始化事件监听器
        initEventListeners();
        
    } catch (error) {
        console.error('初始化页面失败:', error);
        showNotification('初始化页面失败，请刷新页面重试', 'error');
    }
}

// 加载配置信息
async function loadConfig() {
    try {
        const response = await apiRequest('/config');
        if (response && response.data) {
            config = response.data;
            renderPromptCards();
        }
    } catch (error) {
        console.error('加载配置信息失败:', error);
        showNotification('加载配置信息失败', 'error');
    }
}

// 渲染提示卡片
function renderPromptCards() {
    // 渲染分数触发器提示卡片
    const scorePromptCard = document.getElementById('scoreTriggerPromptCard');
    if (scorePromptCard) {
        const promptContent = config.score_trigger_prompt || '暂无分数触发器提示';
        const promptTextarea = scorePromptCard.querySelector('textarea');
        if (promptTextarea) {
            promptTextarea.value = promptContent;
        }
    }
    
    // 渲染语义触发器提示卡片
    const semanticPromptCard = document.getElementById('semanticTriggerPromptCard');
    if (semanticPromptCard) {
        const promptContent = config.semantic_trigger_prompt || '暂无语义触发器提示';
        const promptTextarea = semanticPromptCard.querySelector('textarea');
        if (promptTextarea) {
            promptTextarea.value = promptContent;
        }
    }
}

// 更新配置
async function updateConfig(key, value) {
    try {
        const data = { [key]: value };
        await apiRequest('/update-config', 'POST', data);
        showNotification('配置更新成功', 'success');
        config[key] = value;
    } catch (error) {
        console.error('更新配置失败:', error);
        showNotification('更新配置失败', 'error');
    }
}

// 加载分数触发器数据
async function loadScoreTriggers() {
    try {
        const triggerTableBody = document.getElementById('scoreTriggerTable').querySelector('tbody');
        triggerTableBody.innerHTML = '<tr><td colspan="5" class="text-center">加载中...</td></tr>';
        
        // 获取所有分数触发器
        let response = await apiRequest('/score-triggers');
        
        // 处理API返回的数据
        if (response && response.data) {
            scoreTriggers = response.data;
        } else if (Array.isArray(response)) {
            scoreTriggers = response;
        } else {
            scoreTriggers = [];
            console.warn("Unexpected API response format:", response);
        }
        
        // 渲染分数触发器列表
        renderScoreTriggers();
        updateScorePagination();
        
    } catch (error) {
        console.error('加载分数触发器数据失败:', error);
        showNotification('加载分数触发器数据失败', 'error');
        
        const triggerTable = document.getElementById('scoreTriggerTable').querySelector('tbody');
        triggerTable.innerHTML = '<tr><td colspan="5" class="text-center">加载失败，请重试</td></tr>';
    }
}

// 加载语义触发器数据
async function loadSemanticTriggers() {
    try {
        const triggerTableBody = document.getElementById('semanticTriggerTable').querySelector('tbody');
        triggerTableBody.innerHTML = '<tr><td colspan="4" class="text-center">加载中...</td></tr>';
        
        // 获取所有语义触发器
        let response = await apiRequest('/semantic-triggers');
        
        // 处理API返回的数据
        if (response && response.data) {
            semanticTriggers = response.data;
        } else if (Array.isArray(response)) {
            semanticTriggers = response;
        } else {
            semanticTriggers = [];
            console.warn("Unexpected API response format:", response);
        }
        
        // 渲染语义触发器列表
        renderSemanticTriggers();
        updateSemanticPagination();
        
    } catch (error) {
        console.error('加载语义触发器数据失败:', error);
        showNotification('加载语义触发器数据失败', 'error');
        
        const triggerTable = document.getElementById('semanticTriggerTable').querySelector('tbody');
        triggerTable.innerHTML = '<tr><td colspan="4" class="text-center">加载失败，请重试</td></tr>';
    }
}

// 渲染分数触发器列表
function renderScoreTriggers() {
    const triggerTable = document.getElementById('scoreTriggerTable').querySelector('tbody');
    triggerTable.innerHTML = '';
    
    // 过滤数据
    let filteredTriggers = scoreTriggers;
    if (scoreSearchTerm) {
        const searchLower = scoreSearchTerm.toLowerCase();
        filteredTriggers = scoreTriggers.filter(trigger => 
            (trigger.score_trigger_name && trigger.score_trigger_name.toLowerCase().includes(searchLower)) || 
            (typeof trigger.action === 'string' && trigger.action.toLowerCase().includes(searchLower)) ||
            (typeof trigger.action === 'object' && JSON.stringify(trigger.action).toLowerCase().includes(searchLower))
        );
    }
    
    // 计算分页
    const startIndex = (scoreCurrentPage - 1) * scoreItemsPerPage;
    const paginatedTriggers = filteredTriggers.slice(startIndex, startIndex + scoreItemsPerPage);
    
    if (filteredTriggers.length === 0) {
        triggerTable.innerHTML = '<tr><td colspan="4" class="text-center">暂无数据</td></tr>';
        
        // 填充空行，保持固定高度
        for (let i = 1; i < scoreItemsPerPage; i++) {
            const emptyRow = document.createElement('tr');
            emptyRow.innerHTML = '<td colspan="4" class="text-center empty-row">&nbsp;</td>';
            triggerTable.appendChild(emptyRow);
        }
        return;
    }
    
    // 创建触发器列表行
    paginatedTriggers.forEach((trigger, index) => {
        if (!trigger) return;
        
        // 处理action显示，如果是对象则转为JSON字符串
        let actionDisplay = trigger.action || '未设置';
        if (typeof actionDisplay === 'object' && actionDisplay !== null) {
            try {
                actionDisplay = JSON.stringify(actionDisplay, null, 2);
            } catch (e) {
                actionDisplay = String(actionDisplay);
            }
        }
        
        const row = document.createElement('tr');
        
        row.innerHTML = `
            <td>${startIndex + index + 1}</td>
            <td>${trigger.score_trigger_name || '未命名'}</td>
            <td>${actionDisplay}</td>
            <td>
                <button class="btn btn-sm btn-action btn-edit" data-id="${trigger.id}">
                    <i class="fas fa-edit"></i> 编辑
                </button>
                <button class="btn btn-sm btn-action btn-delete" data-id="${trigger.id}">
                    <i class="fas fa-trash"></i> 删除
                </button>
            </td>
        `;
        
        triggerTable.appendChild(row);
        
        // 添加编辑按钮点击事件
        const editBtn = row.querySelector('.btn-edit');
        if (editBtn) {
            editBtn.addEventListener('click', () => editScoreTrigger(trigger.id));
        }
        
        // 添加删除按钮点击事件
        const deleteBtn = row.querySelector('.btn-delete');
        if (deleteBtn) {
            deleteBtn.addEventListener('click', () => deleteScoreTrigger(trigger.id));
        }
    });
    
    // 填充空行，保持固定高度
    const remainingRows = scoreItemsPerPage - paginatedTriggers.length;
    for (let i = 0; i < remainingRows; i++) {
        const emptyRow = document.createElement('tr');
        emptyRow.innerHTML = '<td colspan="4" class="text-center empty-row">&nbsp;</td>';
        triggerTable.appendChild(emptyRow);
    }
}

// 渲染语义触发器列表
function renderSemanticTriggers() {
    const triggerTable = document.getElementById('semanticTriggerTable').querySelector('tbody');
    triggerTable.innerHTML = '';
    
    // 过滤数据
    let filteredTriggers = semanticTriggers;
    if (semanticSearchTerm) {
        const searchLower = semanticSearchTerm.toLowerCase();
        filteredTriggers = semanticTriggers.filter(trigger => 
            (trigger.semantic_trigger_name && trigger.semantic_trigger_name.toLowerCase().includes(searchLower)) || 
            (trigger.semantic_content && trigger.semantic_content.toLowerCase().includes(searchLower)) ||
            (typeof trigger.action === 'string' && trigger.action.toLowerCase().includes(searchLower)) ||
            (typeof trigger.action === 'object' && JSON.stringify(trigger.action).toLowerCase().includes(searchLower))
        );
    }
    
    // 计算分页
    const startIndex = (semanticCurrentPage - 1) * semanticItemsPerPage;
    const paginatedTriggers = filteredTriggers.slice(startIndex, startIndex + semanticItemsPerPage);
    
    if (filteredTriggers.length === 0) {
        triggerTable.innerHTML = '<tr><td colspan="4" class="text-center">暂无数据</td></tr>';
        
        // 填充空行，保持固定高度
        for (let i = 1; i < semanticItemsPerPage; i++) {
            const emptyRow = document.createElement('tr');
            emptyRow.innerHTML = '<td colspan="4" class="text-center empty-row">&nbsp;</td>';
            triggerTable.appendChild(emptyRow);
        }
        return;
    }
    
    // 创建触发器列表行
    paginatedTriggers.forEach((trigger, index) => {
        if (!trigger) return;
        
        // 处理action显示，如果是对象则转为JSON字符串
        let actionDisplay = trigger.action || '未设置';
        if (typeof actionDisplay === 'object' && actionDisplay !== null) {
            try {
                actionDisplay = JSON.stringify(actionDisplay, null, 2);
            } catch (e) {
                actionDisplay = String(actionDisplay);
            }
        }
        
        const row = document.createElement('tr');
        
        row.innerHTML = `
            <td>${startIndex + index + 1}</td>
            <td>${trigger.semantic_trigger_name || '未命名'}</td>
            <td>${actionDisplay}</td>
            <td>
                <button class="btn btn-sm btn-action btn-edit" data-id="${trigger.id}">
                    <i class="fas fa-edit"></i> 编辑
                </button>
                <button class="btn btn-sm btn-action btn-delete" data-id="${trigger.id}">
                    <i class="fas fa-trash"></i> 删除
                </button>
            </td>
        `;
        
        triggerTable.appendChild(row);
        
        // 添加编辑按钮点击事件
        const editBtn = row.querySelector('.btn-edit');
        if (editBtn) {
            editBtn.addEventListener('click', () => editSemanticTrigger(trigger.id));
        }
        
        // 添加删除按钮点击事件
        const deleteBtn = row.querySelector('.btn-delete');
        if (deleteBtn) {
            deleteBtn.addEventListener('click', () => deleteSemanticTrigger(trigger.id));
        }
    });
    
    // 填充空行，保持固定高度
    const remainingRows = semanticItemsPerPage - paginatedTriggers.length;
    for (let i = 0; i < remainingRows; i++) {
        const emptyRow = document.createElement('tr');
        emptyRow.innerHTML = '<td colspan="4" class="text-center empty-row">&nbsp;</td>';
        triggerTable.appendChild(emptyRow);
    }
}

// 更新分数触发器分页信息
function updateScorePagination() {
    const paginationContainer = document.getElementById('scorePagination');
    if (!paginationContainer) return;
    
    let filteredTriggers = scoreTriggers;
    if (scoreSearchTerm) {
        const searchLower = scoreSearchTerm.toLowerCase();
        filteredTriggers = scoreTriggers.filter(trigger => 
            (trigger.score_trigger_name && trigger.score_trigger_name.toLowerCase().includes(searchLower)) || 
            (typeof trigger.action === 'string' && trigger.action.toLowerCase().includes(searchLower)) ||
            (typeof trigger.action === 'object' && JSON.stringify(trigger.action).toLowerCase().includes(searchLower))
        );
    }
    
    const totalPages = Math.max(1, Math.ceil(filteredTriggers.length / scoreItemsPerPage));
    
    // 确保当前页在有效范围内
    if (scoreCurrentPage > totalPages) {
        scoreCurrentPage = totalPages;
    }
    
    // 生成分页HTML
    const totalItems = filteredTriggers.length;
    const startItem = Math.min(totalItems, (scoreCurrentPage - 1) * scoreItemsPerPage + 1);
    const endItem = Math.min(totalItems, scoreCurrentPage * scoreItemsPerPage);
    
    paginationContainer.innerHTML = `
        <div class="pagination-info">
            显示 ${startItem}-${endItem} 条，共 ${totalItems} 条
        </div>
        <div class="pagination-controls">
            <button class="btn btn-sm" ${scoreCurrentPage === 1 ? 'disabled' : ''} data-page="prev">
                <i class="fas fa-chevron-left"></i>
            </button>
            <span class="pagination-current">${scoreCurrentPage} / ${totalPages}</span>
            <button class="btn btn-sm" ${scoreCurrentPage === totalPages ? 'disabled' : ''} data-page="next">
                <i class="fas fa-chevron-right"></i>
            </button>
            <select class="form-select form-select-sm items-per-page">
                <option value="10" ${scoreItemsPerPage === 10 ? 'selected' : ''}>10条/页</option>
                <option value="20" ${scoreItemsPerPage === 20 ? 'selected' : ''}>20条/页</option>
                <option value="50" ${scoreItemsPerPage === 50 ? 'selected' : ''}>50条/页</option>
            </select>
        </div>
    `;
    
    // 添加分页事件
    const prevBtn = paginationContainer.querySelector('[data-page="prev"]');
    const nextBtn = paginationContainer.querySelector('[data-page="next"]');
    const itemsSelect = paginationContainer.querySelector('.items-per-page');
    
    if (prevBtn) {
        prevBtn.addEventListener('click', () => {
            if (scoreCurrentPage > 1) {
                scoreCurrentPage--;
                renderScoreTriggers();
                updateScorePagination();
            }
        });
    }
    
    if (nextBtn) {
        nextBtn.addEventListener('click', () => {
            if (scoreCurrentPage < totalPages) {
                scoreCurrentPage++;
                renderScoreTriggers();
                updateScorePagination();
            }
        });
    }
    
    if (itemsSelect) {
        itemsSelect.addEventListener('change', () => {
            scoreItemsPerPage = parseInt(itemsSelect.value);
            scoreCurrentPage = 1;
            renderScoreTriggers();
            updateScorePagination();
        });
    }
}

// 更新语义触发器分页信息
function updateSemanticPagination() {
    const paginationContainer = document.getElementById('semanticPagination');
    if (!paginationContainer) return;
    
    let filteredTriggers = semanticTriggers;
    if (semanticSearchTerm) {
        const searchLower = semanticSearchTerm.toLowerCase();
        filteredTriggers = semanticTriggers.filter(trigger => 
            (trigger.semantic_trigger_name && trigger.semantic_trigger_name.toLowerCase().includes(searchLower)) || 
            (trigger.semantic_content && trigger.semantic_content.toLowerCase().includes(searchLower)) ||
            (typeof trigger.action === 'string' && trigger.action.toLowerCase().includes(searchLower)) ||
            (typeof trigger.action === 'object' && JSON.stringify(trigger.action).toLowerCase().includes(searchLower))
        );
    }
    
    const totalPages = Math.max(1, Math.ceil(filteredTriggers.length / semanticItemsPerPage));
    
    // 确保当前页在有效范围内
    if (semanticCurrentPage > totalPages) {
        semanticCurrentPage = totalPages;
    }
    
    // 生成分页HTML
    const totalItems = filteredTriggers.length;
    const startItem = Math.min(totalItems, (semanticCurrentPage - 1) * semanticItemsPerPage + 1);
    const endItem = Math.min(totalItems, semanticCurrentPage * semanticItemsPerPage);
    
    paginationContainer.innerHTML = `
        <div class="pagination-info">
            显示 ${startItem}-${endItem} 条，共 ${totalItems} 条
        </div>
        <div class="pagination-controls">
            <button class="btn btn-sm" ${semanticCurrentPage === 1 ? 'disabled' : ''} data-page="prev">
                <i class="fas fa-chevron-left"></i>
            </button>
            <span class="pagination-current">${semanticCurrentPage} / ${totalPages}</span>
            <button class="btn btn-sm" ${semanticCurrentPage === totalPages ? 'disabled' : ''} data-page="next">
                <i class="fas fa-chevron-right"></i>
            </button>
            <select class="form-select form-select-sm items-per-page">
                <option value="10" ${semanticItemsPerPage === 10 ? 'selected' : ''}>10条/页</option>
                <option value="20" ${semanticItemsPerPage === 20 ? 'selected' : ''}>20条/页</option>
                <option value="50" ${semanticItemsPerPage === 50 ? 'selected' : ''}>50条/页</option>
            </select>
        </div>
    `;
    
    // 添加分页事件
    const prevBtn = paginationContainer.querySelector('[data-page="prev"]');
    const nextBtn = paginationContainer.querySelector('[data-page="next"]');
    const itemsSelect = paginationContainer.querySelector('.items-per-page');
    
    if (prevBtn) {
        prevBtn.addEventListener('click', () => {
            if (semanticCurrentPage > 1) {
                semanticCurrentPage--;
                renderSemanticTriggers();
                updateSemanticPagination();
            }
        });
    }
    
    if (nextBtn) {
        nextBtn.addEventListener('click', () => {
            if (semanticCurrentPage < totalPages) {
                semanticCurrentPage++;
                renderSemanticTriggers();
                updateSemanticPagination();
            }
        });
    }
    
    if (itemsSelect) {
        itemsSelect.addEventListener('change', () => {
            semanticItemsPerPage = parseInt(itemsSelect.value);
            semanticCurrentPage = 1;
            renderSemanticTriggers();
            updateSemanticPagination();
        });
    }
}

// 搜索分数触发器
function searchScoreTriggers(term) {
    scoreSearchTerm = term;
    scoreCurrentPage = 1;
    renderScoreTriggers();
    updateScorePagination();
}

// 搜索语义触发器
function searchSemanticTriggers(term) {
    semanticSearchTerm = term;
    semanticCurrentPage = 1;
    renderSemanticTriggers();
    updateSemanticPagination();
}

// 初始化事件监听器
function initEventListeners() {
    // 分数触发器事件
    document.getElementById('addScoreTriggerBtn').addEventListener('click', showScoreTriggerModal);
    document.getElementById('saveScoreTriggerBtn').addEventListener('click', saveScoreTrigger);
    document.getElementById('cancelScoreTriggerBtn').addEventListener('click', hideScoreTriggerModal);
    document.querySelector('#scoreTriggerModal .modal-close').addEventListener('click', hideScoreTriggerModal);
    
    // 语义触发器事件
    document.getElementById('addSemanticTriggerBtn').addEventListener('click', showSemanticTriggerModal);
    document.getElementById('saveSemanticTriggerBtn').addEventListener('click', saveSemanticTrigger);
    document.getElementById('cancelSemanticTriggerBtn').addEventListener('click', hideSemanticTriggerModal);
    document.querySelector('#semanticTriggerModal .modal-close').addEventListener('click', hideSemanticTriggerModal);
    
    // 搜索事件
    const scoreSearchInput = document.getElementById('scoreSearchInput');
    if (scoreSearchInput) {
        scoreSearchInput.addEventListener('input', (e) => {
            searchScoreTriggers(e.target.value);
        });
    }
    
    const semanticSearchInput = document.getElementById('semanticSearchInput');
    if (semanticSearchInput) {
        semanticSearchInput.addEventListener('input', (e) => {
            searchSemanticTriggers(e.target.value);
        });
    }
    
    // 配置保存事件
    const saveScorePromptBtn = document.getElementById('saveScoreTriggerPromptBtn');
    if (saveScorePromptBtn) {
        saveScorePromptBtn.addEventListener('click', () => {
            const prompt = document.getElementById('scoreTriggerPrompt').value;
            updateConfig('score_trigger_prompt', prompt);
        });
    }
    
    const saveSemanticPromptBtn = document.getElementById('saveSemanticTriggerPromptBtn');
    if (saveSemanticPromptBtn) {
        saveSemanticPromptBtn.addEventListener('click', () => {
            const prompt = document.getElementById('semanticTriggerPrompt').value;
            updateConfig('semantic_trigger_prompt', prompt);
        });
    }
}

// 显示分数触发器模态框
function showScoreTriggerModal() {
    const modal = document.getElementById('scoreTriggerModal');
    document.getElementById('scoreTriggerForm').reset();
    document.getElementById('scoreTriggerModalTitle').textContent = '新增分数触发器';
    isEditingScore = false;
    modal.style.display = 'flex';
}

// 隐藏分数触发器模态框
function hideScoreTriggerModal() {
    const modal = document.getElementById('scoreTriggerModal');
    modal.style.display = 'none';
}

// 显示语义触发器模态框
function showSemanticTriggerModal() {
    const modal = document.getElementById('semanticTriggerModal');
    document.getElementById('semanticTriggerForm').reset();
    document.getElementById('semanticTriggerModalTitle').textContent = '新增语义触发器';
    isEditingSemantic = false;
    modal.style.display = 'flex';
}

// 隐藏语义触发器模态框
function hideSemanticTriggerModal() {
    const modal = document.getElementById('semanticTriggerModal');
    modal.style.display = 'none';
}

// 编辑分数触发器
async function editScoreTrigger(triggerId) {
    try {
        // 获取触发器详情
        const response = await apiRequest(`/score-trigger/${triggerId}`);
        const trigger = response.data;
        
        if (!trigger) {
            showNotification('获取触发器详情失败', 'error');
            return;
        }
        
        // 设置编辑模式
        isEditingScore = true;
        
        // 显示模态框，但不重置表单
        const modal = document.getElementById('scoreTriggerModal');
        document.getElementById('scoreTriggerModalTitle').textContent = '编辑分数触发器';
        modal.style.display = 'flex';
        
        // 处理action值，如果是对象则转为JSON字符串
        let actionValue = trigger.action || '';
        if (typeof actionValue === 'object' && actionValue !== null) {
            try {
                actionValue = JSON.stringify(actionValue, null, 2);
            } catch (e) {
                actionValue = String(actionValue);
            }
        }
        
        // 填充表单数据
        document.getElementById('scoreTriggerID').value = trigger.id;
        document.getElementById('score_trigger_name').value = trigger.score_trigger_name || '';
        document.getElementById('score').value = trigger.score || 0;
        document.getElementById('action').value = actionValue;
        document.getElementById('score_explanation').value = trigger.explanation || '';
        document.getElementById('repeated_triggering').value = trigger.repeated_triggering || 0;
        
    } catch (error) {
        console.error('获取触发器详情失败:', error);
        showNotification('获取触发器详情失败', 'error');
    }
}

// 编辑语义触发器
async function editSemanticTrigger(triggerId) {
    try {
        // 获取触发器详情
        const response = await apiRequest(`/semantic-trigger/${triggerId}`);
        const trigger = response.data;
        
        if (!trigger) {
            showNotification('获取语义触发器详情失败', 'error');
            return;
        }
        
        // 设置编辑模式
        isEditingSemantic = true;
        
        // 显示模态框，但不重置表单
        const modal = document.getElementById('semanticTriggerModal');
        document.getElementById('semanticTriggerModalTitle').textContent = '编辑语义触发器';
        modal.style.display = 'flex';
        
        // 处理action值，如果是对象则转为JSON字符串
        let actionValue = trigger.action || '';
        if (typeof actionValue === 'object' && actionValue !== null) {
            try {
                actionValue = JSON.stringify(actionValue, null, 2);
            } catch (e) {
                actionValue = String(actionValue);
            }
        }
        
        // 填充表单数据
        document.getElementById('semanticTriggerID').value = trigger.id;
        document.getElementById('semantic_trigger_name').value = trigger.semantic_trigger_name || '';
        document.getElementById('semantic_content').value = trigger.semantic_content || '';
        document.getElementById('semantic_action').value = actionValue;
        document.getElementById('semantic_explanation').value = trigger.explanation || '';
        document.getElementById('semantic_repeated_triggering').value = trigger.repeated_triggering || 0;
        
    } catch (error) {
        console.error('获取语义触发器详情失败:', error);
        showNotification('获取语义触发器详情失败', 'error');
    }
}

// 保存分数触发器
async function saveScoreTrigger() {
    try {
        const form = document.getElementById('scoreTriggerForm');
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }
        
        const id = document.getElementById('scoreTriggerID').value;
        
        // 处理action值，尝试解析为JSON对象
        let action = document.getElementById('action').value;
        try {
            // 检查是否是JSON格式
            if (action.trim().startsWith('{') && action.trim().endsWith('}')) {
                action = JSON.parse(action);
            }
        } catch (e) {
            // 如果解析失败，保持原始字符串
            console.warn('Action不是有效的JSON格式:', e);
        }
        
        const data = {
            score_trigger_name: document.getElementById('score_trigger_name').value,
            score: parseInt(document.getElementById('score').value),
            action: action,
            explanation: document.getElementById('score_explanation').value,
            repeated_triggering: parseInt(document.getElementById('repeated_triggering').value)
        };
        
        let response;
        if (isEditingScore) {
            // 更新现有触发器
            const token = localStorage.getItem('accessToken');
            response = await apiRequest(`/score-trigger/${id}`, 'PUT', data,{
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            showNotification('触发器更新成功', 'success');
        } else {
            // 添加新触发器
            const token = localStorage.getItem('accessToken');
            response = await apiRequest('/score-trigger', 'POST', data,{
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            showNotification('触发器添加成功', 'success');
        }
        
        // 重新加载数据
        await loadScoreTriggers();
        
        // 关闭模态框
        hideScoreTriggerModal();
        
    } catch (error) {
        console.error('保存触发器失败:', error);
        showNotification('保存触发器失败: ' + (error.message || '未知错误'), 'error');
    }
}

// 保存语义触发器
async function saveSemanticTrigger() {
    try {
        const form = document.getElementById('semanticTriggerForm');
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }
        
        const id = document.getElementById('semanticTriggerID').value;
        
        // 处理action值，尝试解析为JSON对象
        let action = document.getElementById('semantic_action').value;
        try {
            // 检查是否是JSON格式
            if (action.trim().startsWith('{') && action.trim().endsWith('}')) {
                action = JSON.parse(action);
            }
        } catch (e) {
            // 如果解析失败，保持原始字符串
            console.warn('Action不是有效的JSON格式:', e);
        }
        
        const data = {
            semantic_trigger_name: document.getElementById('semantic_trigger_name').value,
            semantic_content: document.getElementById('semantic_content').value,
            action: action,
            explanation: document.getElementById('semantic_explanation').value,
            repeated_triggering: parseInt(document.getElementById('semantic_repeated_triggering').value)
        };
        
        let response;
        if (isEditingSemantic) {
            // 更新现有触发器
            const token = localStorage.getItem('accessToken');
            response = await apiRequest(`/semantic-trigger/${id}`, 'PUT', data,{
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            showNotification('语义触发器更新成功', 'success');
        } else {
            // 添加新触发器
            const token = localStorage.getItem('accessToken');
            response = await apiRequest('/semantic-trigger', 'POST', data,{
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            showNotification('语义触发器添加成功', 'success');
        }
        
        // 重新加载数据
        await loadSemanticTriggers();
        
        // 关闭模态框
        hideSemanticTriggerModal();
        
    } catch (error) {
        console.error('保存语义触发器失败:', error);
        showNotification('保存语义触发器失败: ' + (error.message || '未知错误'), 'error');
    }
}

// 删除分数触发器
function deleteScoreTrigger(triggerId) {
    confirmAction('确定要删除此触发器吗？', async () => {
        try {
            const token = localStorage.getItem('accessToken');
            await apiRequest(`/score-trigger/${triggerId}`, 'DELETE',{
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            showNotification('触发器已删除', 'success');
            await loadScoreTriggers();
        } catch (error) {
            console.error('删除触发器失败:', error);
            showNotification('删除触发器失败', 'error');
        }
    });
}

// 删除语义触发器
function deleteSemanticTrigger(triggerId) {
    confirmAction('确定要删除此语义触发器吗？', async () => {
        try {
            const token = localStorage.getItem('accessToken');
            await apiRequest(`/semantic-trigger/${triggerId}`, 'DELETE',{
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            showNotification('语义触发器已删除', 'success');
            await loadSemanticTriggers();
        } catch (error) {
            console.error('删除语义触发器失败:', error);
            showNotification('删除语义触发器失败', 'error');
        }
    });
} 