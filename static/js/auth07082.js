/**
 * 认证工具函数
 */

// 检查用户是否已登录
function isAuthenticated() {
    return !!localStorage.getItem('accessToken');
}

// 检查用户是否是管理员
function isAdmin() {
    return localStorage.getItem('userType') === 'admin';
}

// 检查用户是否是租户
function isTenant() {
    return localStorage.getItem('userType') === 'tenant';
}

// 获取当前用户信息
function getCurrentUser() {
    if (!isAuthenticated()) return null;
    
    return {
        id: localStorage.getItem('userId'),
        account: localStorage.getItem('userAccount'),
        nickname: localStorage.getItem('userNickname'),
        userType: localStorage.getItem('userType')
    };
}

// 登出
function logout() {
    localStorage.removeItem('accessToken');
    localStorage.removeItem('userType');
    localStorage.removeItem('userId');
    localStorage.removeItem('userAccount');
    localStorage.removeItem('userNickname');
    
    // 重定向到登录页
    window.location.href = '/login07082';
}

// 为XHR请求添加JWT头的包装函数
function authenticatedFetch(url, options = {}) {
    const token = localStorage.getItem('accessToken');
    
    if (!token) {
        return Promise.reject(new Error('未登录，无法进行请求'));
    }
    
    // 合并选项
    const mergedOptions = {
        ...options,
        headers: {
            ...options.headers,
            'Authorization': `Bearer ${token}`
        }
    };
    
    return fetch(url, mergedOptions)
        .then(response => {
            // 如果返回401，可能是令牌过期，自动登出
            if (response.status === 401) {
                logout();
                throw new Error('登录已过期，请重新登录');
            }
            return response;
        });
}

// 权限检查
function checkPermission(requiredUserType = null) {
    if (!isAuthenticated()) {
        Swal.fire({
            title: '需要登录',
            text: '请先登录系统',
            icon: 'warning',
            confirmButtonText: '去登录'
        }).then(() => {
            window.location.href = '/login07082';
        });
        return false;
    }
    
    if (requiredUserType) {
        const userType = localStorage.getItem('userType');
        if (userType !== requiredUserType) {
            Swal.fire({
                title: '权限不足',
                text: '您没有访问此功能的权限',
                icon: 'error',
                confirmButtonText: '确定'
            });
            return false;
        }
    }
    
    return true;
} 