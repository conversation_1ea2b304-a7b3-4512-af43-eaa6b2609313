// 定时分析页面JavaScript

let currentAnalysisId = null;
let currentTenantId = null;

document.addEventListener('DOMContentLoaded', function() {
    // 检查权限 - 允许管理员和租户访问
    if (!isAuthenticated()) {
        Swal.fire({
            title: '需要登录',
            text: '请先登录系统',
            icon: 'warning',
            confirmButtonText: '去登录'
        }).then(() => {
            window.location.href = '/login07082';
        });
        return;
    }

    const userType = localStorage.getItem('userType');
    if (userType !== 'admin' && userType !== 'tenant') {
        Swal.fire({
            title: '权限不足',
            text: '您没有访问此功能的权限',
            icon: 'error',
            confirmButtonText: '确定'
        });
        return;
    }

    // 获取当前用户ID（管理员或租户）
    currentTenantId = localStorage.getItem('userId');

    // 初始化页面
    initPage();

    // 绑定事件
    bindEvents();

    // 绑定执行结果模态框事件
    bindExecutionResultModalEvents();
});

function initPage() {
    loadAnalysisList();
    loadUsersList();
    loadExecutionStatus();

    // 调试信息
    loadDebugInfo();
}

async function loadDebugInfo() {
    try {
        const token = localStorage.getItem('accessToken');
        const response = await fetch(`/api/tenant/${currentTenantId}/debug-info`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const result = await response.json();
            console.log('调试信息:', result);

            // 如果当前tenant_id没有用户，尝试使用第一个可用的tenant_id
            if (result.users_found === 0 && result.available_tenants.length > 0) {
                console.log('当前tenant_id没有用户，尝试使用:', result.available_tenants[0]);
                currentTenantId = result.available_tenants[0];
                localStorage.setItem('debugTenantId', currentTenantId);

                // 重新加载数据
                loadAnalysisList();
                loadUsersList();
                loadExecutionStatus();
            }
        }
    } catch (error) {
        console.error('加载调试信息失败:', error);
    }
}

function bindEvents() {
    // 新建分析按钮
    document.getElementById('createAnalysisBtn').addEventListener('click', function() {
        openAnalysisModal();
    });
    
    // 刷新用户列表按钮
    document.getElementById('refreshUsersBtn').addEventListener('click', function() {
        loadUsersList();
    });
    
    // 保存分析配置按钮
    document.getElementById('saveAnalysisBtn').addEventListener('click', function() {
        saveAnalysisConfig();
    });
    
    // 右上角关闭按钮事件
    const modalCloseBtn = document.getElementById('modalCloseBtn');
    if (modalCloseBtn) {
        modalCloseBtn.addEventListener('click', function() {
            closeAnalysisModal();
        });
    }

    // 取消按钮事件
    const modalCancelBtn = document.getElementById('modalCancelBtn');
    if (modalCancelBtn) {
        modalCancelBtn.addEventListener('click', function() {
            closeAnalysisModal();
        });
    }

    // 点击模态框背景关闭
    document.getElementById('analysisConfigModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeAnalysisModal();
        }
    });
}

async function loadAnalysisList() {
    try {
        const token = localStorage.getItem('accessToken');
        const response = await fetch(`/api/tenant/${currentTenantId}/scheduled-analysis`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (!response.ok) {
            throw new Error('获取分析列表失败');
        }
        
        const result = await response.json();
        renderAnalysisList(result.data || []);
        
    } catch (error) {
        console.error('加载分析列表失败:', error);
        showNotification('加载分析列表失败', 'error');
    }
}

function renderAnalysisList(analyses) {
    const container = document.getElementById('analysisList');
    
    if (analyses.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                <p class="text-muted">暂无分析配置，点击"新建分析"开始创建</p>
            </div>
        `;
        return;
    }
    
    container.innerHTML = analyses.map(analysis => `
        <div class="analysis-card ${currentAnalysisId === analysis.id ? 'selected' : ''}" data-id="${analysis.id}" onclick="selectAnalysis('${analysis.id}')">
            <div class="analysis-card-header">
                <h5 class="analysis-card-title">${analysis.name}</h5>
                <div class="analysis-card-actions">
                    <span class="status-badge ${analysis.is_auto_enabled ? 'active' : 'inactive'}">
                        ${analysis.is_auto_enabled ? '已启用' : '已禁用'}
                    </span>
                    <button class="btn btn-sm btn-success" onclick="event.stopPropagation(); executeAnalysis('${analysis.id}')">
                        <i class="fas fa-play"></i> 立即执行
                    </button>
                    <button class="btn btn-sm btn-primary" onclick="event.stopPropagation(); editAnalysis('${analysis.id}')">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="event.stopPropagation(); deleteAnalysis('${analysis.id}')">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                </div>
            </div>
            <div class="analysis-card-body">
                <div class="analysis-info">
                    <div class="info-item">
                        <span class="info-label">执行时间</span>
                        <span class="info-value">${analysis.schedule_time}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">分析天数</span>
                        <span class="info-value">${analysis.time_range_days} 天</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">职位分类</span>
                        <span class="info-value">${analysis.job_classification_name || '全部职位'}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">所属租户</span>
                        <span class="info-value">${analysis.tenant_display || '未知租户'}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">邮件接收者</span>
                        <span class="info-value">${analysis.email_recipients || '未设置'}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">最后执行</span>
                        <span class="info-value">${formatDateTime(analysis.last_execution_time) || '从未执行'}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">下次执行</span>
                        <span class="info-value">${formatDateTime(analysis.next_execution_time) || '未设置'}</span>
                    </div>
                </div>
                <div class="analysis-prompt">
                    ${analysis.prompt}
                </div>
            </div>
        </div>
    `).join('');
}

function selectAnalysis(analysisId) {
    // 更新当前选中的分析ID
    currentAnalysisId = analysisId;

    // 更新视觉选中状态
    document.querySelectorAll('.analysis-card').forEach(card => {
        card.classList.remove('selected');
    });
    document.querySelector(`[data-id="${analysisId}"]`).classList.add('selected');

    // 加载相关数据
    loadUsersList();
    loadExecutionStatus();
}

async function loadUsersList() {
    if (!currentAnalysisId) {
        document.getElementById('usersList').innerHTML = `
            <div class="empty-state">
                <p class="text-muted">请先选择一个分析配置</p>
            </div>
        `;
        return;
    }
    
    try {
        const token = localStorage.getItem('accessToken');
        const response = await fetch(`/api/tenant/${currentTenantId}/scheduled-analysis/${currentAnalysisId}/users`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (!response.ok) {
            throw new Error('获取用户列表失败');
        }
        
        const result = await response.json();
        renderUsersList(result.data || []);
        
    } catch (error) {
        console.error('加载用户列表失败:', error);
        showNotification('加载用户列表失败', 'error');
    }
}

function renderUsersList(users) {
    const container = document.getElementById('usersList');
    
    if (users.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-users fa-2x text-muted mb-3"></i>
                <p class="text-muted">当前时间范围内没有用户数据</p>
            </div>
        `;
        return;
    }
    
    container.innerHTML = users.map(user => `
        <div class="user-item">
            <div class="user-info">
                <div class="user-name">${user.real_name || user.name || '未知用户'}</div>
                <div class="user-details">
                    电话: ${user.phone || '未提供'} | 用户ID: ${user.userid}
                </div>
            </div>
            <div class="user-stats">
                <div class="message-count">${user.message_count}</div>
                <div class="message-label">条消息</div>
            </div>
        </div>
    `).join('');
}

async function loadExecutionStatus() {
    if (!currentAnalysisId) {
        document.getElementById('statusList').innerHTML = `
            <div class="empty-state">
                <p class="text-muted">请先选择一个分析配置</p>
            </div>
        `;
        return;
    }
    
    try {
        const token = localStorage.getItem('accessToken');
        const response = await fetch(`/api/tenant/${currentTenantId}/scheduled-analysis/${currentAnalysisId}/logs`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (!response.ok) {
            throw new Error('获取执行状态失败');
        }
        
        const result = await response.json();
        renderExecutionStatus(result.data || []);
        
    } catch (error) {
        console.error('加载执行状态失败:', error);
        showNotification('加载执行状态失败', 'error');
    }
}

function renderExecutionStatus(logs) {
    const container = document.getElementById('statusList');
    
    if (logs.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-history fa-2x text-muted mb-3"></i>
                <p class="text-muted">暂无执行记录</p>
            </div>
        `;
        return;
    }
    
    container.innerHTML = logs.map(log => `
        <div class="status-item">
            <div class="status-header">
                <span class="status-time">${log.execution_time}</span>
                <span class="status-badge ${log.status}">${getStatusText(log.status)}</span>
                <button class="view-result-btn" onclick="viewExecutionResult('${log.id}')"
                        ${log.status === 'running' ? 'disabled' : ''}>
                    <i class="fas fa-eye"></i> 查看
                </button>
            </div>
            <div class="status-details">
                <div>总用户: ${log.total_users}</div>
                <div>成功: ${log.successful_analyses}</div>
                <div>失败: ${log.failed_analyses}</div>
            </div>
            ${log.error_message ? `<div class="error-message text-danger mt-2">${log.error_message}</div>` : ''}
        </div>
    `).join('');
}

function getStatusText(status) {
    const statusMap = {
        'running': '执行中',
        'completed': '已完成',
        'failed': '失败'
    };
    return statusMap[status] || status;
}

function openAnalysisModal(analysisId = null) {
    currentAnalysisId = analysisId;
    const modal = document.getElementById('analysisConfigModal');
    const form = document.getElementById('analysisConfigForm');
    const title = document.getElementById('modalTitle');

    // 加载职位分类列表
    loadJobClassifications();

    if (analysisId) {
        title.textContent = '编辑分析配置';
        loadAnalysisConfig(analysisId);
    } else {
        title.textContent = '新建分析配置';
        form.reset();
        document.getElementById('analysisId').value = '';
    }

    modal.classList.add('show');
}

function closeAnalysisModal() {
    const modal = document.getElementById('analysisConfigModal');
    modal.classList.remove('show');
    currentAnalysisId = null;
}

async function loadJobClassifications() {
    try {
        const token = localStorage.getItem('accessToken');
        const response = await fetch(`/api/tenant/${currentTenantId}/job-classifications`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('获取职位分类失败');
        }

        const result = await response.json();
        const jobClassifications = result.data;

        // 填充职位分类下拉框
        const select = document.getElementById('jobClassificationId');
        // 清空现有选项（保留第一个默认选项）
        while (select.children.length > 1) {
            select.removeChild(select.lastChild);
        }

        // 添加职位分类选项
        jobClassifications.forEach(jc => {
            const option = document.createElement('option');
            option.value = jc.id;
            option.textContent = jc.class_name;
            select.appendChild(option);
        });

    } catch (error) {
        console.error('加载职位分类失败:', error);
        showNotification('加载职位分类失败', 'error');
    }
}

async function loadAnalysisConfig(analysisId) {
    try {
        const token = localStorage.getItem('accessToken');
        const response = await fetch(`/api/tenant/${currentTenantId}/scheduled-analysis/${analysisId}`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (!response.ok) {
            throw new Error('获取分析配置失败');
        }
        
        const result = await response.json();
        const analysis = result.data;
        
        // 填充表单
        document.getElementById('analysisId').value = analysis.id;
        document.getElementById('analysisName').value = analysis.name;
        document.getElementById('analysisPrompt').value = analysis.prompt;
        document.getElementById('scheduleTime').value = analysis.schedule_time;
        document.getElementById('timeRangeDays').value = analysis.time_range_days;
        document.getElementById('isAutoEnabled').checked = analysis.is_auto_enabled;
        document.getElementById('emailRecipients').value = analysis.email_recipients || '';
        document.getElementById('jobClassificationId').value = analysis.job_classification_id || '';
        
    } catch (error) {
        console.error('加载分析配置失败:', error);
        showNotification('加载分析配置失败', 'error');
    }
}

async function saveAnalysisConfig() {
    const form = document.getElementById('analysisConfigForm');
    const formData = new FormData(form);
    const analysisId = formData.get('analysisId');

    const data = {
        name: formData.get('name'),
        prompt: formData.get('prompt'),
        schedule_time: formData.get('schedule_time'),
        time_range_days: parseInt(formData.get('time_range_days')),
        is_auto_enabled: formData.has('is_auto_enabled'),
        email_recipients: formData.get('email_recipients'),
        job_classification_id: formData.get('job_classification_id')
    };

    // 前端验证：如果填写了邮件接收者，提醒用户检查邮件配置
    if (data.email_recipients && data.email_recipients.trim()) {
        const confirmResult = await Swal.fire({
            title: '邮件配置检查',
            text: '检测到您填写了邮件接收者。系统将检查邮件服务器配置是否完整，如果配置不完整将无法创建分析任务。',
            icon: 'info',
            showCancelButton: true,
            confirmButtonText: '继续创建',
            cancelButtonText: '取消',
            footer: '<a href="/global_settings" target="_blank">点击这里配置邮件服务器</a>'
        });

        if (!confirmResult.isConfirmed) {
            return;
        }
    }

    try {
        const token = localStorage.getItem('accessToken');
        const url = analysisId
            ? `/api/tenant/${currentTenantId}/scheduled-analysis/${analysisId}`
            : `/api/tenant/${currentTenantId}/scheduled-analysis`;
        const method = analysisId ? 'PUT' : 'POST';

        const response = await fetch(url, {
            method: method,
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });

        if (!response.ok) {
            const error = await response.json();

            // 如果是邮件配置问题，显示特殊提示
            if (error.detail && error.detail.includes('邮件服务器配置不完整')) {
                await Swal.fire({
                    title: '邮件配置不完整',
                    text: error.detail,
                    icon: 'warning',
                    confirmButtonText: '去配置邮件服务器',
                    showCancelButton: true,
                    cancelButtonText: '取消'
                }).then((result) => {
                    if (result.isConfirmed) {
                        window.open('/global_settings', '_blank');
                    }
                });
                return;
            }

            throw new Error(error.detail || '保存失败');
        }

        const result = await response.json();
        showNotification(analysisId ? '更新成功' : '创建成功', 'success');
        closeAnalysisModal();
        loadAnalysisList();

        // 如果启用了自动执行，添加定时任务
        if (data.is_auto_enabled) {
            const newAnalysisId = analysisId || result.analysis_id;
            await manageSchedule(newAnalysisId, 'add');
        }

    } catch (error) {
        console.error('保存分析配置失败:', error);
        showNotification(error.message, 'error');
    }
}

async function executeAnalysis(analysisId) {
    try {
        const token = localStorage.getItem('accessToken');
        const response = await fetch(`/api/tenant/${currentTenantId}/scheduled-analysis/${analysisId}/execute`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.detail || '执行失败');
        }

        const result = await response.json();
        showNotification(result.message, 'success');

        // 开始实时监控执行状态
        if (currentAnalysisId === analysisId) {
            startRealtimeStatusMonitoring(analysisId);
        }

    } catch (error) {
        console.error('执行分析失败:', error);
        showNotification(error.message, 'error');
    }
}

let realtimeStatusInterval = null;

function startRealtimeStatusMonitoring(analysisId) {
    // 清除之前的监控
    if (realtimeStatusInterval) {
        clearInterval(realtimeStatusInterval);
    }

    // 立即获取一次状态
    loadRealtimeStatus(analysisId);

    // 每3秒更新一次状态
    realtimeStatusInterval = setInterval(() => {
        loadRealtimeStatus(analysisId);
    }, 3000);
}

function stopRealtimeStatusMonitoring() {
    if (realtimeStatusInterval) {
        clearInterval(realtimeStatusInterval);
        realtimeStatusInterval = null;
    }
}

async function loadRealtimeStatus(analysisId) {
    try {
        const token = localStorage.getItem('accessToken');
        const response = await fetch(`/api/tenant/${currentTenantId}/scheduled-analysis/${analysisId}/realtime-status`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('获取实时状态失败');
        }

        const result = await response.json();
        const status = result.data;

        // 更新执行状态显示
        updateRealtimeStatusDisplay(status);

        // 如果任务完成，停止监控并刷新完整状态
        if (status.status === 'completed' || status.status === 'failed') {
            stopRealtimeStatusMonitoring();
            setTimeout(() => {
                loadExecutionStatus();
            }, 1000);
        }

    } catch (error) {
        console.error('获取实时状态失败:', error);
    }
}

function updateRealtimeStatusDisplay(status) {
    const statusContainer = document.getElementById('statusList');

    if (status.status === 'running') {
        // 显示实时进度
        const progressHtml = `
            <div class="realtime-status-item">
                <div class="status-header">
                    <span class="status-time">${status.execution_time}</span>
                    <span class="status-badge running">执行中</span>
                </div>
                <div class="progress-info">
                    <div class="progress-bar-container">
                        <div class="progress-bar" style="width: ${status.progress_percentage}%"></div>
                        <span class="progress-text">${status.progress_percentage}%</span>
                    </div>
                    <div class="progress-details">
                        <span>总用户: ${status.total_users}</span>
                        <span>已完成: ${status.completed_analyses}</span>
                        <span>成功: ${status.successful_analyses}</span>
                        <span>失败: ${status.failed_analyses}</span>
                    </div>
                </div>
            </div>
        `;

        // 查找是否已有实时状态显示
        const existingRealtime = statusContainer.querySelector('.realtime-status-item');
        if (existingRealtime) {
            existingRealtime.outerHTML = progressHtml;
        } else {
            statusContainer.insertAdjacentHTML('afterbegin', progressHtml);
        }
    }
}

async function editAnalysis(analysisId) {
    currentAnalysisId = analysisId;
    openAnalysisModal(analysisId);
    loadUsersList();
    loadExecutionStatus();
}

async function deleteAnalysis(analysisId) {
    const result = await Swal.fire({
        title: '确认删除',
        text: '确定要删除这个分析配置吗？此操作不可恢复。',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: '删除',
        cancelButtonText: '取消'
    });

    if (!result.isConfirmed) {
        return;
    }

    try {
        const token = localStorage.getItem('accessToken');
        const response = await fetch(`/api/tenant/${currentTenantId}/scheduled-analysis/${analysisId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.detail || '删除失败');
        }

        showNotification('删除成功', 'success');
        loadAnalysisList();

        // 移除定时任务
        await manageSchedule(analysisId, 'remove');

        // 如果当前选中的是被删除的分析，清空相关显示
        if (currentAnalysisId === analysisId) {
            currentAnalysisId = null;
            loadUsersList();
            loadExecutionStatus();
        }

    } catch (error) {
        console.error('删除分析配置失败:', error);
        showNotification(error.message, 'error');
    }
}

async function manageSchedule(analysisId, action) {
    try {
        const token = localStorage.getItem('accessToken');
        const response = await fetch(`/api/tenant/${currentTenantId}/scheduled-analysis/${analysisId}/schedule`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ action: action })
        });

        if (!response.ok) {
            const error = await response.json();
            console.warn('管理定时任务失败:', error.detail);
        }

    } catch (error) {
        console.error('管理定时任务失败:', error);
    }
}

function showNotification(message, type = 'info') {
    const iconMap = {
        'success': 'success',
        'error': 'error',
        'warning': 'warning',
        'info': 'info'
    };

    Swal.fire({
        title: message,
        icon: iconMap[type] || 'info',
        timer: 3000,
        showConfirmButton: false,
        toast: true,
        position: 'top-end'
    });
}

// 页面可见性变化时刷新数据
document.addEventListener('visibilitychange', function() {
    if (!document.hidden && currentAnalysisId) {
        loadExecutionStatus();
    }
});

// 定期刷新执行状态
setInterval(function() {
    if (!document.hidden && currentAnalysisId) {
        loadExecutionStatus();
    }
}, 30000); // 每30秒刷新一次

// 时间格式化函数
function formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return null;

    try {
        const date = new Date(dateTimeStr);
        if (isNaN(date.getTime())) return dateTimeStr; // 如果解析失败，返回原字符串

        const now = new Date();
        const diffMs = now - date;
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

        // 如果是今天
        if (diffDays === 0) {
            return date.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
        }
        // 如果是昨天
        else if (diffDays === 1) {
            return '昨天 ' + date.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
        }
        // 如果是明天
        else if (diffDays === -1) {
            return '明天 ' + date.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
        }
        // 如果是其他日期
        else {
            return date.toLocaleDateString('zh-CN', {
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }
    } catch (error) {
        console.error('时间格式化失败:', error);
        return dateTimeStr;
    }
}

// 查看执行结果
async function viewExecutionResult(logId) {
    try {
        const token = localStorage.getItem('accessToken');
        const response = await fetch(`/api/tenant/${currentTenantId}/scheduled-analysis/${currentAnalysisId}/logs/${logId}`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('获取执行结果失败');
        }

        const result = await response.json();
        showExecutionResultModal(result.data);

    } catch (error) {
        console.error('查看执行结果失败:', error);
        showNotification('查看执行结果失败: ' + error.message, 'error');
    }
}

// 显示执行结果模态框
function showExecutionResultModal(data) {
    console.log('显示执行结果模态框:', data);

    const modal = document.getElementById('executionResultModal');
    const executionInfo = document.getElementById('executionInfo');
    const markdownContent = document.getElementById('markdownContent');

    if (!modal || !executionInfo || !markdownContent) {
        console.error('模态框元素未找到:', { modal, executionInfo, markdownContent });
        return;
    }

    // 填充执行信息
    const log = data.log;
    executionInfo.innerHTML = `
        <div class="info-item" data-type="time">
            <div class="info-left">
                <div class="info-icon"><i class="fas fa-clock"></i></div>
                <div class="info-content">
                    <div class="info-label">执行时间</div>
                    <div class="info-value">${log.execution_time || 'N/A'}</div>
                </div>
            </div>
        </div>
        <div class="info-item" data-type="status">
            <div class="info-left">
                <div class="info-icon"><i class="fas fa-flag"></i></div>
                <div class="info-content">
                    <div class="info-label">执行状态</div>
                    <div class="info-value">
                        <span class="status-badge ${log.status}">${getStatusText(log.status)}</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="info-item" data-type="users">
            <div class="info-left">
                <div class="info-icon"><i class="fas fa-users"></i></div>
                <div class="info-content">
                    <div class="info-label">总用户数</div>
                    <div class="info-value">${log.total_users || 0}</div>
                </div>
            </div>
        </div>
        <div class="info-item" data-type="success">
            <div class="info-left">
                <div class="info-icon"><i class="fas fa-check-circle"></i></div>
                <div class="info-content">
                    <div class="info-label">成功分析数</div>
                    <div class="info-value">${log.successful_analyses || 0}</div>
                </div>
            </div>
        </div>
        <div class="info-item" data-type="failed">
            <div class="info-left">
                <div class="info-icon"><i class="fas fa-times-circle"></i></div>
                <div class="info-content">
                    <div class="info-label">失败分析数</div>
                    <div class="info-value">${log.failed_analyses || 0}</div>
                </div>
            </div>
        </div>
        <div class="info-item" data-type="email">
            <div class="info-left">
                <div class="info-icon"><i class="fas fa-envelope"></i></div>
                <div class="info-content">
                    <div class="info-label">邮件发送</div>
                    <div class="info-value">${log.email_sent ? '是' : '否'}</div>
                </div>
            </div>
        </div>
    `;

    // 渲染markdown内容
    if (typeof marked !== 'undefined' && data.markdown_content) {
        markdownContent.innerHTML = marked.parse(data.markdown_content);
    } else {
        markdownContent.innerHTML = `<pre>${data.markdown_content || '暂无内容'}</pre>`;
    }

    // 显示模态框
    modal.classList.add('show');
}

// 关闭执行结果模态框
function closeExecutionResultModal() {
    const modal = document.getElementById('executionResultModal');
    modal.classList.remove('show');
}

// 绑定执行结果模态框事件
function bindExecutionResultModalEvents() {
    const resultModalCloseBtn = document.getElementById('resultModalCloseBtn');
    const resultModalCancelBtn = document.getElementById('resultModalCancelBtn');

    if (resultModalCloseBtn) {
        resultModalCloseBtn.addEventListener('click', closeExecutionResultModal);
    }

    if (resultModalCancelBtn) {
        resultModalCancelBtn.addEventListener('click', closeExecutionResultModal);
    }

    // 点击模态框外部关闭
    const modal = document.getElementById('executionResultModal');
    if (modal) {
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeExecutionResultModal();
            }
        });
    }

    // ESC键关闭模态框
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && modal && modal.classList.contains('show')) {
            closeExecutionResultModal();
        }
    });
}
