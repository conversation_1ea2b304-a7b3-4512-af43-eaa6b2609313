!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("@antv/x6")):"function"==typeof define&&define.amd?define(["exports","@antv/x6"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).X6PluginScroller={},t.X6)}(this,(function(t,e){"use strict";function i(t,e,i,n){var o,s=arguments.length,r=s<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(t,e,i,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(r=(s<3?o(r):s>3?o(e,i,r):o(e,i))||r);return s>3&&r&&Object.defineProperty(e,i,r),r}class n extends e.View{get graph(){return this.options.graph}get model(){return this.graph.model}constructor(t){super(),this.padding={left:0,top:0,right:0,bottom:0},this.options=n.getOptions(t),this.onUpdate=e.FunctionExt.debounce(this.onUpdate,200);const i=this.graph.transform.getScale();this.sx=i.sx,this.sy=i.sy;const o=this.options.width||this.graph.options.width,s=this.options.height||this.graph.options.height;this.container=document.createElement("div"),e.Dom.addClass(this.container,this.prefixClassName(n.containerClass)),e.Dom.css(this.container,{width:o,height:s}),this.options.pageVisible&&e.Dom.addClass(this.container,this.prefixClassName(n.pagedClass)),this.options.className&&e.Dom.addClass(this.container,this.options.className);const r=this.graph.container;r.parentNode&&e.Dom.before(r,this.container),this.content=document.createElement("div"),e.Dom.addClass(this.content,this.prefixClassName(n.contentClass)),e.Dom.css(this.content,{width:this.graph.options.width,height:this.graph.options.height}),this.background=document.createElement("div"),e.Dom.addClass(this.background,this.prefixClassName(n.backgroundClass)),e.Dom.append(this.content,this.background),this.options.pageVisible||e.Dom.append(this.content,this.graph.view.grid),e.Dom.append(this.content,r),e.Dom.appendTo(this.content,this.container),this.startListening(),this.options.pageVisible||this.graph.grid.update(),this.backgroundManager=new n.Background(this),this.options.autoResize||this.update()}startListening(){const t=this.graph,e=this.model;t.on("scale",this.onScale,this),t.on("resize",this.onResize,this),t.on("before:print",this.storeScrollPosition,this),t.on("before:export",this.storeScrollPosition,this),t.on("after:print",this.restoreScrollPosition,this),t.on("after:export",this.restoreScrollPosition,this),e.on("reseted",this.onUpdate,this),e.on("cell:added",this.onUpdate,this),e.on("cell:removed",this.onUpdate,this),e.on("cell:changed",this.onUpdate,this),this.delegateBackgroundEvents()}stopListening(){const t=this.graph,e=this.model;t.off("scale",this.onScale,this),t.off("resize",this.onResize,this),t.off("beforeprint",this.storeScrollPosition,this),t.off("beforeexport",this.storeScrollPosition,this),t.off("afterprint",this.restoreScrollPosition,this),t.off("afterexport",this.restoreScrollPosition,this),e.off("reseted",this.onUpdate,this),e.off("cell:added",this.onUpdate,this),e.off("cell:removed",this.onUpdate,this),e.off("cell:changed",this.onUpdate,this),this.undelegateBackgroundEvents()}enableAutoResize(){this.options.autoResize=!0}disableAutoResize(){this.options.autoResize=!1}onUpdate(){this.options.autoResize&&this.update()}delegateBackgroundEvents(t){const i=t||e.GraphView.events;this.delegatedHandlers=Object.keys(i).reduce(((t,e)=>{const n=i[e];if(-1===e.indexOf(" "))if("function"==typeof n)t[e]=n;else{let i=this.graph.view[n];"function"==typeof i&&(i=i.bind(this.graph.view),t[e]=i)}return t}),{}),this.onBackgroundEvent=this.onBackgroundEvent.bind(this),Object.keys(this.delegatedHandlers).forEach((t=>{this.delegateEvent(t,{guarded:!1},this.onBackgroundEvent)}))}undelegateBackgroundEvents(){Object.keys(this.delegatedHandlers).forEach((t=>{this.undelegateEvent(t,this.onBackgroundEvent)}))}onBackgroundEvent(t){let e=!1;const i=t.target;if(this.options.pageVisible)e=this.options.background?this.background===i:this.content===i;else{const t=this.graph.view;e=t.background===i||t.grid===i}if(e){const e=this.delegatedHandlers[t.type];"function"==typeof e&&e.apply(this.graph,arguments)}}onResize(){this.cachedCenterPoint&&(this.centerPoint(this.cachedCenterPoint.x,this.cachedCenterPoint.y),this.updatePageBreak())}onScale({sx:t,sy:e,ox:i,oy:n}){this.updateScale(t,e),(i||n)&&(this.centerPoint(i,n),this.updatePageBreak());"function"==typeof this.options.autoResizeOptions&&this.update()}storeScrollPosition(){this.cachedScrollLeft=this.container.scrollLeft,this.cachedScrollTop=this.container.scrollTop}restoreScrollPosition(){this.container.scrollLeft=this.cachedScrollLeft,this.container.scrollTop=this.cachedScrollTop,this.cachedScrollLeft=null,this.cachedScrollTop=null}storeClientSize(){this.cachedClientSize={width:this.container.clientWidth,height:this.container.clientHeight}}restoreClientSize(){this.cachedClientSize=null}beforeManipulation(){(e.Platform.IS_IE||e.Platform.IS_EDGE)&&e.Dom.css(this.container,{visibility:"hidden"})}afterManipulation(){(e.Platform.IS_IE||e.Platform.IS_EDGE)&&e.Dom.css(this.container,{visibility:"visible"})}updatePageSize(t,e){null!=t&&(this.options.pageWidth=t),null!=e&&(this.options.pageHeight=e),this.updatePageBreak()}updatePageBreak(){if(this.pageBreak&&this.pageBreak.parentNode&&this.pageBreak.parentNode.removeChild(this.pageBreak),this.pageBreak=null,this.options.pageVisible&&this.options.pageBreak){const t=this.graph.options.width,i=this.graph.options.height,n=this.options.pageWidth*this.sx,o=this.options.pageHeight*this.sy;if(0===n||0===o)return;if(t>n||i>o){let s=!1;const r=document.createElement("div");for(let i=1,o=Math.floor(t/n);i<o;i+=1){const t=document.createElement("div");e.Dom.addClass(t,this.prefixClassName("graph-pagebreak-vertical")),e.Dom.css(t,{left:i*n}),e.Dom.appendTo(t,r),s=!0}for(let t=1,n=Math.floor(i/o);t<n;t+=1){const i=document.createElement("div");e.Dom.addClass(i,this.prefixClassName("graph-pagebreak-horizontal")),e.Dom.css(i,{top:t*o}),e.Dom.appendTo(i,r),s=!0}s&&(e.Dom.addClass(r,this.prefixClassName("graph-pagebreak")),e.Dom.after(this.graph.view.grid,r),this.pageBreak=r)}}}update(){const t=this.getClientSize();this.cachedCenterPoint=this.clientToLocalPoint(t.width/2,t.height/2);let i=this.options.autoResizeOptions;"function"==typeof i&&(i=e.FunctionExt.call(i,this,this));const n=Object.assign({gridWidth:this.options.pageWidth,gridHeight:this.options.pageHeight,allowNewOrigin:"negative"},i);this.graph.fitToContent(this.getFitToContentOptions(n))}getFitToContentOptions(t){const i=this.sx,n=this.sy;return t.gridWidth&&(t.gridWidth*=i),t.gridHeight&&(t.gridHeight*=n),t.minWidth&&(t.minWidth*=i),t.minHeight&&(t.minHeight*=n),"object"==typeof t.padding?t.padding={left:(t.padding.left||0)*i,right:(t.padding.right||0)*i,top:(t.padding.top||0)*n,bottom:(t.padding.bottom||0)*n}:"number"==typeof t.padding&&(t.padding*=i),this.options.autoResize||(t.contentArea=e.Rectangle.create()),t}updateScale(t,e){const i=this.graph.options,n=t/this.sx,o=e/this.sy;this.sx=t,this.sy=e,this.graph.translate(i.x*n,i.y*o),this.graph.transform.resize(i.width*n,i.height*o)}scrollbarPosition(t,i){if(null==t&&null==i)return{left:this.container.scrollLeft,top:this.container.scrollTop};const n={};return"number"==typeof t&&(n.scrollLeft=t),"number"==typeof i&&(n.scrollTop=i),e.Dom.prop(this.container,n),this}scrollToPoint(t,i){const n=this.getClientSize(),o=this.graph.matrix(),s={};return"number"==typeof t&&(s.scrollLeft=t-n.width/2+o.e+(this.padding.left||0)),"number"==typeof i&&(s.scrollTop=i-n.height/2+o.f+(this.padding.top||0)),e.Dom.prop(this.container,s),this}scrollToContent(){const t=this.sx,e=this.sy,i=this.graph.getContentArea().getCenter();return this.scrollToPoint(i.x*t,i.y*e)}scrollToCell(t){const e=this.sx,i=this.sy,n=t.getBBox().getCenter();return this.scrollToPoint(n.x*e,n.y*i)}center(t){return this.centerPoint(t)}centerPoint(t,e,i){const n=this.graph.matrix(),o=n.a,s=n.d,r=-n.e,a=-n.f,l=r+this.graph.options.width,h=a+this.graph.options.height;let c;if(this.storeClientSize(),"number"==typeof t||"number"==typeof e){c=i;const n=this.getVisibleArea().getCenter();"number"==typeof t?t*=o:t=n.x,"number"==typeof e?e*=s:e=n.y}else c=t,t=(r+l)/2,e=(a+h)/2;if(c&&c.padding)return this.positionPoint({x:t,y:e},"50%","50%",c);const p=this.getPadding(),g=this.getClientSize(),d=g.width/2,u=g.height/2,m=d-p.left-t+r,f=d-p.right+t-l,b=u-p.top-e+a,x=u-p.bottom+e-h;this.addPadding(Math.max(m,0),Math.max(f,0),Math.max(b,0),Math.max(x,0));const P=this.scrollToPoint(t,e);return this.restoreClientSize(),P}centerContent(t){return this.positionContent("center",t)}centerCell(t,e){return this.positionCell(t,"center",e)}positionContent(t,e){const i=this.graph.getContentArea(e);return this.positionRect(i,t,e)}positionCell(t,e,i){const n=t.getBBox();return this.positionRect(n,e,i)}positionRect(t,i,n){const o=e.Rectangle.create(t);switch(i){case"center":return this.positionPoint(o.getCenter(),"50%","50%",n);case"top":return this.positionPoint(o.getTopCenter(),"50%",0,n);case"top-right":return this.positionPoint(o.getTopRight(),"100%",0,n);case"right":return this.positionPoint(o.getRightMiddle(),"100%","50%",n);case"bottom-right":return this.positionPoint(o.getBottomRight(),"100%","100%",n);case"bottom":return this.positionPoint(o.getBottomCenter(),"50%","100%",n);case"bottom-left":return this.positionPoint(o.getBottomLeft(),0,"100%",n);case"left":return this.positionPoint(o.getLeftMiddle(),0,"50%",n);case"top-left":return this.positionPoint(o.getTopLeft(),0,0,n);default:return this}}positionPoint(t,i,n,o={}){const{padding:s}=o,r=function(t,e){var i={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(i[n]=t[n]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(t);o<n.length;o++)e.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(t,n[o])&&(i[n[o]]=t[n[o]])}return i}(o,["padding"]),a=e.NumberExt.normalizeSides(s),l=e.Rectangle.fromSize(this.getClientSize()),h=l.clone().moveAndExpand({x:a.left,y:a.top,width:-a.right-a.left,height:-a.top-a.bottom});(i=e.NumberExt.normalizePercentage(i,Math.max(0,h.width)))<0&&(i=h.width+i),(n=e.NumberExt.normalizePercentage(n,Math.max(0,h.height)))<0&&(n=h.height+n);const c=h.getTopLeft().translate(i,n),p=l.getCenter().diff(c),g=this.zoom(),d=p.scale(1/g,1/g),u=e.Point.create(t).translate(d);return this.centerPoint(u.x,u.y,r)}zoom(t,e){if(null==t)return this.sx;let i,n;e=e||{};const o=this.getClientSize(),s=this.clientToLocalPoint(o.width/2,o.height/2);let r=t,a=t;if(e.absolute||(r+=this.sx,a+=this.sy),e.scaleGrid&&(r=Math.round(r/e.scaleGrid)*e.scaleGrid,a=Math.round(a/e.scaleGrid)*e.scaleGrid),e.maxScale&&(r=Math.min(e.maxScale,r),a=Math.min(e.maxScale,a)),e.minScale&&(r=Math.max(e.minScale,r),a=Math.max(e.minScale,a)),r=this.graph.transform.clampScale(r),a=this.graph.transform.clampScale(a),e.center){const t=r/this.sx,o=a/this.sy;i=e.center.x-(e.center.x-s.x)/t,n=e.center.y-(e.center.y-s.y)/o}else i=s.x,n=s.y;return this.beforeManipulation(),this.graph.transform.scale(r,a),this.centerPoint(i,n),this.afterManipulation(),this}zoomToRect(t,i={}){const n=e.Rectangle.create(t),o=this.graph;if(i.contentArea=n,null==i.viewportArea){const t=this.container.getBoundingClientRect();i.viewportArea={x:o.options.x,y:o.options.y,width:t.width,height:t.height}}this.beforeManipulation(),o.transform.scaleContentToFitImpl(i,!1);const s=n.getCenter();return this.centerPoint(s.x,s.y),this.afterManipulation(),this}zoomToFit(t={}){return this.zoomToRect(this.graph.getContentArea(t),t)}transitionToPoint(t,i,o){let s,r;"object"==typeof t&&(o=i,i=t.y,t=t.x),null==o&&(o={});const a=this.sx,l=Math.max(o.scale||a,1e-6),h=this.getClientSize(),c=new e.Point(t,i),p=this.clientToLocalPoint(h.width/2,h.height/2);if(a===l){const t=p.diff(c).scale(a,a).round();s=`translate(${t.x}px,${t.y}px)`}else{const t=l/(a-l)*c.distance(p),e=p.clone().move(c,t),i=this.localToBackgroundPoint(e).round();s=`scale(${l/a})`,r=`${i.x}px ${i.y}px`}const g=o.onTransitionEnd;return e.Dom.addClass(this.container,n.transitionClassName),e.Dom.Event.off(this.content,n.transitionEventName),e.Dom.Event.on(this.content,n.transitionEventName,(n=>{this.syncTransition(l,{x:t,y:i}),"function"==typeof g&&e.FunctionExt.call(g,this,n.originalEvent)})),e.Dom.css(this.content,{transform:s,transformOrigin:r,transition:"transform",transitionDuration:o.duration||"1s",transitionDelay:o.delay,transitionTimingFunction:o.timing}),this}syncTransition(t,e){return this.beforeManipulation(),this.graph.scale(t),this.removeTransition(),this.centerPoint(e.x,e.y),this.afterManipulation(),this}removeTransition(){return e.Dom.removeClass(this.container,n.transitionClassName),e.Dom.Event.off(this.content,n.transitionEventName),e.Dom.css(this.content,{transform:"",transformOrigin:"",transition:"",transitionDuration:"",transitionDelay:"",transitionTimingFunction:""}),this}transitionToRect(t,i={}){const n=e.Rectangle.create(t),o=i.maxScale||1/0,s=i.minScale||Number.MIN_VALUE,r=i.scaleGrid||null,a=i.visibility||1,l=i.center?e.Point.create(i.center):n.getCenter(),h=this.getClientSize(),c=h.width*a,p=h.height*a;let g=new e.Rectangle(l.x-c/2,l.y-p/2,c,p).getMaxUniformScaleToFit(n,l);return g=Math.min(g,o),r&&(g=Math.floor(g/r)*r),g=Math.max(s,g),this.transitionToPoint(l,Object.assign({scale:g},i))}startPanning(t){const i=this.normalizeEvent(t);this.clientX=i.clientX,this.clientY=i.clientY,this.trigger("pan:start",{e:i}),e.Dom.Event.on(document.body,{"mousemove.panning touchmove.panning":this.pan.bind(this),"mouseup.panning touchend.panning":this.stopPanning.bind(this),"mouseleave.panning":this.stopPanning.bind(this)}),e.Dom.Event.on(window,"mouseup.panning",this.stopPanning.bind(this))}pan(t){const e=this.normalizeEvent(t),i=e.clientX-this.clientX,n=e.clientY-this.clientY;this.container.scrollTop-=n,this.container.scrollLeft-=i,this.clientX=e.clientX,this.clientY=e.clientY,this.trigger("panning",{e:e})}stopPanning(t){e.Dom.Event.off(document.body,".panning"),e.Dom.Event.off(window,".panning"),this.trigger("pan:stop",{e:t})}clientToLocalPoint(t,i){let n="object"==typeof t?t.x:t,o="object"==typeof t?t.y:i;const s=this.graph.matrix();return n+=this.container.scrollLeft-this.padding.left-s.e,o+=this.container.scrollTop-this.padding.top-s.f,new e.Point(n/s.a,o/s.d)}localToBackgroundPoint(t,i){const n="object"==typeof t?e.Point.create(t):new e.Point(t,i),o=this.graph.matrix(),s=this.padding;return e.Util.transformPoint(n,o).translate(s.left,s.top)}resize(t,i){let n=null!=t?t:this.container.offsetWidth,o=null!=i?i:this.container.offsetHeight;"number"==typeof n&&(n=Math.round(n)),"number"==typeof o&&(o=Math.round(o)),this.options.width=n,this.options.height=o,e.Dom.css(this.container,{width:n,height:o}),this.update()}getClientSize(){return this.cachedClientSize?this.cachedClientSize:{width:this.container.clientWidth,height:this.container.clientHeight}}autoScroll(t,e){const i=10,n=this.container,o=n.getBoundingClientRect();let s=0,r=0;return t<=o.left+i&&(s=-10),e<=o.top+i&&(r=-10),t>=o.right-i&&(s=i),e>=o.bottom-i&&(r=i),0!==s&&(n.scrollLeft+=s),0!==r&&(n.scrollTop+=r),{scrollerX:s,scrollerY:r}}addPadding(t,i,n,o){let s=this.getPadding();this.padding={left:Math.round(s.left+(t||0)),top:Math.round(s.top+(n||0)),bottom:Math.round(s.bottom+(o||0)),right:Math.round(s.right+(i||0))},s=this.padding,e.Dom.css(this.content,{width:s.left+this.graph.options.width+s.right,height:s.top+this.graph.options.height+s.bottom});const r=this.graph.container;return r.style.left=`${this.padding.left}px`,r.style.top=`${this.padding.top}px`,this}getPadding(){const t=this.options.padding;return"function"==typeof t?e.NumberExt.normalizeSides(e.FunctionExt.call(t,this,this)):e.NumberExt.normalizeSides(t)}getVisibleArea(){const t=this.graph.matrix(),i=this.getClientSize(),n={x:this.container.scrollLeft||0,y:this.container.scrollTop||0,width:i.width,height:i.height},o=e.Util.transformRectangle(n,t.inverse());return o.x-=(this.padding.left||0)/this.sx,o.y-=(this.padding.top||0)/this.sy,o}isCellVisible(t,e={}){const i=t.getBBox(),n=this.getVisibleArea();return e.strict?n.containsRect(i):n.isIntersectWithRect(i)}isPointVisible(t){return this.getVisibleArea().containsPoint(t)}lock(){return e.Dom.css(this.container,{overflow:"hidden"}),this}unlock(){return e.Dom.css(this.container,{overflow:"scroll"}),this}onRemove(){this.stopListening()}dispose(){e.Dom.before(this.container,this.graph.container),this.remove()}}i([e.View.dispose()],n.prototype,"dispose",null),function(t){class i extends e.BackgroundManager{get elem(){return this.scroller.background}constructor(t){super(t.graph),this.scroller=t,t.options.background&&this.draw(t.options.background)}init(){this.graph.on("scale",this.update,this),this.graph.on("translate",this.update,this)}updateBackgroundOptions(t){this.scroller.options.background=t}}t.Background=i}(n||(n={})),function(t){t.containerClass="graph-scroller",t.panningClass=`${t.containerClass}-panning`,t.pannableClass=`${t.containerClass}-pannable`,t.pagedClass=`${t.containerClass}-paged`,t.contentClass=`${t.containerClass}-content`,t.backgroundClass=`${t.containerClass}-background`,t.transitionClassName="transition-in-progress",t.transitionEventName="transitionend.graph-scroller-transition",t.defaultOptions={padding(){const t=this.getClientSize(),e=Math.max(this.options.minVisibleWidth||0,1)||1,i=Math.max(this.options.minVisibleHeight||0,1)||1,n=Math.max(t.width-e,0),o=Math.max(t.height-i,0);return{left:n,top:o,right:n,bottom:o}},minVisibleWidth:50,minVisibleHeight:50,pageVisible:!1,pageBreak:!1,autoResize:!0},t.getOptions=function(i){const n=e.ObjectExt.merge({},t.defaultOptions,i);null==n.pageWidth&&(n.pageWidth=i.graph.options.width),null==n.pageHeight&&(n.pageHeight=i.graph.options.height);const o=i.graph.options;return o.background&&n.enabled&&null==n.background&&(n.background=o.background,i.graph.background.clear()),n}}(n||(n={}));e.Graph.prototype.lockScroller=function(){const t=this.getPlugin("scroller");return t&&t.lockScroller(),this},e.Graph.prototype.unlockScroller=function(){const t=this.getPlugin("scroller");return t&&t.unlockScroller(),this},e.Graph.prototype.updateScroller=function(){const t=this.getPlugin("scroller");return t&&t.updateScroller(),this},e.Graph.prototype.getScrollbarPosition=function(){const t=this.getPlugin("scroller");return t?t.getScrollbarPosition():{left:0,top:0}},e.Graph.prototype.setScrollbarPosition=function(t,e){const i=this.getPlugin("scroller");return i&&i.setScrollbarPosition(t,e),this};class o extends e.Basecoat{get pannable(){return!!this.options&&("object"==typeof this.options.pannable?this.options.pannable.enabled:!!this.options.pannable)}get container(){return this.scrollerImpl.container}constructor(t={}){super(),this.name="scroller",this.options=t,e.CssLoader.ensure(this.name,".x6-graph-scroller {\n  position: relative;\n  box-sizing: border-box;\n  overflow: scroll;\n  outline: none;\n}\n.x6-graph-scroller-content {\n  position: relative;\n}\n.x6-graph-scroller-background {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n}\n.x6-graph-scroller .x6-graph {\n  position: absolute;\n  display: inline-block;\n  margin: 0;\n  box-shadow: none;\n}\n.x6-graph-scroller .x6-graph > svg {\n  display: block;\n}\n.x6-graph-scroller.x6-graph-scroller-paged .x6-graph {\n  box-shadow: 0 0 4px 0 #eee;\n}\n.x6-graph-scroller.x6-graph-scroller-pannable[data-panning='false'] {\n  cursor: grab;\n}\n.x6-graph-scroller.x6-graph-scroller-pannable[data-panning='true'] {\n  cursor: grabbing;\n  user-select: none;\n}\n.x6-graph-pagebreak {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n}\n.x6-graph-pagebreak-vertical {\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  box-sizing: border-box;\n  width: 1px;\n  border-left: 1px dashed #bdbdbd;\n}\n.x6-graph-pagebreak-horizontal {\n  position: absolute;\n  right: 0;\n  left: 0;\n  box-sizing: border-box;\n  height: 1px;\n  border-top: 1px dashed #bdbdbd;\n}\n")}init(t){this.graph=t;const e=n.getOptions(Object.assign(Object.assign({enabled:!0},this.options),{graph:t}));this.options=e,this.scrollerImpl=new n(e),this.setup(),this.startListening(),this.updateClassName(),this.scrollerImpl.center()}resize(t,e){this.scrollerImpl.resize(t,e)}resizePage(t,e){this.scrollerImpl.updatePageSize(t,e)}zoom(t,e){return void 0===t?this.scrollerImpl.zoom():(this.scrollerImpl.zoom(t,e),this)}zoomTo(t,e={}){return this.scrollerImpl.zoom(t,Object.assign(Object.assign({},e),{absolute:!0})),this}zoomToRect(t,e={}){return this.scrollerImpl.zoomToRect(t,e),this}zoomToFit(t={}){return this.scrollerImpl.zoomToFit(t),this}center(t){return this.centerPoint(t)}centerPoint(t,e,i){return this.scrollerImpl.centerPoint(t,e,i),this}centerContent(t){return this.scrollerImpl.centerContent(t),this}centerCell(t,e){return this.scrollerImpl.centerCell(t,e),this}positionPoint(t,e,i,n={}){return this.scrollerImpl.positionPoint(t,e,i,n),this}positionRect(t,e,i){return this.scrollerImpl.positionRect(t,e,i),this}positionCell(t,e,i){return this.scrollerImpl.positionCell(t,e,i),this}positionContent(t,e){return this.scrollerImpl.positionContent(t,e),this}drawBackground(t,e){return null!=this.graph.options.background&&e||this.scrollerImpl.backgroundManager.draw(t),this}clearBackground(t){return null!=this.graph.options.background&&t||this.scrollerImpl.backgroundManager.clear(),this}isPannable(){return this.pannable}enablePanning(){this.pannable||(this.options.pannable=!0,this.updateClassName())}disablePanning(){this.pannable&&(this.options.pannable=!1,this.updateClassName())}togglePanning(t){return null==t?this.isPannable()?this.disablePanning():this.enablePanning():t!==this.isPannable()&&(t?this.enablePanning():this.disablePanning()),this}lockScroller(){return this.scrollerImpl.lock(),this}unlockScroller(){return this.scrollerImpl.unlock(),this}updateScroller(){return this.scrollerImpl.update(),this}getScrollbarPosition(){return this.scrollerImpl.scrollbarPosition()}setScrollbarPosition(t,e){return this.scrollerImpl.scrollbarPosition(t,e),this}scrollToPoint(t,e){return this.scrollerImpl.scrollToPoint(t,e),this}scrollToContent(){return this.scrollerImpl.scrollToContent(),this}scrollToCell(t){return this.scrollerImpl.scrollToCell(t),this}transitionToPoint(t,e,i){return this.scrollerImpl.transitionToPoint(t,e,i),this}transitionToRect(t,e={}){return this.scrollerImpl.transitionToRect(t,e),this}enableAutoResize(){this.scrollerImpl.enableAutoResize()}disableAutoResize(){this.scrollerImpl.disableAutoResize()}autoScroll(t,e){return this.scrollerImpl.autoScroll(t,e)}clientToLocalPoint(t,e){return this.scrollerImpl.clientToLocalPoint(t,e)}setup(){this.scrollerImpl.on("*",((t,e)=>{this.trigger(t,e)}))}startListening(){let t=[];const i=this.options.pannable;t="object"==typeof i?i.eventTypes||[]:["leftMouseDown"],t.includes("leftMouseDown")&&(this.graph.on("blank:mousedown",this.preparePanning,this),this.graph.on("node:unhandled:mousedown",this.preparePanning,this),this.graph.on("edge:unhandled:mousedown",this.preparePanning,this)),t.includes("rightMouseDown")&&(this.onRightMouseDown=this.onRightMouseDown.bind(this),e.Dom.Event.on(this.scrollerImpl.container,"mousedown",this.onRightMouseDown))}stopListening(){let t=[];const i=this.options.pannable;t="object"==typeof i?i.eventTypes||[]:["leftMouseDown"],t.includes("leftMouseDown")&&(this.graph.off("blank:mousedown",this.preparePanning,this),this.graph.off("node:unhandled:mousedown",this.preparePanning,this),this.graph.off("edge:unhandled:mousedown",this.preparePanning,this)),t.includes("rightMouseDown")&&e.Dom.Event.off(this.scrollerImpl.container,"mousedown",this.onRightMouseDown)}onRightMouseDown(t){2===t.button&&this.allowPanning(t,!0)&&(this.updateClassName(!0),this.scrollerImpl.startPanning(t),this.scrollerImpl.once("pan:stop",(()=>this.updateClassName(!1))))}preparePanning({e:t}){const e=this.allowPanning(t,!0),i=this.graph.getPlugin("selection"),n=i&&i.allowRubberband(t,!0);(e||this.allowPanning(t)&&!n)&&(this.updateClassName(!0),this.scrollerImpl.startPanning(t),this.scrollerImpl.once("pan:stop",(()=>this.updateClassName(!1))))}allowPanning(t,i){return this.pannable&&e.ModifierKey.isMatch(t,this.options.modifiers,i)}updateClassName(t){const i=this.scrollerImpl.container,n=e.Config.prefix("graph-scroller-pannable");this.pannable?(e.Dom.addClass(i,n),i.dataset.panning=(!!t).toString()):e.Dom.removeClass(i,n)}dispose(){this.scrollerImpl.dispose(),this.stopListening(),this.off(),e.CssLoader.clean(this.name)}}i([e.Basecoat.dispose()],o.prototype,"dispose",null),t.Scroller=o}));
//# sourceMappingURL=index.js.map
