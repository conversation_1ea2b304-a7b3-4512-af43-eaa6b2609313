// 职位管理页面脚本
document.addEventListener('DOMContentLoaded', function() {
    // 初始化职位列表
    initPositionPage();
});

// 状态变量
let positions = [];
let jobClassifications = [];
let currentPage = 1;
let pageSize = 10;
let totalPages = 1;
let currentFilters = {
    search: '',
    city: '',
    sortBy: 'newest'
};

// 初始化职位页面
async function initPositionPage() {
    // 绑定事件监听
    bindEvents();
    
    // 加载职位分类数据
    await loadJobClassifications();
    
    // 加载职位数据
    await loadPositions();
    
    // 初始化城市筛选器
    initCityFilter();
}

// 加载职位分类数据
async function loadJobClassifications() {
    try {
        // 从API获取职位分类数据
        const token = localStorage.getItem('accessToken');
        const response = await fetch('/web_job_classifications',{
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        const result = await response.json();
        
        if (result.code === 200 && result.data) {
            jobClassifications = result.data;
            
            // 填充职位分类选择框
            const jobClassificationSelect = document.getElementById('jobClassificationId');
            
            // 清空现有选项（保留第一个"请选择"选项）
            while (jobClassificationSelect.options.length > 1) {
                jobClassificationSelect.remove(1);
            }
            
            // 添加职位分类选项
            jobClassifications.forEach(classification => {
                const option = document.createElement('option');
                option.value = classification.id;
                option.textContent = classification.class_name || '未命名分类';
                jobClassificationSelect.appendChild(option);
            });
        }
    } catch (error) {
        console.error('加载职位分类数据失败:', error);
    }
}

// 绑定事件监听
function bindEvents() {
    // 新增职位按钮
    const addPositionBtn = document.getElementById('addPositionBtn');
    addPositionBtn.addEventListener('click', showAddPositionModal);
    
    // 搜索按钮
    const searchBtn = document.getElementById('searchBtn');
    searchBtn.addEventListener('click', handleSearch);
    
    // 搜索输入框回车事件
    const searchInput = document.getElementById('searchInput');
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            handleSearch();
        }
    });
    
    // 城市筛选器变化事件
    const cityFilter = document.getElementById('cityFilter');
    cityFilter.addEventListener('change', handleFilterChange);
    
    // 排序方式变化事件
    const sortBy = document.getElementById('sortBy');
    sortBy.addEventListener('change', handleFilterChange);
    
    // 模态框关闭按钮
    const modalCloseButtons = document.querySelectorAll('.modal-close');
    modalCloseButtons.forEach(button => {
        button.addEventListener('click', closeAllModals);
    });
    
    // 取消按钮
    const cancelBtn = document.getElementById('cancelBtn');
    cancelBtn.addEventListener('click', closeAllModals);
    
    // 保存职位按钮
    const savePositionBtn = document.getElementById('savePositionBtn');
    savePositionBtn.addEventListener('click', savePosition);
    
    // 详情模态框关闭按钮
    const closeDetailBtn = document.getElementById('closeDetailBtn');
    closeDetailBtn.addEventListener('click', closeAllModals);
    
    // 编辑职位按钮
    const editPositionBtn = document.getElementById('editPositionBtn');
    editPositionBtn.addEventListener('click', function() {
        // 获取当前职位ID
        const positionId = document.getElementById('positionDetails').getAttribute('data-id');
        if (positionId) {
            // 先关闭详情模态框，再打开编辑模态框
            document.getElementById('positionDetailModal').style.display = 'none';
            openEditPositionModal(positionId);
        }
    });
}

// 加载职位数据
async function loadPositions() {
    try {
        const positionList = document.getElementById('positionList');
        
        // 显示加载状态
        positionList.innerHTML = `
            <div class="position-list-loading">
                <p>加载中...</p>
            </div>
        `;
        
        // 从API获取职位数据
        const allPositions = await API.position.getAll();
        
        // 如果没有职位数据，显示空状态
        if (!allPositions || allPositions.length === 0) {
            positionList.innerHTML = `
                <div class="position-empty">
                    <i class="fas fa-briefcase"></i>
                    <p>暂无职位数据</p>
                    <button class="btn btn-primary mt-10" id="emptyAddBtn">
                        <i class="fas fa-plus"></i> 添加职位
                    </button>
                </div>
            `;
            
            // 绑定空状态下的添加按钮
            const emptyAddBtn = document.getElementById('emptyAddBtn');
            if (emptyAddBtn) {
                emptyAddBtn.addEventListener('click', showAddPositionModal);
            }
            
            return;
        }
        
        // 应用筛选和排序
        positions = filterAndSortPositions(allPositions);
        
        // 计算分页
        totalPages = Math.ceil(positions.length / pageSize);
        
        // 渲染职位列表
        renderPositionList();
        
        // 渲染分页
        renderPagination();
    } catch (error) {
        console.error('加载职位数据失败:', error);
        
        const positionList = document.getElementById('positionList');
        positionList.innerHTML = `
            <div class="position-empty">
                <i class="fas fa-exclamation-circle"></i>
                <p>加载职位数据失败</p>
                <button class="btn btn-primary mt-10" id="retryBtn">
                    <i class="fas fa-redo"></i> 重试
                </button>
            </div>
        `;
        
        // 绑定重试按钮
        const retryBtn = document.getElementById('retryBtn');
        if (retryBtn) {
            retryBtn.addEventListener('click', loadPositions);
        }
    }
}

// 初始化城市筛选器
function initCityFilter() {
    // 获取所有不重复的城市
    const cities = [...new Set(positions.map(p => p.jobCity).filter(Boolean))];
    
    // 获取城市筛选器元素
    const cityFilter = document.getElementById('cityFilter');
    
    // 清空现有选项（保留"全部"选项）
    while (cityFilter.options.length > 1) {
        cityFilter.remove(1);
    }
    
    // 添加城市选项
    cities.forEach(city => {
        const option = document.createElement('option');
        option.value = city;
        option.textContent = city;
        cityFilter.appendChild(option);
    });
}

// 筛选和排序职位
function filterAndSortPositions(positionList) {
    // 复制数组以避免修改原数组
    let filtered = [...positionList];
    
    // 应用搜索筛选
    if (currentFilters.search) {
        const searchLower = currentFilters.search.toLowerCase();
        filtered = filtered.filter(position => 
            (position.positionName && position.positionName.toLowerCase().includes(searchLower)) ||
            (position.jobCity && position.jobCity.toLowerCase().includes(searchLower))
        );
    }
    
    // 应用城市筛选
    if (currentFilters.city) {
        filtered = filtered.filter(position => 
            position.jobCity === currentFilters.city
        );
    }
    
    // 应用排序
    switch (currentFilters.sortBy) {
        case 'name':
            filtered.sort((a, b) => (a.positionName || '').localeCompare(b.positionName || ''));
            break;
        case 'city':
            filtered.sort((a, b) => (a.jobCity || '').localeCompare(b.jobCity || ''));
            break;
        case 'newest':
        default:
            filtered.sort((a, b) => new Date(b.createdAt || 0) - new Date(a.createdAt || 0));
            break;
    }
    
    return filtered;
}

// 渲染职位列表
function renderPositionList() {
    const positionList = document.getElementById('positionList');
    
    // 计算当前页的职位
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, positions.length);
    const currentPagePositions = positions.slice(startIndex, endIndex);
    
    // 清空列表
    positionList.innerHTML = '';
    
    // 渲染每个职位
    currentPagePositions.forEach(position => {
        const positionItem = document.createElement('div');
        positionItem.className = 'position-item';
        
        // 解析年龄范围
        let ageRangeText = '不限';
        if (position.age) {
            try {
                // 检查是否已经是数组
                const ageRange = Array.isArray(position.age) 
                    ? position.age 
                    : JSON.parse(position.age);
                
                if (Array.isArray(ageRange) && ageRange.length === 2) {
                    ageRangeText = `${ageRange[0]}-${ageRange[1]}岁`;
                }
            } catch (e) {
                console.error('解析年龄范围失败:', e);
            }
        }
        
        // 解析教育要求
        let educationText = '不限';
        if (position.educationRequirements) {
            try {
                // 检查是否已经是数组
                const education = Array.isArray(position.educationRequirements) 
                    ? position.educationRequirements 
                    : JSON.parse(position.educationRequirements);
                
                if (Array.isArray(education) && education.length > 0) {
                    educationText = education.join(', ');
                }
            } catch (e) {
                console.error('解析教育要求失败:', e);
            }
        }
        
        positionItem.innerHTML = `
            <div class="position-header">
                <div class="position-title">${position.positionName || '未命名职位'}</div>
                <div class="position-actions">
                    <button class="btn btn-light btn-sm view-btn" data-id="${position.dataId}">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-light btn-sm edit-btn" data-id="${position.dataId}">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-light btn-sm delete-btn" data-id="${position.dataId}">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="position-info">
                <div class="position-tag-container">
                    <div class="position-tag position-city">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>${position.jobCity || '未指定城市'}</span>
                    </div>
                    <div class="position-tag position-salary">
                        <i class="fas fa-money-bill-wave"></i>
                        <span>${position.salaryRange || '薪资面议'}</span>
                    </div>
                    <div class="position-tag position-age">
                        <i class="fas fa-user"></i>
                        <span>${ageRangeText}</span>
                    </div>
                    <div class="position-tag position-education">
                        <i class="fas fa-graduation-cap"></i>
                        <span>${educationText}</span>
                    </div>
                </div>
                <div class="position-additional-info">
                    <div class="position-info-item">
                        <i class="fas fa-id-card"></i>
                        <span>Boss账号: ${position.boss_account ? (typeof position.boss_account === 'object' ? JSON.stringify(position.boss_account) : position.boss_account) : '未设置'}</span>
                    </div>
                    <div class="position-info-item">
                        <i class="fas fa-mobile-alt"></i>
                        <span>设备信息: ${position.device_info ? (typeof position.device_info === 'object' ? JSON.stringify(position.device_info) : position.device_info) : '未设置'}</span>
                    </div>
                </div>
            </div>
            <div class="position-meta">
                <span>创建时间: ${formatDate(position.createdAt)}</span>
                <span>更新时间: ${formatDate(position.updatedAt)}</span>
            </div>
        `;
        
        // 添加事件监听
        const viewBtn = positionItem.querySelector('.view-btn');
        viewBtn.addEventListener('click', function() {
            showPositionDetail(this.getAttribute('data-id'));
        });
        
        const editBtn = positionItem.querySelector('.edit-btn');
        editBtn.addEventListener('click', function() {
            openEditPositionModal(this.getAttribute('data-id'));
        });
        
        const deleteBtn = positionItem.querySelector('.delete-btn');
        deleteBtn.addEventListener('click', function() {
            confirmDeletePosition(this.getAttribute('data-id'));
        });
        
        positionList.appendChild(positionItem);
    });
}

// 渲染分页
function renderPagination() {
    const paginationContainer = document.getElementById('pagination');
    
    // 如果总页数为1，不显示分页
    if (totalPages <= 1) {
        paginationContainer.innerHTML = '';
        return;
    }
    
    // 创建分页HTML
    let paginationHTML = '<ul class="pagination">';
    
    // 上一页按钮
    paginationHTML += `
        <li class="pagination-item">
            <a class="pagination-link ${currentPage === 1 ? 'disabled' : ''}" id="prevPage">
                <i class="fas fa-chevron-left"></i>
            </a>
        </li>
    `;
    
    // 页码按钮
    const maxPageButtons = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxPageButtons / 2));
    let endPage = Math.min(totalPages, startPage + maxPageButtons - 1);
    
    if (endPage - startPage + 1 < maxPageButtons) {
        startPage = Math.max(1, endPage - maxPageButtons + 1);
    }
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `
            <li class="pagination-item">
                <a class="pagination-link ${i === currentPage ? 'active' : ''}" data-page="${i}">${i}</a>
            </li>
        `;
    }
    
    // 下一页按钮
    paginationHTML += `
        <li class="pagination-item">
            <a class="pagination-link ${currentPage === totalPages ? 'disabled' : ''}" id="nextPage">
                <i class="fas fa-chevron-right"></i>
            </a>
        </li>
    `;
    
    paginationHTML += '</ul>';
    
    // 渲染分页
    paginationContainer.innerHTML = paginationHTML;
    
    // 绑定分页事件
    const pageLinks = document.querySelectorAll('.pagination-link[data-page]');
    pageLinks.forEach(link => {
        link.addEventListener('click', function() {
            const page = parseInt(this.getAttribute('data-page'));
            if (page !== currentPage) {
                currentPage = page;
                renderPositionList();
                renderPagination();
                // 滚动到顶部
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }
        });
    });
    
    // 绑定上一页按钮
    const prevPageBtn = document.getElementById('prevPage');
    if (prevPageBtn && currentPage > 1) {
        prevPageBtn.addEventListener('click', function() {
            currentPage--;
            renderPositionList();
            renderPagination();
            // 滚动到顶部
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });
    }
    
    // 绑定下一页按钮
    const nextPageBtn = document.getElementById('nextPage');
    if (nextPageBtn && currentPage < totalPages) {
        nextPageBtn.addEventListener('click', function() {
            currentPage++;
            renderPositionList();
            renderPagination();
            // 滚动到顶部
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });
    }
}

// 处理搜索
function handleSearch() {
    const searchInput = document.getElementById('searchInput');
    currentFilters.search = searchInput.value.trim();
    currentPage = 1; // 重置为第一页
    
    // 重新筛选和排序
    positions = filterAndSortPositions(positions);
    
    // 重新计算分页
    totalPages = Math.ceil(positions.length / pageSize);
    
    // 重新渲染列表和分页
    renderPositionList();
    renderPagination();
}

// 处理筛选条件变化
function handleFilterChange() {
    const cityFilter = document.getElementById('cityFilter');
    const sortBy = document.getElementById('sortBy');
    
    currentFilters.city = cityFilter.value;
    currentFilters.sortBy = sortBy.value;
    currentPage = 1; // 重置为第一页
    
    // 重新加载职位
    loadPositions();
}

// 显示添加职位模态框
function showAddPositionModal() {
    // 重置表单
    const positionForm = document.getElementById('positionForm');
    positionForm.reset();
    
    // 设置模态框标题
    document.getElementById('modalTitle').textContent = '新增职位';
    
    // 清除隐藏ID字段
    document.getElementById('positionId').value = '';
    
    // 显示模态框
    const modal = document.getElementById('positionModal');
    modal.style.display = 'block';
}

// 打开编辑职位模态框
async function openEditPositionModal(positionId) {
    try {
        // 获取职位数据
        const position = await API.position.getById(positionId);
        
        if (!position) {
            showNotification('获取职位数据失败', 'error');
            return;
        }
        
        // 设置模态框标题
        document.getElementById('modalTitle').textContent = '编辑职位';
        
        // 填充表单数据
        document.getElementById('positionId').value = position.dataId;
        document.getElementById('positionName').value = position.positionName || '';
        document.getElementById('jobCity').value = position.jobCity || '';
        document.getElementById('salaryRange').value = position.salaryRange || '';
        
        // 处理年龄范围
        if (position.age) {
            try {
                // 检查是否已经是数组
                const ageRange = Array.isArray(position.age) 
                    ? position.age 
                    : JSON.parse(position.age);
                
                if (Array.isArray(ageRange) && ageRange.length === 2) {
                    document.getElementById('ageMin').value = ageRange[0] || '';
                    document.getElementById('ageMax').value = ageRange[1] || '';
                }
            } catch (e) {
                console.error('解析年龄范围失败:', e);
            }
        }
        
        // 处理性别
        document.getElementById('gender').value = position.gender || '';
        
        // 设置boss_account (转换对象为字符串)
        if (position.boss_account) {
            if (typeof position.boss_account === 'object') {
                document.getElementById('boss_account').value = JSON.stringify(position.boss_account);
            } else {
                document.getElementById('boss_account').value = position.boss_account;
            }
        } else {
            document.getElementById('boss_account').value = '';
        }
        
        // 设置device_info (转换对象为字符串)
        if (position.device_info) {
            if (typeof position.device_info === 'object') {
                document.getElementById('device_info').value = JSON.stringify(position.device_info);
            } else {
                document.getElementById('device_info').value = position.device_info;
            }
        } else {
            document.getElementById('device_info').value = '';
        }
        
        // 设置boss_scene (转换数组对象为字符串)
        if (position.boss_scene) {
            if (Array.isArray(position.boss_scene)) {
                document.getElementById('boss_scene').value = JSON.stringify(position.boss_scene);
            } else {
                document.getElementById('boss_scene').value = position.boss_scene;
            }
        } else {
            document.getElementById('boss_scene').value = '';
        }
        
        // 设置是否启用Boss场景图片
        document.getElementById('enable_boss_scene').value = position.enable_boss_scene ? '1' : '0';
        
        // 处理学历要求
        const educationSelect = document.getElementById('educationRequirements');
        if (position.educationRequirements) {
            try {
                // 检查是否已经是数组
                const education = Array.isArray(position.educationRequirements) 
                    ? position.educationRequirements 
                    : JSON.parse(position.educationRequirements);
                
                if (Array.isArray(education)) {
                    // 清除所有选中状态
                    for (let i = 0; i < educationSelect.options.length; i++) {
                        educationSelect.options[i].selected = false;
                    }
                    
                    // 设置选中状态
                    for (let i = 0; i < educationSelect.options.length; i++) {
                        if (education.includes(educationSelect.options[i].value)) {
                            educationSelect.options[i].selected = true;
                        }
                    }
                }
            } catch (e) {
                console.error('解析教育要求失败:', e);
            }
        }
        
        // 处理经验要求
        if (position.experienceRequirements) {
            try {
                // 检查是否已经是数组
                const experience = Array.isArray(position.experienceRequirements) 
                    ? position.experienceRequirements 
                    : JSON.parse(position.experienceRequirements);
                
                if (Array.isArray(experience)) {
                    document.getElementById('experienceRequirements').value = experience.join('\n');
                }
            } catch (e) {
                console.error('解析岗位描述失败:', e);
            }
        }
        
        // 设置职位分类的选中状态
        const jobClassificationSelect = document.getElementById('jobClassificationId');
        if (position.job_classification_id) {
            jobClassificationSelect.value = position.job_classification_id;
        }
        
        // 显示模态框
        const modal = document.getElementById('positionModal');
        modal.style.display = 'block';
    } catch (error) {
        console.error('获取职位数据失败:', error);
        showNotification('获取职位数据失败', 'error');
    }
}

// 保存职位
async function savePosition() {
    // 获取表单数据
    const positionForm = document.getElementById('positionForm');
    
    // 表单验证
    if (!positionForm.checkValidity()) {
        // 触发浏览器的默认表单验证
        positionForm.reportValidity();
        return;
    }
    
    // 收集表单数据
    const positionId = document.getElementById('positionId').value;
    const positionData = {
        positionName: document.getElementById('positionName').value,
        jobCity: document.getElementById('jobCity').value,
        gender: document.getElementById('gender').value,
        salaryRange: document.getElementById('salaryRange').value,
        job_classification_id: document.getElementById('jobClassificationId').value,
        boss_account: document.getElementById('boss_account').value,
        device_info: document.getElementById('device_info').value,
        boss_scene: document.getElementById('boss_scene').value,
        enable_boss_scene: document.getElementById('enable_boss_scene').value === '1'
    };
    
    // 处理年龄范围
    const ageMin = document.getElementById('ageMin').value;
    const ageMax = document.getElementById('ageMax').value;
    if (ageMin || ageMax) {
        positionData.age = JSON.stringify([ageMin, ageMax]);
    }
    
    // 处理教育要求
    const educationSelect = document.getElementById('educationRequirements');
    const selectedEducation = Array.from(educationSelect.selectedOptions).map(option => option.value);
    if (selectedEducation.length > 0) {
        positionData.educationRequirements = JSON.stringify(selectedEducation);
    }
    
    // 处理经验要求
    const experienceText = document.getElementById('experienceRequirements').value;
    if (experienceText) {
        const experienceList = experienceText.split('\n').map(item => item.trim()).filter(item => item !== '');
        positionData.experienceRequirements = JSON.stringify(experienceList);
    }
    
    try {
        // 显示加载状态
        const saveBtn = document.getElementById('savePositionBtn');
        const originalText = saveBtn.textContent;
        saveBtn.textContent = '保存中...';
        saveBtn.disabled = true;
        
        // 是新增还是编辑
        let result;
        if (positionId) {
            // 编辑职位
            result = await API.position.update(positionId, positionData);
            showNotification('职位更新成功', 'success');
        } else {
            // 新增职位，生成唯一ID
            positionData.dataId = generateUniqueId();
            result = await API.position.add(positionData);
            showNotification('职位添加成功', 'success');
        }
        
        // 关闭模态框
        closeAllModals();
        
        // 重新加载职位数据
        await loadPositions();
        
        // 恢复按钮状态
        saveBtn.textContent = originalText;
        saveBtn.disabled = false;
    } catch (error) {
        console.error('保存职位失败:', error);
        
        // 确保错误消息显示在UI上
        const errorMsg = error.message || '保存职位失败';
        showNotification(errorMsg, 'error');
        
        // 使用alert确保用户看到错误
        alert('错误: ' + errorMsg);
        
        // 恢复按钮状态
        const saveBtn = document.getElementById('savePositionBtn');
        saveBtn.textContent = '保存';
        saveBtn.disabled = false;
    }
}

// 确认删除职位
function confirmDeletePosition(positionId) {
    confirmAction('确定要删除此职位吗？此操作不可恢复。', async () => {
        try {
            await API.position.delete(positionId);
            showNotification('职位删除成功', 'success');
            
            // 重新加载职位数据
            await loadPositions();
        } catch (error) {
            console.error('删除职位失败:', error);
            showNotification(error.message || '删除职位失败', 'error');
        }
    });
}

// 显示职位详情
async function showPositionDetail(positionId) {
    try {
        // 获取职位数据
        const position = await API.position.getById(positionId);
        
        if (!position) {
            showNotification('获取职位详情失败', 'error');
            return;
        }
        
        // 设置模态框标题
        document.getElementById('detailModalTitle').textContent = position.positionName || '职位详情';
        
        // 解析年龄范围
        let ageRangeText = '不限';
        if (position.age) {
            try {
                // 检查是否已经是数组
                const ageRange = Array.isArray(position.age) 
                    ? position.age 
                    : JSON.parse(position.age);
                
                if (Array.isArray(ageRange) && ageRange.length === 2) {
                    ageRangeText = `${ageRange[0]}-${ageRange[1]}岁`;
                }
            } catch (e) {
                console.error('解析年龄范围失败:', e);
            }
        }
        
        // 解析教育要求
        let educationText = '不限';
        if (position.educationRequirements) {
            try {
                // 检查是否已经是数组
                const education = Array.isArray(position.educationRequirements) 
                    ? position.educationRequirements 
                    : JSON.parse(position.educationRequirements);
                
                if (Array.isArray(education) && education.length > 0) {
                    educationText = education.join(', ');
                }
            } catch (e) {
                console.error('解析教育要求失败:', e);
            }
        }
        
        // 解析岗位描述
        let experienceText = '无';
        if (position.experienceRequirements) {
            try {
                // 检查是否已经是数组
                const experience = Array.isArray(position.experienceRequirements) 
                    ? position.experienceRequirements 
                    : JSON.parse(position.experienceRequirements);
                
                if (Array.isArray(experience) && experience.length > 0) {
                    experienceText = experience.join('<br>');
                }
            } catch (e) {
                console.error('解析岗位描述失败:', e);
            }
        }
        
        // 获取职位分类名称
        let classificationName = '未分类';
        if (position.job_classification_id) {
            const classification = jobClassifications.find(c => c.id === position.job_classification_id);
            if (classification) {
                classificationName = classification.class_name || '未命名分类';
            }
        }
        
        // 构建详情HTML
        const detailsHTML = `
            <div class="detail-section" data-id="${position.dataId}">
                <h4>基本信息</h4>
                <div class="detail-grid">
                    <div class="detail-column">
                        <div class="detail-item">
                            <div class="detail-label">职位名称</div>
                            <div class="detail-value">${position.positionName || '<span class="empty">未设置</span>'}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">职位分类</div>
                            <div class="detail-value">${classificationName}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">工作城市</div>
                            <div class="detail-value">${position.jobCity || '<span class="empty">未设置</span>'}</div>
                        </div>
                    </div>
                    <div class="detail-column">
                        <div class="detail-item">
                            <div class="detail-label">薪资范围</div>
                            <div class="detail-value">${position.salaryRange || '<span class="empty">未设置</span>'}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Boss账号</div>
                            <div class="detail-value">${position.boss_account ? (typeof position.boss_account === 'object' ? JSON.stringify(position.boss_account) : position.boss_account) : '<span class="empty">未设置</span>'}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">设备信息</div>
                            <div class="detail-value">${position.device_info ? (typeof position.device_info === 'object' ? JSON.stringify(position.device_info) : position.device_info) : '<span class="empty">未设置</span>'}</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="detail-section">
                <h4>要求信息</h4>
                <div class="detail-grid">
                    <div class="detail-column">
                        <div class="detail-item">
                            <div class="detail-label">年龄要求</div>
                            <div class="detail-value">${ageRangeText}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">性别要求</div>
                            <div class="detail-value">${position.gender || '不限'}</div>
                        </div>
                    </div>
                    <div class="detail-column">
                        <div class="detail-item">
                            <div class="detail-label">学历要求</div>
                            <div class="detail-value">${educationText}</div>
                        </div>
                    </div>
                </div>
                
                <div class="detail-item full-width">
                    <div class="detail-label">岗位描述</div>
                    <div class="detail-value">${experienceText}</div>
                </div>
            </div>
            
            <div class="detail-section">
                <h4>其他信息</h4>
                <div class="detail-grid">
                    <div class="detail-column">
                        <div class="detail-item">
                            <div class="detail-label">创建时间</div>
                            <div class="detail-value">${formatDate(position.createdAt)}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">更新时间</div>
                            <div class="detail-value">${formatDate(position.updatedAt)}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">是否启用Boss场景图片</div>
                            <div class="detail-value">${position.enable_boss_scene ? '是' : '否'}</div>
                        </div>
                    </div>
                    <div class="detail-column">
                        <div class="detail-item">
                            <div class="detail-label">ID</div>
                            <div class="detail-value">${position.dataId}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Boss场景图片</div>
                            <div class="detail-value">${position.boss_scene ? (typeof position.boss_scene === 'object' ? JSON.stringify(position.boss_scene) : position.boss_scene) : '<span class="empty">未设置</span>'}</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // 设置详情内容
        const positionDetails = document.getElementById('positionDetails');
        positionDetails.innerHTML = detailsHTML;
        positionDetails.setAttribute('data-id', position.dataId);
        
        // 显示模态框
        const modal = document.getElementById('positionDetailModal');
        modal.style.display = 'block';
    } catch (error) {
        console.error('获取职位详情失败:', error);
        showNotification('获取职位详情失败', 'error');
    }
}

// 关闭所有模态框
function closeAllModals() {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.style.display = 'none';
    });
}

// 生成唯一ID
function generateUniqueId() {
    return 'pos_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
} 