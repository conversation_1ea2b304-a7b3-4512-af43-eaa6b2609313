// 仪表盘脚本 - 现代科技风格
document.addEventListener('DOMContentLoaded', function() {
    // 检查用户是否已登录
    checkAuth().then(() => {
        // 先添加粒子背景效果
        initParticleBackground();
        
    // 初始化仪表盘数据
    initDashboard();
});
});

// 检查用户是否已登录，如未登录则跳转到登录页面
async function checkAuth() {
    try {
        // 从本地存储获取token
        const token = localStorage.getItem('accessToken');
        if (!token) {
            window.location.href = '/login07082';
            return;
        }
        
        // 设置当前用户信息
        const userData = JSON.parse(localStorage.getItem('user_data') || '{}');
        if (userData && userData.account) {
            document.getElementById('currentUserName').textContent = userData.nickname || userData.account;
        }
    } catch (error) {
        console.error('认证检查失败:', error);
        window.location.href = '/login07082';
    }
}

// 获取授权请求头
function getAuthHeaders() {
    const token = localStorage.getItem('accessToken');
    return {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    };
}

// 初始化粒子背景效果
function initParticleBackground() {
    // 检查是否存在particles.js库
    if (window.particlesJS) {
        particlesJS('particles-js', {
            "particles": {
                "number": {
                    "value": 60,
                    "density": {
                        "enable": true,
                        "value_area": 800
                    }
                },
                "color": {
                    "value": "#3498db"
                },
                "shape": {
                    "type": "circle",
                    "stroke": {
                        "width": 0,
                        "color": "#000000"
                    },
                    "polygon": {
                        "nb_sides": 5
                    }
                },
                "opacity": {
                    "value": 0.1,
                    "random": false,
                    "anim": {
                        "enable": false,
                        "speed": 1,
                        "opacity_min": 0.1,
                        "sync": false
                    }
                },
                "size": {
                    "value": 3,
                    "random": true,
                    "anim": {
                        "enable": false,
                        "speed": 40,
                        "size_min": 0.1,
                        "sync": false
                    }
                },
                "line_linked": {
                    "enable": true,
                    "distance": 150,
                    "color": "#3498db",
                    "opacity": 0.1,
                    "width": 1
                },
                "move": {
                    "enable": true,
                    "speed": 1.5,
                    "direction": "none",
                    "random": false,
                    "straight": false,
                    "out_mode": "out",
                    "bounce": false,
                    "attract": {
                        "enable": false,
                        "rotateX": 600,
                        "rotateY": 1200
                    }
                }
            },
            "interactivity": {
                "detect_on": "canvas",
                "events": {
                    "onhover": {
                        "enable": true,
                        "mode": "grab"
                    },
                    "onclick": {
                        "enable": false,
                        "mode": "push"
                    },
                    "resize": true
                },
                "modes": {
                    "grab": {
                        "distance": 140,
                        "line_linked": {
                            "opacity": 0.3
                        }
                    },
                    "bubble": {
                        "distance": 400,
                        "size": 40,
                        "duration": 2,
                        "opacity": 8,
                        "speed": 3
                    },
                    "repulse": {
                        "distance": 200,
                        "duration": 0.4
                    },
                    "push": {
                        "particles_nb": 4
                    },
                    "remove": {
                        "particles_nb": 2
                    }
                }
            },
            "retina_detect": true
        });
    } else {
        console.warn("particles.js 库未找到，粒子背景将不可用");
    }
}

// 初始化仪表盘
async function initDashboard() {
    try {
        // 显示加载状态
        showLoading();
        
        // 优先加载主要统计数据，这样即使其他数据加载失败，页面也有基本内容
        await loadDashboardStats();
        
        // 构建布局容器
        createLayoutContainers();
        
        // 并行加载其他数据
        const promises = [
            loadDailyStatsCharts(),
            loadTokenUsageCharts(),
            loadRecentUsers(),
            loadRecentPositions()
        ];
        
        // 使用Promise.allSettled确保即使部分请求失败，也不影响整体显示
        await Promise.allSettled(promises);
        
        // 隐藏加载状态
        hideLoading();
        
        // 添加数据卡片的悬停效果
        addCardHoverEffects();
        
    } catch (error) {
        console.error('初始化仪表盘失败:', error);
        hideLoading();
        showErrorMessage('加载仪表盘数据失败，请刷新页面重试。');
    }
}

// 创建布局容器
function createLayoutContainers() {
    const content = document.querySelector('.content');
    
    // 创建图表行容器
    const chartRow = document.createElement('div');
    chartRow.className = 'dashboard-row';
    chartRow.id = 'chartRow';
    content.appendChild(chartRow);
    
    // 创建数据行容器
    const dataRow = document.createElement('div');
    dataRow.className = 'dashboard-row';
    dataRow.id = 'dataRow';
    content.appendChild(dataRow);
}

// 显示加载状态
function showLoading() {
    const content = document.querySelector('.content');
    
    // 创建加载器
    const loader = document.createElement('div');
    loader.className = 'dashboard-loader';
    loader.innerHTML = `
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
        </div>
        <p>加载仪表盘数据中...</p>
    `;
    
    // 添加到内容区
    document.body.appendChild(loader);
}
        
        // 隐藏加载状态
function hideLoading() {
    const loader = document.querySelector('.dashboard-loader');
    
    if (loader) {
        loader.remove();
    }
}

// 显示错误消息
function showErrorMessage(message) {
    Swal.fire({
        title: '出错了!',
        text: message,
        icon: 'error',
        confirmButtonText: '确定'
    });
}

// 加载仪表盘主要统计数据
async function loadDashboardStats() {
    try {
        const response = await fetch('/api/dashboard/stats', {
            headers: getAuthHeaders()
        });
        const result = await response.json();
        
        if (result.code === 200 && result.data) {
            const stats = result.data;
            
            // 更新统计卡片数据
            updateStatCard('positionCount', stats.position_count || 0, '职位数量');
            updateStatCard('userCount', stats.user_count || 0, '候选人数量');
            updateStatCard('sceneCount', stats.scene_count || 0, '场景数量');
            updateStatCard('virtualHRCount', stats.virtual_hr_count || 0, '虚拟HR数量');
            updateStatCard('chatCount', stats.chat_count || 0, '沟通次数');
            
            // 添加一个额外的统计卡片：平均每人沟通次数
            const avgChatsPerUser = stats.user_count > 0 
                ? (stats.chat_count / stats.user_count).toFixed(1) 
                : 0;
            updateStatCard('avgChatsPerUser', avgChatsPerUser, '平均沟通次数/人');
            
        } else {
            console.error('获取统计数据失败:', result.message);
        }
    } catch (error) {
        console.error('加载统计数据出错:', error);
        throw error;
    }
}

// 更新统计卡片
function updateStatCard(id, value, label) {
    const countElement = document.getElementById(id);
    if (countElement) {
        // 创建计数动画效果
        animateCounter(countElement, 0, value);
        
        // 更新标签
        const labelElement = countElement.nextElementSibling;
        if (labelElement && labelElement.classList.contains('stat-label')) {
            labelElement.textContent = label;
        }
    }
}

// 计数器动画效果
function animateCounter(element, start, end) {
    const duration = 1500;
    const frameDuration = 1000 / 60;
    const totalFrames = Math.round(duration / frameDuration);
    let frame = 0;
    
    // 对于小数值，保留一位小数
    const isDecimal = String(end).includes('.');
    
    const counter = setInterval(() => {
        frame++;
        
        const progress = frame / totalFrames;
        const currentValue = start + (end - start) * progress;
        
        if (isDecimal) {
            element.textContent = currentValue.toFixed(1);
        } else {
            element.textContent = Math.round(currentValue);
        }
        
        if (frame === totalFrames) {
            clearInterval(counter);
        }
    }, frameDuration);
}

// 加载每日数据变化图表
async function loadDailyStatsCharts() {
    try {
        const response = await fetch('/api/dashboard/daily-stats', {
            headers: getAuthHeaders()
        });
        const result = await response.json();
        
        if (result.code === 200 && result.data) {
            const { position_stats, user_stats } = result.data;
            
            // 创建图表容器
            const chartRow = document.getElementById('chartRow');
            if (!chartRow) return;
            
            const chartCard = document.createElement('div');
            chartCard.className = 'chart-card';
            chartCard.style.gridColumn = '1 / -1'; // 占满一行
            
            chartCard.innerHTML = `
                <div class="chart-header">
                    <h3 class="chart-title">每日数据变化趋势</h3>
                </div>
                <div class="chart-container">
                    <canvas id="dailyStatsChart"></canvas>
                </div>
            `;
            
            chartRow.appendChild(chartCard);
            
            // 获取日期和数据
            const dates = position_stats.map(item => item.date);
            const positionCounts = position_stats.map(item => item.count);
            const userCounts = user_stats.map(item => item.count);
            
            // 创建图表
            const ctx = document.getElementById('dailyStatsChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: dates,
                    datasets: [
                        {
                            label: '职位数量',
                            data: positionCounts,
                            borderColor: '#3498db',
                            backgroundColor: 'rgba(52, 152, 219, 0.1)',
                            borderWidth: 2,
                            pointRadius: 4,
                            pointBackgroundColor: '#3498db',
                            tension: 0.3,
                            fill: true
                        },
                        {
                            label: '候选人数量',
                            data: userCounts,
                            borderColor: '#2ecc71',
                            backgroundColor: 'rgba(46, 204, 113, 0.1)',
                            borderWidth: 2,
                            pointRadius: 4,
                            pointBackgroundColor: '#2ecc71',
                            tension: 0.3,
                            fill: true
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                boxWidth: 6
                            }
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                            backgroundColor: 'rgba(255, 255, 255, 0.9)',
                            titleColor: '#2c3e50',
                            bodyColor: '#2c3e50',
                            borderColor: '#eaeaea',
                            borderWidth: 1,
                            bodyFont: {
                                size: 13
                            },
                            titleFont: {
                                size: 14,
                                weight: 'bold'
                            },
                            padding: 10,
                            displayColors: true,
                            callbacks: {
                                label: function(context) {
                                    return context.dataset.label + ': ' + context.raw;
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            grid: {
                                borderDash: [2, 4],
                                color: 'rgba(0, 0, 0, 0.05)'
                            },
                            beginAtZero: true
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'nearest'
                    },
                    animation: {
                        duration: 1000,
                        easing: 'easeOutQuart'
                    }
                }
            });
        } else {
            console.error('获取每日统计数据失败:', result.message);
        }
    } catch (error) {
        console.error('加载每日统计图表出错:', error);
    }
}

// 加载Token使用情况图表
async function loadTokenUsageCharts() {
    try {
        const response = await fetch('/api/dashboard/token-usage', {
            headers: getAuthHeaders()
        });
        const result = await response.json();
        
        if (result.code === 200 && result.data) {
            const { daily_usage, apikey_usage, tenant_usage } = result.data;
            const chartRow = document.getElementById('chartRow');
            if (!chartRow) return;
            
            // 创建Token使用量卡片
            const tokenCard = document.createElement('div');
            tokenCard.className = 'token-usage-card';
            
            tokenCard.innerHTML = `
                <div class="token-usage-header">
                    <h3 class="token-usage-title">Token 使用情况 (7天)</h3>
                </div>
                <div id="tokenUsageChartContainer" class="token-usage-chart">
                    <canvas id="tokenUsageChart"></canvas>
                </div>
                <div class="token-usage-info">
                    <div class="token-usage-stat">
                        <div id="totalTokens" class="token-usage-value">0</div>
                        <div class="token-usage-label">总Token数</div>
                    </div>
                    <div class="token-usage-stat">
                        <div id="upTokens" class="token-usage-value">0</div>
                        <div class="token-usage-label">上行Token</div>
                    </div>
                    <div class="token-usage-stat">
                        <div id="downTokens" class="token-usage-value">0</div>
                        <div class="token-usage-label">下行Token</div>
                    </div>
                </div>
            `;
            
            chartRow.appendChild(tokenCard);
            
            if (daily_usage && daily_usage.length > 0) {
                // 获取日期和数据
                const dates = daily_usage.map(item => item.date);
                const upTokens = daily_usage.map(item => item.uptoken);
                const downTokens = daily_usage.map(item => item.downtoken);
                
                // 创建每日Token使用图表
                const ctx = document.getElementById('tokenUsageChart').getContext('2d');
                new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: dates,
                        datasets: [
                            {
                                label: '上行Token',
                                data: upTokens,
                                backgroundColor: 'rgba(52, 152, 219, 0.7)',
                                borderColor: '#3498db',
                                borderWidth: 1
                            },
                            {
                                label: '下行Token',
                                data: downTokens,
                                backgroundColor: 'rgba(155, 89, 182, 0.7)',
                                borderColor: '#9b59b6',
                                borderWidth: 1
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'top',
                                labels: {
                                    usePointStyle: true,
                                    boxWidth: 6
                                }
                            }
                        },
                        scales: {
                            x: {
                                grid: {
                                    display: false
                                }
                            },
                            y: {
                                grid: {
                                    borderDash: [2, 4],
                                    color: 'rgba(0, 0, 0, 0.05)'
                                },
                                beginAtZero: true
                            }
                        },
                        animation: {
                            duration: 1000,
                            easing: 'easeOutQuart'
                        }
                    }
                });
                
                // 计算总量并更新统计信息
                const totalUpTokens = upTokens.reduce((a, b) => a + b, 0);
                const totalDownTokens = downTokens.reduce((a, b) => a + b, 0);
                const totalTokens = totalUpTokens + totalDownTokens;
                
                document.getElementById('totalTokens').textContent = totalTokens.toLocaleString();
                document.getElementById('upTokens').textContent = totalUpTokens.toLocaleString();
                document.getElementById('downTokens').textContent = totalDownTokens.toLocaleString();
            } else {
                document.getElementById('tokenUsageChartContainer').innerHTML = 
                    '<p class="text-center py-5">暂无Token使用数据</p>';
            }
            
            // 检查是否为管理员用户（有tenant_usage数据）
            if (tenant_usage && tenant_usage.length > 0) {
                // 管理员用户：显示租户使用情况
                const tenantCard = document.createElement('div');
                tenantCard.className = 'token-usage-card';
                tenantCard.innerHTML = `
                    <div class="token-usage-header">
                        <h3 class="token-usage-title">各租户Token使用量 (Top 10)</h3>
                    </div>
                    <div class="token-usage-chart" style="height: 300px;">
                        <canvas id="tenantUsageChart"></canvas>
                    </div>
                `;
                
                chartRow.appendChild(tenantCard);
                
                const tenantNames = tenant_usage.map(item => `${item.tenant_name}:${item.tenant_account}`);
                const tenantUsages = tenant_usage.map(item => item.total);
                
                const ctx = document.getElementById('tenantUsageChart').getContext('2d');
                new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: tenantNames,
                        datasets: [
                            {
                                data: tenantUsages,
                                backgroundColor: [
                                    'rgba(52, 152, 219, 0.8)',
                                    'rgba(46, 204, 113, 0.8)',
                                    'rgba(155, 89, 182, 0.8)',
                                    'rgba(243, 156, 18, 0.8)',
                                    'rgba(26, 188, 156, 0.8)',
                                    'rgba(231, 76, 60, 0.8)',
                                    'rgba(241, 196, 15, 0.8)',
                                    'rgba(230, 126, 34, 0.8)',
                                    'rgba(149, 165, 166, 0.8)',
                                    'rgba(52, 73, 94, 0.8)'
                                ],
                                borderColor: '#ffffff',
                                borderWidth: 2
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'right',
                                labels: {
                                    usePointStyle: true,
                                    boxWidth: 6
                                }
                            }
                        },
                        animation: {
                            animateRotate: true,
                            animateScale: true,
                            duration: 1200,
                            easing: 'easeOutCirc'
                        }
                    }
                });
            } else if (apikey_usage && apikey_usage.length > 0) {
                // 租户用户：显示APIKey使用情况
                const apiKeyCard = document.createElement('div');
                apiKeyCard.className = 'token-usage-card';
                apiKeyCard.innerHTML = `
                    <div class="token-usage-header">
                        <h3 class="token-usage-title">各APIKey使用量(7天)</h3>
                    </div>
                    <div class="token-usage-chart" style="height: 300px;">
                        <canvas id="apiKeyUsageChart"></canvas>
                    </div>
                `;
                
                chartRow.appendChild(apiKeyCard);
                
                const keyNames = apikey_usage.map(item => item.name);
                const keyUsages = apikey_usage.map(item => item.total_usage);
                
                const ctx = document.getElementById('apiKeyUsageChart').getContext('2d');
                new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: keyNames,
                        datasets: [
                            {
                                data: keyUsages,
                                backgroundColor: [
                                    'rgba(52, 152, 219, 0.8)',
                                    'rgba(46, 204, 113, 0.8)',
                                    'rgba(155, 89, 182, 0.8)',
                                    'rgba(243, 156, 18, 0.8)',
                                    'rgba(26, 188, 156, 0.8)',
                                    'rgba(231, 76, 60, 0.8)'
                                ],
                                borderColor: '#ffffff',
                                borderWidth: 2
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'right',
                                labels: {
                                    usePointStyle: true,
                                    boxWidth: 6
                                }
                            }
                        },
                        animation: {
                            animateRotate: true,
                            animateScale: true,
                            duration: 1200,
                            easing: 'easeOutCirc'
                        }
                    }
                });
            }
        } else {
            console.error('获取Token使用情况失败:', result.message);
        }
    } catch (error) {
        console.error('加载Token使用情况图表出错:', error);
    }
}

// 加载最近新增的候选人
async function loadRecentUsers() {
    try {
        const response = await fetch('/api/dashboard/recent-users', {
            headers: getAuthHeaders()
        });
        const result = await response.json();
        
        if (result.code === 200 && result.data) {
            const { users, count } = result.data;
            const dataRow = document.getElementById('dataRow');
            if (!dataRow) return;
            
            // 创建数据卡片
            const dataCard = document.createElement('div');
            dataCard.className = 'data-card';
            dataCard.id = 'recentUsersCard';
            
            // 创建表头 - 移除操作列，添加租户列
            const columns = ['姓名', '应聘职位', '评分', '添加时间', '所属租户'];
            let tableHeaders = '';
            columns.forEach(col => {
                tableHeaders += `<th>${col}</th>`;
            });
            
            dataCard.innerHTML = `
                <div class="data-card-header">
                    <h3 class="data-card-title">
                        <i class="fas fa-user-tie"></i>
                        最近新增候选人
                    </h3>
                    <span class="data-card-badge">${count} 个</span>
                </div>
                <div class="data-card-body">
                    <table class="data-table">
                        <thead>
                            <tr>
                                ${tableHeaders}
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="${columns.length}" class="text-center py-3">
                                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    <span class="ms-2">加载数据中...</span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            `;
            
            dataRow.appendChild(dataCard);
            
            // 设置表格容器的固定高度和滚动效果
            const tableContainer = dataCard.querySelector('.data-card-body');
            tableContainer.style.height = '400px'; // 约10行的高度
            tableContainer.style.overflowY = 'auto';
            tableContainer.style.position = 'relative';
            
            const tbody = document.querySelector('#recentUsersCard .data-table tbody');
            
            if (users && users.length > 0) {
                // 清空表格
                tbody.innerHTML = '';
                
                // 添加候选人数据
                users.forEach(user => {
                    const tr = document.createElement('tr');
                    
                    // 格式化时间
                    const createdAt = user.createdAt ? new Date(user.createdAt) : new Date();
                    const formattedDate = `${createdAt.getFullYear()}-${String(createdAt.getMonth() + 1).padStart(2, '0')}-${String(createdAt.getDate()).padStart(2, '0')}`;
                    
                    // 获取租户信息
                    const tenantInfo = user.tenantNickname && user.tenantAccount ? 
                        `${user.tenantNickname}:${user.tenantAccount}` : 
                        '未知租户';
                    
                    tr.innerHTML = `
                        <td>${user.name || user.real_name || '未知'}</td>
                        <td>${user.positionName || '未知职位'}</td>
                        <td>
                            ${user.score !== undefined && user.score !== null 
                                ? `<span class="badge ${getScoreBadgeClass(user.score)}">${user.score}</span>` 
                                : '<span class="badge badge-info">未评分</span>'}
                        </td>
                        <td>${formattedDate}</td>
                        <td>${tenantInfo}</td>
                    `;
                    
                    tbody.appendChild(tr);
                });
                
                // 添加滚动动画效果
                initScrollAnimation(tableContainer);
                
            } else {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="${columns.length}" class="text-center py-3">暂无新增候选人数据</td>
                    </tr>
                `;
            }
        } else {
            console.error('获取最近候选人数据失败:', result.message);
        }
    } catch (error) {
        console.error('加载最近候选人出错:', error);
    }
}

// 加载最近新增的职位
async function loadRecentPositions() {
    try {
        const response = await fetch('/api/dashboard/recent-positions', {
            headers: getAuthHeaders()
        });
        const result = await response.json();
        
        if (result.code === 200 && result.data) {
            const { positions, count } = result.data;
            const dataRow = document.getElementById('dataRow');
            if (!dataRow) return;
            
            // 创建数据卡片
            const dataCard = document.createElement('div');
            dataCard.className = 'data-card';
            dataCard.id = 'recentPositionsCard';
            
            // 创建表头 - 移除操作列，添加租户列
            const columns = ['职位名称', '工作城市', '所属分类', '创建时间', '所属租户'];
            let tableHeaders = '';
            columns.forEach(col => {
                tableHeaders += `<th>${col}</th>`;
            });
            
            dataCard.innerHTML = `
                <div class="data-card-header">
                    <h3 class="data-card-title">
                        <i class="fas fa-briefcase"></i>
                        最近新增职位
                    </h3>
                    <span class="data-card-badge">${count} 个</span>
                </div>
                <div class="data-card-body">
                    <table class="data-table">
                        <thead>
                            <tr>
                                ${tableHeaders}
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="${columns.length}" class="text-center py-3">
                                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    <span class="ms-2">加载数据中...</span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            `;
            
            dataRow.appendChild(dataCard);
            
            // 设置表格容器的固定高度和滚动效果
            const tableContainer = dataCard.querySelector('.data-card-body');
            tableContainer.style.height = '400px'; // 约10行的高度
            tableContainer.style.overflowY = 'auto';
            tableContainer.style.position = 'relative';
            
            const tbody = document.querySelector('#recentPositionsCard .data-table tbody');
            
            if (positions && positions.length > 0) {
                // 清空表格
                tbody.innerHTML = '';
                
                // 添加职位数据
                positions.forEach(position => {
                    const tr = document.createElement('tr');
                    
                    // 格式化时间
                    const createdAt = position.createdAt ? new Date(position.createdAt) : new Date();
                    const formattedDate = `${createdAt.getFullYear()}-${String(createdAt.getMonth() + 1).padStart(2, '0')}-${String(createdAt.getDate()).padStart(2, '0')}`;
                    
                    // 获取租户信息
                    const tenantInfo = position.tenantNickname && position.tenantAccount ? 
                        `${position.tenantNickname}:${position.tenantAccount}` : 
                        '未知租户';
                    
                    // 使用分类名称而不是ID
                    const classificationName = position.classificationName || '未分类';
                    
                    tr.innerHTML = `
                        <td>${position.positionName || '未命名职位'}</td>
                        <td>${position.jobCity || '未指定'}</td>
                        <td>${classificationName}</td>
                        <td>${formattedDate}</td>
                        <td>${tenantInfo}</td>
                    `;
                    
                    tbody.appendChild(tr);
                });
                
                // 添加滚动动画效果
                initScrollAnimation(tableContainer);
                
            } else {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="${columns.length}" class="text-center py-3">暂无新增职位数据</td>
                    </tr>
                `;
            }
        } else {
            console.error('获取最近职位数据失败:', result.message);
        }
    } catch (error) {
        console.error('加载最近职位出错:', error);
    }
}

// 初始化表格滚动动画
function initScrollAnimation(container) {
    if (!container) return;
    
    const table = container.querySelector('table');
    const tbody = container.querySelector('tbody');
    
    if (table && tbody) {
        // 计算表格行数
        const rowCount = tbody.querySelectorAll('tr').length;
        
        // 确保表头不透明，添加背景色
        const thead = table.querySelector('thead');
        if (thead) {
            // 设置thead的背景色 - 使用不透明的纯色
            thead.style.cssText = 'background-color: #f0f7fc !important; position: sticky; top: 0; z-index: 10;';
            
            // 设置thead中tr的背景色
            const headerRow = thead.querySelector('tr');
            if (headerRow) {
                headerRow.style.cssText = 'background-color: #f0f7fc !important;';
            }
            
            // 设置所有th的样式
            const headers = thead.querySelectorAll('th');
            headers.forEach(header => {
                header.style.cssText = 'background-color: #f0f7fc !important; position: sticky; top: 0; z-index: 10;';
            });
        }
        
        // 添加一个额外的样式元素，确保表头样式不被覆盖
        const dataCardId = container.closest('.data-card').id;
        const style = document.createElement('style');
        style.textContent = `
            #${dataCardId} .data-table thead,
            #${dataCardId} .data-table thead tr,
            #${dataCardId} .data-table th {
                background-color: #f0f7fc !important;
                opacity: 1 !important;
                -webkit-backdrop-filter: none !important;
                backdrop-filter: none !important;
            }
        `;
        document.head.appendChild(style);
        
        // 如果行数超过10行，启用滚动动画
        if (rowCount > 10) {
            // 克隆前5行并附加到表格末尾，以实现无缝滚动
            const rows = tbody.querySelectorAll('tr');
            const cloneRows = Array.from(rows).slice(0, 5).map(row => row.cloneNode(true));
            
            cloneRows.forEach(row => {
                tbody.appendChild(row);
            });
            
            // 设置动画
            let currentPosition = 0;
            const rowHeight = rows[0].offsetHeight;
            const totalScrollHeight = rowHeight * rowCount;
            
            // 设置滚动动画
            let scrollInterval = setInterval(() => {
                currentPosition += 1;
                container.scrollTop = currentPosition;
                
                // 确保在滚动过程中表头保持不透明
                if (thead) {
                    thead.style.cssText = 'background-color: #f0f7fc !important; position: sticky; top: 0; z-index: 10;';
                }
                
                // 当滚动到最后一个克隆行之前，重置位置
                if (currentPosition >= (rowCount - 5) * rowHeight) {
                    currentPosition = 0;
                    container.scrollTop = 0;
                }
            }, 80); // 控制滚动速度
            
            // 鼠标悬停时暂停动画
            container.addEventListener('mouseenter', () => {
                clearInterval(scrollInterval);
            });
            
            // 鼠标离开时恢复动画
            container.addEventListener('mouseleave', () => {
                clearInterval(scrollInterval);
                scrollInterval = setInterval(() => {
                    currentPosition += 1;
                    container.scrollTop = currentPosition;
                    
                    // 确保在滚动过程中表头保持不透明
                    if (thead) {
                        thead.style.cssText = 'background-color: #f0f7fc !important; position: sticky; top: 0; z-index: 10;';
                    }
                    
                    if (currentPosition >= (rowCount - 5) * rowHeight) {
                        currentPosition = 0;
                        container.scrollTop = 0;
                    }
                }, 80);
            });
        }
    }
}

// 根据评分获取对应的徽章样式类
function getScoreBadgeClass(score) {
    if (score >= 80) return 'badge-success';
    if (score >= 60) return 'badge-info';
    if (score >= 40) return 'badge-warning';
    return 'badge-danger';
}

// 添加卡片悬停效果
function addCardHoverEffects() {
    // 添加卡片悬停效果
    const cards = document.querySelectorAll('.chart-card, .token-usage-card, .data-card');
    
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 15px 30px rgba(0, 0, 0, 0.1)';
            this.style.transition = 'all 0.3s ease';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 8px 20px rgba(0, 0, 0, 0.08)';
        });
    });
} 