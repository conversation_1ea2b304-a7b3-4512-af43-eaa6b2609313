/**
 * 流程图管理器
 * 基于X6实现场景流程图的可视化编辑
 */
class FlowchartManager {
    constructor(containerId, options = {}) {
        // 检查X6是否已加载
        if (typeof X6 === 'undefined') {
            throw new Error('X6库未加载，请确保X6库已正确引入');
        }

        // 检查插件是否已加载
        if (typeof X6PluginSelection === 'undefined') {
            console.warn('Selection插件未加载，框选功能将不可用');
        }

        if (typeof X6PluginScroller === 'undefined') {
            console.warn('Scroller插件未加载，滚动功能将不可用');
        }
        this.containerId = containerId;
        this.options = {
            width: '100%',
            height: '100%',
            autoResize: true,
            background: {
                color: '#fafafa',
            },
            grid: {
                size: 20,
                visible: true,
                type: 'dot',
                args: {
                    color: '#e0e0e0',
                    thickness: 1,
                },
            },
            mousewheel: {
                enabled: true,
                zoomAtMousePosition: true,
                modifiers: 'ctrl',
                minScale: 0.5,
                maxScale: 3,
            },
            connecting: {
                router: 'manhattan',
                connector: {
                    name: 'rounded',
                    args: {
                        radius: 8,
                    },
                },
                anchor: 'center',
                connectionPoint: 'boundary',
                allowBlank: false,
                snap: {
                    radius: 20,
                },
                createEdge() {
                    return new X6.Shape.Edge({
                        attrs: {
                            line: {
                                stroke: '#666',
                                strokeWidth: 2,
                                targetMarker: {
                                    name: 'block',
                                    width: 12,
                                    height: 8,
                                },
                            },
                        },
                        zIndex: 0,
                    });
                },
                validateConnection({ targetMagnet }) {
                    return !!targetMagnet;
                },
            },
            highlighting: {
                magnetAdsorbed: {
                    name: 'stroke',
                    args: {
                        attrs: {
                            fill: '#5F95FF',
                            stroke: '#5F95FF',
                        },
                    },
                },
            },
            resizing: true,
            rotating: false,
            snapline: true,
            keyboard: true,
            clipboard: true,
            panning: false, // 禁用内置的拖拽，使用Scroller插件
            ...options
        };
        
        this.graph = null;
        this.scenes = [];
        this.switchers = [];
        this.currentJobClassificationId = null;
        
        this.init();
    }
    
    init() {
        // 初始化X6图形
        this.graph = new X6.Graph({
            container: document.getElementById(this.containerId),
            ...this.options
        });

        // 初始化插件
        this.initPlugins();

        // 注册自定义节点
        this.registerCustomNodes();

        // 绑定事件
        this.bindEvents();

        // 绑定位置保存事件
        this.bindPositionSaveEvents();

        console.log('FlowchartManager initialized');
    }

    initPlugins() {
        // 初始化Selection插件 - 框选功能
        if (typeof X6PluginSelection !== 'undefined') {
            this.graph.use(new X6PluginSelection.Selection({
                enabled: true,
                multiple: true,
                rubberband: true,
                movable: true,
                showNodeSelectionBox: true,
                showEdgeSelectionBox: false,
                strict: false,
                modifiers: ['ctrl', 'meta'], // 按住Ctrl或Cmd键才能框选
            }));
            console.log('Selection插件初始化成功');
        }

        // 初始化Scroller插件 - 滚动和拖拽功能
        if (typeof X6PluginScroller !== 'undefined') {
            this.graph.use(new X6PluginScroller.Scroller({
                enabled: true,
                pannable: true, // 启用画布拖拽
                pageVisible: false,
                pageBreak: false,
                autoResize: true,
                modifiers: null, // 不需要修饰键，直接拖拽
            }));
            console.log('Scroller插件初始化成功');
        }
    }

    registerCustomNodes() {
        // 注册场景节点
        X6.Graph.registerNode('scene-node', {
            inherit: 'rect',
            width: 140,
            height: 90,
            attrs: {
                body: {
                    strokeWidth: 2,
                    stroke: '#e0e0e0',
                    fill: '#ffffff',
                    rx: 8,
                    ry: 8,
                },
                text: {
                    fontSize: 12,
                    fill: '#333333',
                    textAnchor: 'middle',
                    textVerticalAnchor: 'middle',
                },
            },
            ports: {
                groups: {
                    top: {
                        position: 'top',
                        attrs: {
                            circle: {
                                r: 8,
                                magnet: true,
                                stroke: '#5F95FF',
                                strokeWidth: 2,
                                fill: '#fff',
                                style: {
                                    visibility: 'hidden',
                                },
                            },
                        },
                        maxConnections: -1, // 允许无限连接
                    },
                    right: {
                        position: 'right',
                        attrs: {
                            circle: {
                                r: 8,
                                magnet: true,
                                stroke: '#5F95FF',
                                strokeWidth: 2,
                                fill: '#fff',
                                style: {
                                    visibility: 'hidden',
                                },
                            },
                        },
                        maxConnections: -1, // 允许无限连接
                    },
                    bottom: {
                        position: 'bottom',
                        attrs: {
                            circle: {
                                r: 8,
                                magnet: true,
                                stroke: '#5F95FF',
                                strokeWidth: 2,
                                fill: '#fff',
                                style: {
                                    visibility: 'hidden',
                                },
                            },
                        },
                        maxConnections: -1, // 允许无限连接
                    },
                    left: {
                        position: 'left',
                        attrs: {
                            circle: {
                                r: 8,
                                magnet: true,
                                stroke: '#5F95FF',
                                strokeWidth: 2,
                                fill: '#fff',
                                style: {
                                    visibility: 'hidden',
                                },
                            },
                        },
                        maxConnections: -1, // 允许无限连接
                    },
                },
                items: [
                    { group: 'top' },
                    { group: 'right' },
                    { group: 'bottom' },
                    { group: 'left' },
                ],
            },
        });
    }
    
    bindEvents() {
        // 节点连接完成事件
        this.graph.on('edge:connected', ({ edge }) => {
            this.onEdgeConnected(edge);
        });
        
        // 节点选择事件
        this.graph.on('node:selected', ({ node }) => {
            this.onNodeSelected(node);
        });
        
        // 边选择事件
        this.graph.on('edge:selected', ({ edge }) => {
            this.onEdgeSelected(edge);
        });
        
        // 节点鼠标悬停事件
        this.graph.on('node:mouseenter', ({ node }) => {
            this.showNodePorts(node);
        });
        
        this.graph.on('node:mouseleave', ({ node }) => {
            this.hideNodePorts(node);
        });
        
        // 右键菜单事件
        this.graph.on('node:contextmenu', ({ e, node }) => {
            this.showContextMenu(e, 'node', node);
        });
        
        this.graph.on('edge:contextmenu', ({ e, edge }) => {
            this.showContextMenu(e, 'edge', edge);
        });
        
        // 画布右键菜单
        this.graph.on('blank:contextmenu', ({ e }) => {
            this.showContextMenu(e, 'canvas');
        });
    }
    
    showNodePorts(node) {
        const ports = node.getPorts();
        ports.forEach((port) => {
            node.setPortProp(port.id, 'attrs/circle/style/visibility', 'visible');
        });
    }
    
    hideNodePorts(node) {
        const ports = node.getPorts();
        ports.forEach((port) => {
            node.setPortProp(port.id, 'attrs/circle/style/visibility', 'hidden');
        });
    }
    
    onEdgeConnected(edge) {
        const sourceNode = edge.getSourceNode();
        const targetNode = edge.getTargetNode();

        console.log('Edge connected:', sourceNode, targetNode);

        if (sourceNode && targetNode) {
            // 从节点数据中获取场景信息
            const sourceSceneData = sourceNode.getData();
            const targetSceneData = targetNode.getData();

            console.log('Source scene data from node:', sourceSceneData);
            console.log('Target scene data from node:', targetSceneData);

            // 从scenes数组中获取完整的场景信息
            const sourceScene = this.scenes.find(s => s.sceneId === sourceSceneData.sceneId);
            const targetScene = this.scenes.find(s => s.sceneId === targetSceneData.sceneId);

            if (sourceScene && targetScene) {
                console.log('Found complete scene data:', sourceScene, targetScene);
                // 显示场景切换器设置弹窗
                this.showSwitcherModal(edge, sourceScene, targetScene);
            } else {
                console.error('Could not find complete scene data');
            }
        }
    }
    
    onNodeSelected(node) {
        console.log('Node selected:', node.id);
    }
    
    onEdgeSelected(edge) {
        console.log('Edge selected:', edge.id);
    }
    
    showContextMenu(e, type, element) {
        e.preventDefault();

        // 移除现有的右键菜单
        const existingMenu = document.querySelector('.context-menu');
        if (existingMenu) {
            existingMenu.remove();
        }

        // 创建右键菜单
        const menu = document.createElement('div');
        menu.className = 'context-menu';
        menu.style.left = e.clientX + 'px';
        menu.style.top = e.clientY + 'px';

        let menuItems = [];

        if (type === 'node') {
            const sceneData = element.getData();
            menuItems = [
                { text: '编辑场景', action: () => this.editSceneNode(sceneData) },
                { text: '删除节点', action: () => this.deleteSceneNode(element), className: 'danger' }
            ];
        } else if (type === 'edge') {
            const switcherData = element.getData();
            menuItems = [
                { text: '编辑切换器', action: () => this.editSwitcher(switcherData) },
                { text: '删除连线', action: () => this.deleteEdge(element), className: 'danger' }
            ];
        } else if (type === 'canvas') {
            menuItems = [
                { text: '适应画布', action: () => this.fitToContent() },
                { text: '重置缩放', action: () => this.resetZoom() },
                { text: '刷新数据', action: () => this.loadScenes(this.currentJobClassificationId) }
            ];
        }

        menuItems.forEach((item, index) => {
            if (index > 0) {
                const divider = document.createElement('div');
                divider.className = 'context-menu-divider';
                menu.appendChild(divider);
            }

            const menuItem = document.createElement('div');
            menuItem.className = `context-menu-item ${item.className || ''}`;
            menuItem.textContent = item.text;
            menuItem.addEventListener('click', () => {
                item.action();
                menu.remove();
            });
            menu.appendChild(menuItem);
        });

        // 确保菜单添加到正确的容器中，特别是在全屏模式下
        const fullscreenPanel = document.querySelector('.scenes-panel.fullscreen');
        const targetContainer = fullscreenPanel || document.body;
        targetContainer.appendChild(menu);

        // 点击其他地方关闭菜单
        const closeMenu = (e) => {
            if (!menu.contains(e.target)) {
                menu.remove();
                document.removeEventListener('click', closeMenu);
            }
        };
        setTimeout(() => document.addEventListener('click', closeMenu), 0);
    }

    bindPositionSaveEvents() {
        // 节点位置改变时保存
        this.graph.on('node:moved', ({ node }) => {
            this.saveNodePosition(node);
        });

        // 边的路径改变时保存
        this.graph.on('edge:moved', ({ edge }) => {
            this.saveEdgePosition(edge);
        });
    }

    async saveNodePosition(node) {
        const position = node.getPosition();
        const sceneData = node.getData();
        if (sceneData && sceneData.sceneId && this.currentJobClassificationId) {
            try {
                const token = localStorage.getItem('accessToken');
                await fetch('/flowchart_position', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        job_classification_id: this.currentJobClassificationId,
                        element_type: 'node',
                        element_id: sceneData.sceneId,
                        position_data: position
                    })
                });
            } catch (error) {
                console.error('保存节点位置失败:', error);
            }
        }
    }

    async saveEdgePosition(edge) {
        const vertices = edge.getVertices();
        const edgeData = edge.getData();
        if (edgeData && edgeData.switcherId && this.currentJobClassificationId) {
            try {
                const token = localStorage.getItem('accessToken');
                await fetch('/flowchart_position', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        job_classification_id: this.currentJobClassificationId,
                        element_type: 'edge',
                        element_id: edgeData.switcherId,
                        position_data: { vertices }
                    })
                });
            } catch (error) {
                console.error('保存边位置失败:', error);
            }
        }
    }

    loadNodePosition(node) {
        const sceneData = node.getData();
        if (sceneData && sceneData.sceneId && this.savedPositions) {
            const key = `node_${sceneData.sceneId}`;
            const savedPosition = this.savedPositions[key];
            if (savedPosition) {
                try {
                    node.setPosition(savedPosition.x, savedPosition.y);
                    return true;
                } catch (e) {
                    console.error('Failed to apply saved position:', e);
                }
            }
        }
        return false;
    }

    loadEdgePosition(edge) {
        const edgeData = edge.getData();
        if (edgeData && edgeData.switcherId && this.savedPositions) {
            const key = `edge_${edgeData.switcherId}`;
            const savedData = this.savedPositions[key];
            if (savedData && savedData.vertices) {
                try {
                    edge.setVertices(savedData.vertices);
                    return true;
                } catch (e) {
                    console.error('Failed to apply saved vertices:', e);
                }
            }
        }
        return false;
    }

    editSceneNode(sceneData) {
        // 触发编辑场景事件
        const scene = this.scenes.find(s => s.sceneId === sceneData.sceneId);
        if (scene) {
            const event = new CustomEvent('editScene', { detail: scene });
            document.dispatchEvent(event);
        }
    }

    viewSceneQuestions(sceneData) {
        // 触发查看问题事件
        const event = new CustomEvent('viewSceneQuestions', { detail: sceneData });
        document.dispatchEvent(event);
    }

    deleteSceneNode(node) {
        if (confirm('确定要删除这个场景节点吗？这将同时删除相关的场景数据。')) {
            const sceneData = node.getData();
            const event = new CustomEvent('deleteScene', { detail: sceneData.sceneId });
            document.dispatchEvent(event);
        }
    }

    editSwitcher(switcherData) {
        // 获取源场景和目标场景信息
        const sourceScene = this.scenes.find(s => s.sceneId === switcherData.sourceSceneId);
        const targetSceneName = switcherData.targetConfig?.dist_scene;
        const targetScene = this.scenes.find(s => s.sceneName === targetSceneName);

        if (sourceScene && targetScene) {
            // 填充编辑表单
            document.getElementById('sourceSceneName').value = sourceScene.sceneName;
            document.getElementById('targetSceneName').value = targetScene.sceneName;

            // 填充分数条件
            if (switcherData.scoreCondition) {
                document.getElementById('scoreSymbol').value = switcherData.scoreCondition.symbol || '';
                document.getElementById('scoreValue').value = switcherData.scoreCondition.score || '';
            } else {
                document.getElementById('scoreSymbol').value = '';
                document.getElementById('scoreValue').value = '';
            }

            // 填充语义条件
            document.getElementById('semanticCondition').value = switcherData.semanticCondition || '';

            // 填充目标分数和激活话术
            document.getElementById('targetScore').value = switcherData.targetConfig?.score || 0;
            document.getElementById('userMessage').value = switcherData.targetConfig?.user_message || '';

            // 保存编辑状态
            currentSwitcherData = {
                sourceScene,
                targetScene,
                isEdit: true,
                switcherId: switcherData.switcherId,
                onConfirm: async (newSwitcherData) => {
                    await this.updateSwitcherEdge(switcherData.switcherId, newSwitcherData);
                }
            };

            // 显示弹窗
            showModal('switcherModal');
        }
    }

    async updateSwitcherEdge(switcherId, switcherData) {
        try {
            const data = {
                score_condition: switcherData.symbol && switcherData.score ? {
                    symbol: switcherData.symbol,
                    score: switcherData.score
                } : null,
                semantic_condition: switcherData.semantic_condition || null,
                target_config: {
                    dist_scene: currentSwitcherData.targetScene.sceneName,
                    score: switcherData.target_score || 0,
                    user_message: switcherData.user_message || ''
                }
            };

            const token = localStorage.getItem('accessToken');
            const response = await fetch(`/scene_switcher/${switcherId}`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });
            const result = await response.json();

            if (result.code === 200) {
                // 重新加载数据
                this.loadScenes(this.currentJobClassificationId);
            } else {
                throw new Error(result.message || '更新失败');
            }
        } catch (error) {
            console.error('更新场景切换器失败:', error);
            throw error;
        }
    }

    async deleteEdge(edge) {
        if (confirm('确定要删除这个场景切换器吗？')) {
            try {
                const switcherData = edge.getData();
                if (switcherData.switcherId) {
                    const token = localStorage.getItem('accessToken');
                    const response = await fetch(`/scene_switcher/${switcherData.switcherId}`, {
                        method: 'DELETE',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });
                    const result = await response.json();

                    if (result.code === 200) {
                        this.graph.removeCell(edge);
                        console.log('场景切换器删除成功');
                    } else {
                        throw new Error(result.message || '删除失败');
                    }
                } else {
                    // 如果没有switcherId，直接删除边
                    this.graph.removeCell(edge);
                }
            } catch (error) {
                console.error('删除场景切换器失败:', error);
                alert('删除失败: ' + error.message);
            }
        }
    }
    
    // 加载场景数据
    async loadScenes(jobClassificationId) {
        try {
            this.currentJobClassificationId = jobClassificationId;

            const token = localStorage.getItem('accessToken');

            // 加载位置信息
            const positionResponse = await fetch(`/flowchart_positions/${jobClassificationId}`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            const positionResult = await positionResponse.json();
            this.savedPositions = positionResult.code === 200 ? positionResult.data : {};

            // 获取场景数据
            const scenesResponse = await fetch(`/scenes/job_classification/${jobClassificationId}`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            this.scenes = await scenesResponse.json();

            // 获取切换器数据
            const switchersResponse = await fetch(`/scene_switchers/job_classification/${jobClassificationId}`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            this.switchers = await switchersResponse.json();

            // 渲染流程图
            this.renderFlowchart();

        } catch (error) {
            console.error('加载场景数据失败:', error);
        }
    }
    
    renderFlowchart() {
        // 清空画布
        this.graph.clearCells();
        
        // 创建场景节点
        this.createSceneNodes();
        
        // 创建连线
        this.createEdges();
        
        // 自动布局
        this.autoLayout();
    }
    
    createSceneNodes() {
        this.scenes.forEach((scene, index) => {
            const node = this.createSceneNode(scene, index);
            this.graph.addNode(node);
        });
    }
    
    createSceneNode(scene, index) {
        const isDefault = scene.isDefault === 1;

        // 默认位置
        const defaultX = 100 + (index % 3) * 220;
        const defaultY = 100 + Math.floor(index / 3) * 150;

        // 格式化分数范围
        const scoreRange = scene.scoreRange ?
            `${scene.scoreRange[0]}-${scene.scoreRange[1]}分` :
            '未设置分数';

        // 组合显示文本
        const displayText = `${scene.sceneName}\n${scoreRange}`;

        const node = this.graph.createNode({
            shape: 'scene-node',
            x: defaultX,
            y: defaultY,
            data: {
                sceneId: scene.sceneId,
                sceneName: scene.sceneName,
                sceneDescription: scene.sceneDescription,
                isDefault: isDefault,
                scoreRange: scene.scoreRange,
            },
            attrs: {
                body: {
                    stroke: isDefault ? '#4caf50' : '#e0e0e0',
                    fill: isDefault ? '#e8f5e8' : '#ffffff',
                },
                text: {
                    text: displayText,
                    fontSize: 12,
                    fill: '#333333',
                    textAnchor: 'middle',
                    textVerticalAnchor: 'middle',
                    lineHeight: 16,
                },
            },
        });

        // 尝试加载保存的位置
        setTimeout(() => {
            this.loadNodePosition(node);
        }, 100);

        return node;
    }
    
    createEdges() {
        this.switchers.forEach(switcher => {
            const sourceNode = this.getNodeBySceneId(switcher.scene_id);
            const targetScene = this.getSceneByName(switcher.target_config.dist_scene);
            const targetNode = targetScene ? this.getNodeBySceneId(targetScene.sceneId) : null;
            
            if (sourceNode && targetNode) {
                const edge = this.createEdge(sourceNode, targetNode, switcher);
                this.graph.addEdge(edge);
            }
        });
    }
    
    createEdge(sourceNode, targetNode, switcher) {
        const label = this.getEdgeLabel(switcher);
        
        const edge = this.graph.createEdge({
            source: sourceNode,
            target: targetNode,
            data: {
                switcherId: switcher.switcher_id,
                scoreCondition: switcher.score_condition,
                semanticCondition: switcher.semantic_condition,
                targetConfig: switcher.target_config,
                sourceSceneId: switcher.scene_id,
            },
            labels: [
                {
                    attrs: {
                        text: {
                            text: label,
                            fontSize: 11,
                            fill: '#333',
                            textWrap: {
                                width: 120,
                                height: 40,
                                ellipsis: false,
                            },
                        },
                        rect: {
                            fill: '#ffffff',
                            stroke: '#e0e0e0',
                            strokeWidth: 1,
                            rx: 6,
                            ry: 6,
                            refWidth: '100%',
                            refHeight: '100%',
                            refX: 0,
                            refY: 0,
                        },
                    },
                    position: {
                        distance: 0.5,
                        offset: 15,
                    },
                },
            ],
        });

        // 尝试加载保存的位置
        setTimeout(() => {
            this.loadEdgePosition(edge);
        }, 100);

        return edge;
    }
    
    getEdgeLabel(switcher) {
        const labels = [];

        // 分数条件
        if (switcher.score_condition) {
            const condition = switcher.score_condition;
            labels.push(`分数${condition.symbol}${condition.score}`);
        }

        // 语义条件
        if (switcher.semantic_condition) {
            labels.push(switcher.semantic_condition);
        }

        return labels.length > 0 ? labels.join(' 且 ') : '无条件';
    }
    
    // 辅助方法
    getSceneByNodeId(nodeId) {
        const node = this.graph.getCellById(nodeId);
        return node ? this.scenes.find(s => s.sceneId === node.getData().sceneId) : null;
    }
    
    getNodeBySceneId(sceneId) {
        const cells = this.graph.getCells();
        return cells.find(cell => cell.isNode() && cell.getData().sceneId === sceneId);
    }
    
    getSceneByName(sceneName) {
        return this.scenes.find(s => s.sceneName === sceneName);
    }
    
    autoLayout() {
        // 简单的自动布局
        this.graph.centerContent();
    }
    
    // 显示切换器设置弹窗
    showSwitcherModal(edge, sourceScene, targetScene) {
        console.log('Show switcher modal:', sourceScene, targetScene);

        // 临时移除边，等待用户确认
        this.graph.removeCell(edge);

        // 确保场景数据完整
        const sourceSceneData = sourceScene || this.getSceneByNodeId(edge.getSourceNode().id);
        const targetSceneData = targetScene || this.getSceneByNodeId(edge.getTargetNode().id);

        console.log('Source scene data:', sourceSceneData);
        console.log('Target scene data:', targetSceneData);

        // 触发自定义事件
        const event = new CustomEvent('showSwitcherModal', {
            detail: {
                edge,
                sourceScene: sourceSceneData,
                targetScene: targetSceneData,
                onConfirm: (switcherData) => {
                    this.createSwitcherEdge(sourceSceneData, targetSceneData, switcherData);
                }
            }
        });
        document.dispatchEvent(event);
    }
    
    async createSwitcherEdge(sourceScene, targetScene, switcherData) {
        try {
            // 创建切换器数据
            const data = {
                scene_id: sourceScene.sceneId,
                scene_name: sourceScene.sceneName,
                score_condition: switcherData.symbol && switcherData.score ? {
                    symbol: switcherData.symbol,
                    score: switcherData.score
                } : null,
                semantic_condition: switcherData.semantic_condition || null,
                target_config: {
                    dist_scene: targetScene.sceneName,
                    score: switcherData.target_score || 0,
                    user_message: switcherData.user_message || ''
                }
            };
            
            // 保存到后端
            const token = localStorage.getItem('accessToken');
            const response = await fetch('/scene_switcher', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });
            const result = await response.json();

            if (result.code === 200) {
                // 重新加载数据
                this.loadScenes(this.currentJobClassificationId);
            } else {
                throw new Error(result.message || '保存失败');
            }
        } catch (error) {
            console.error('创建场景切换器失败:', error);
        }
    }
    
    // 公共方法
    fitToContent() {
        this.graph.centerContent();
    }
    
    resetZoom() {
        this.graph.zoomTo(1);
        this.graph.centerContent();
    }
    
    exportData() {
        return this.graph.toJSON();
    }
    
    importData(data) {
        this.graph.fromJSON(data);
    }
}
