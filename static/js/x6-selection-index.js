!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("@antv/x6")):"function"==typeof define&&define.amd?define(["exports","@antv/x6"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).X6PluginSelection={},e.X6)}(this,(function(e,t){"use strict";function i(e,t,i,n){var s,o=arguments.length,l=o<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,i):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)l=Reflect.decorate(e,t,i,n);else for(var r=e.length-1;r>=0;r--)(s=e[r])&&(l=(o<3?s(l):o>3?s(t,i,l):s(t,i))||l);return o>3&&l&&Object.defineProperty(t,i,l),l}class n extends t.View{get graph(){return this.options.graph}get boxClassName(){return this.prefixClassName(s.classNames.box)}get $boxes(){return t.Dom.children(this.container,this.boxClassName)}get handleOptions(){return this.options}constructor(e){super(),this.options=e,this.options.model&&(this.options.collection=this.options.model.collection),this.options.collection?this.collection=this.options.collection:(this.collection=new t.Collection([],{comparator:s.depthComparator}),this.options.collection=this.collection),this.boxCount=0,this.createContainer(),this.startListening()}startListening(){const e=this.graph,t=this.collection;this.delegateEvents({[`mousedown .${this.boxClassName}`]:"onSelectionBoxMouseDown",[`touchstart .${this.boxClassName}`]:"onSelectionBoxMouseDown"},!0),e.on("scale",this.onGraphTransformed,this),e.on("translate",this.onGraphTransformed,this),e.model.on("updated",this.onModelUpdated,this),t.on("added",this.onCellAdded,this),t.on("removed",this.onCellRemoved,this),t.on("reseted",this.onReseted,this),t.on("updated",this.onCollectionUpdated,this),t.on("node:change:position",this.onNodePositionChanged,this),t.on("cell:changed",this.onCellChanged,this)}stopListening(){const e=this.graph,t=this.collection;this.undelegateEvents(),e.off("scale",this.onGraphTransformed,this),e.off("translate",this.onGraphTransformed,this),e.model.off("updated",this.onModelUpdated,this),t.off("added",this.onCellAdded,this),t.off("removed",this.onCellRemoved,this),t.off("reseted",this.onReseted,this),t.off("updated",this.onCollectionUpdated,this),t.off("node:change:position",this.onNodePositionChanged,this),t.off("cell:changed",this.onCellChanged,this)}onRemove(){this.stopListening()}onGraphTransformed(){this.updateSelectionBoxes()}onCellChanged(){this.updateSelectionBoxes()}onNodePositionChanged({node:e,options:t}){const{showNodeSelectionBox:i,pointerEvents:n}=this.options,{ui:s,selection:o,translateBy:l,snapped:r}=t,a=(!0!==i||n&&"none"===this.getPointerEventsValue(n))&&!this.translating&&!o,c=s&&l&&e.id===l;if(a&&(c||r)){this.translating=!0;const i=e.position(),n=e.previous("position"),s=i.x-n.x,o=i.y-n.y;0===s&&0===o||this.translateSelectedNodes(s,o,e,t),this.translating=!1}}onModelUpdated({removed:e}){e&&e.length&&this.unselect(e)}isEmpty(){return this.length<=0}isSelected(e){return this.collection.has(e)}get length(){return this.collection.length}get cells(){return this.collection.toArray()}select(e,t={}){t.dryrun=!0;const i=this.filter(Array.isArray(e)?e:[e]);return this.collection.add(i,t),this}unselect(e,t={}){return t.dryrun=!0,this.collection.remove(Array.isArray(e)?e:[e],t),this}reset(e,t={}){if(e){if(t.batch){const i=this.filter(Array.isArray(e)?e:[e]);return this.collection.reset(i,Object.assign(Object.assign({},t),{ui:!0})),this}const i=this.cells,n=this.filter(Array.isArray(e)?e:[e]),s={},o={};i.forEach((e=>s[e.id]=e)),n.forEach((e=>o[e.id]=e));const l=[],r=[];return n.forEach((e=>{s[e.id]||l.push(e)})),i.forEach((e=>{o[e.id]||r.push(e)})),r.length&&this.unselect(r,Object.assign(Object.assign({},t),{ui:!0})),l.length&&this.select(l,Object.assign(Object.assign({},t),{ui:!0})),0===r.length&&0===l.length&&this.updateContainer(),this}return this.clean(t)}clean(e={}){return this.length&&(!1===e.batch?this.unselect(this.cells,e):this.collection.reset([],Object.assign(Object.assign({},e),{ui:!0}))),this}setFilter(e){this.options.filter=e}setContent(e){this.options.content=e}startSelecting(e){let i,n;e=this.normalizeEvent(e),this.clean();const o=this.graph.container;if(null!=e.offsetX&&null!=e.offsetY&&o.contains(e.target))i=e.offsetX,n=e.offsetY;else{const s=t.Dom.offset(o),l=o.scrollLeft,r=o.scrollTop;i=e.clientX-s.left+window.pageXOffset+l,n=e.clientY-s.top+window.pageYOffset+r}t.Dom.css(this.container,{top:n,left:i,width:1,height:1}),this.setEventData(e,{action:"selecting",clientX:e.clientX,clientY:e.clientY,offsetX:i,offsetY:n,scrollerX:0,scrollerY:0,moving:!1}),this.delegateDocumentEvents(s.documentEvents,e.data)}filter(e){const i=this.options.filter;return e.filter((e=>Array.isArray(i)?i.some((t=>"string"==typeof t?e.shape===t:e.id===t.id)):"function"!=typeof i||t.FunctionExt.call(i,this.graph,e)))}stopSelecting(e){const i=this.graph,n=this.getEventData(e);switch(n.action){case"selecting":{let e=t.Dom.width(this.container),n=t.Dom.height(this.container);const s=t.Dom.offset(this.container),o=i.pageToLocal(s.left,s.top),l=i.transform.getScale();e/=l.sx,n/=l.sy;const r=new t.Rectangle(o.x,o.y,e,n),a=this.getCellViewsInArea(r).map((e=>e.cell));this.reset(a,{batch:!0}),this.hideRubberband();break}case"translating":{const t=i.snapToGrid(e.clientX,e.clientY);if(!this.options.following){const e=n;this.updateSelectedNodesPosition({dx:e.clientX-e.originX,dy:e.clientY-e.originY})}this.graph.model.stopBatch("move-selection"),this.notifyBoxEvent("box:mouseup",e,t.x,t.y);break}default:this.clean()}}onMouseUp(e){this.getEventData(e).action&&(this.stopSelecting(e),this.undelegateDocumentEvents())}onSelectionBoxMouseDown(e){this.options.following||e.stopPropagation();const t=this.normalizeEvent(e);this.options.movable&&this.startTranslating(t);const i=this.getCellViewFromElem(t.target);this.setEventData(t,{activeView:i});const n=this.graph.snapToGrid(t.clientX,t.clientY);this.notifyBoxEvent("box:mousedown",t,n.x,n.y),this.delegateDocumentEvents(s.documentEvents,t.data)}startTranslating(e){this.graph.model.startBatch("move-selection");const t=this.graph.snapToGrid(e.clientX,e.clientY);this.setEventData(e,{action:"translating",clientX:t.x,clientY:t.y,originX:t.x,originY:t.y})}getRestrictArea(){const e=this.graph.options.translating.restrict,i="function"==typeof e?t.FunctionExt.call(e,this.graph,null):e;return"number"==typeof i?this.graph.transform.getGraphArea().inflate(i):!0===i?this.graph.transform.getGraphArea():i||null}getSelectionOffset(e,i){let n=e.x-i.clientX,s=e.y-i.clientY;const o=this.getRestrictArea();if(o){const l=this.collection.toArray(),r=t.Cell.getCellsBBox(l,{deep:!0})||t.Rectangle.create(),a=o.x-r.x,c=o.y-r.y,h=o.x+o.width-(r.x+r.width),d=o.y+o.height-(r.y+r.height);if(n<a&&(n=a),s<c&&(s=c),h<n&&(n=h),d<s&&(s=d),!this.options.following){const t=e.x-i.originX,o=e.y-i.originY;n=t<=a||t>=h?0:n,s=o<=c||o>=d?0:s}}return{dx:n,dy:s}}updateElementPosition(e,i,n){const s=t.Dom.css(e,"left"),o=t.Dom.css(e,"top"),l=s?parseFloat(s):0,r=o?parseFloat(o):0;t.Dom.css(e,"left",l+i),t.Dom.css(e,"top",r+n)}updateSelectedNodesPosition(e){const{dx:t,dy:i}=e;if(t||i)if(this.translateSelectedNodes(t,i),this.boxesUpdated)this.collection.length>1&&this.updateSelectionBoxes();else{const e=this.graph.transform.getScale();for(let n=0,s=this.$boxes,o=s.length;n<o;n+=1)this.updateElementPosition(s[n],t*e.sx,i*e.sy);this.updateElementPosition(this.selectionContainer,t*e.sx,i*e.sy)}}autoScrollGraph(e,t){const i=this.graph.getPlugin("scroller");return i?i.autoScroll(e,t):{scrollerX:0,scrollerY:0}}adjustSelection(e){const i=this.normalizeEvent(e),n=this.getEventData(i);switch(n.action){case"selecting":{const e=n;!0!==e.moving&&(t.Dom.appendTo(this.container,this.graph.container),this.showRubberband(),e.moving=!0);const{scrollerX:s,scrollerY:o}=this.autoScrollGraph(i.clientX,i.clientY);e.scrollerX+=s,e.scrollerY+=o;const l=i.clientX-e.clientX+e.scrollerX,r=i.clientY-e.clientY+e.scrollerY,a=parseInt(t.Dom.css(this.container,"left")||"0",10),c=parseInt(t.Dom.css(this.container,"top")||"0",10);t.Dom.css(this.container,{left:l<0?e.offsetX+l:a,top:r<0?e.offsetY+r:c,width:Math.abs(l),height:Math.abs(r)});break}case"translating":{const t=this.graph.snapToGrid(i.clientX,i.clientY),s=n,o=this.getSelectionOffset(t,s);this.options.following?this.updateSelectedNodesPosition(o):this.updateContainerPosition(o),o.dx&&(s.clientX=t.x),o.dy&&(s.clientY=t.y),this.notifyBoxEvent("box:mousemove",e,t.x,t.y);break}}this.boxesUpdated=!1}translateSelectedNodes(e,t,i,n){const s={},o=[];if(i&&(s[i.id]=!0),this.collection.toArray().forEach((e=>{e.getDescendants({deep:!0}).forEach((e=>{s[e.id]=!0}))})),n&&n.translateBy){const e=this.graph.getCellById(n.translateBy);e&&(s[e.id]=!0,e.getDescendants({deep:!0}).forEach((e=>{s[e.id]=!0})),o.push(e))}this.collection.toArray().forEach((i=>{if(!s[i.id]){const l=Object.assign(Object.assign({},n),{selection:this.cid,exclude:o});i.translate(e,t,l),this.graph.model.getConnectedEdges(i).forEach((i=>{s[i.id]||(i.translate(e,t,l),s[i.id]=!0)}))}}))}getCellViewsInArea(e){const t=this.graph,i={strict:this.options.strict};let n=[];return this.options.rubberNode&&(n=n.concat(t.model.getNodesInArea(e,i).map((e=>t.renderer.findViewByCell(e))).filter((e=>null!=e)))),this.options.rubberEdge&&(n=n.concat(t.model.getEdgesInArea(e,i).map((e=>t.renderer.findViewByCell(e))).filter((e=>null!=e)))),n}notifyBoxEvent(e,t,i,n){const s=this.getEventData(t).activeView;this.trigger(e,{e:t,view:s,x:i,y:n,cell:s.cell})}getSelectedClassName(e){return this.prefixClassName((e.isNode()?"node":"edge")+"-selected")}addCellSelectedClassName(e){const t=this.graph.renderer.findViewByCell(e);t&&t.addClass(this.getSelectedClassName(e))}removeCellUnSelectedClassName(e){const t=this.graph.renderer.findViewByCell(e);t&&t.removeClass(this.getSelectedClassName(e))}destroySelectionBox(e){this.removeCellUnSelectedClassName(e),this.canShowSelectionBox(e)&&(t.Dom.remove(this.container.querySelector(`[data-cell-id="${e.id}"]`)),0===this.$boxes.length&&this.hide(),this.boxCount=Math.max(0,this.boxCount-1))}destroyAllSelectionBoxes(e){e.forEach((e=>this.removeCellUnSelectedClassName(e))),this.hide(),t.Dom.remove(this.$boxes),this.boxCount=0}hide(){t.Dom.removeClass(this.container,this.prefixClassName(s.classNames.rubberband)),t.Dom.removeClass(this.container,this.prefixClassName(s.classNames.selected))}showRubberband(){t.Dom.addClass(this.container,this.prefixClassName(s.classNames.rubberband))}hideRubberband(){t.Dom.removeClass(this.container,this.prefixClassName(s.classNames.rubberband))}showSelected(){t.Dom.removeAttribute(this.container,"style"),t.Dom.addClass(this.container,this.prefixClassName(s.classNames.selected))}createContainer(){this.container=document.createElement("div"),t.Dom.addClass(this.container,this.prefixClassName(s.classNames.root)),this.options.className&&t.Dom.addClass(this.container,this.options.className),this.selectionContainer=document.createElement("div"),t.Dom.addClass(this.selectionContainer,this.prefixClassName(s.classNames.inner)),this.selectionContent=document.createElement("div"),t.Dom.addClass(this.selectionContent,this.prefixClassName(s.classNames.content)),t.Dom.append(this.selectionContainer,this.selectionContent),t.Dom.attr(this.selectionContainer,"data-selection-length",this.collection.length),t.Dom.prepend(this.container,this.selectionContainer)}updateContainerPosition(e){(e.dx||e.dy)&&this.updateElementPosition(this.selectionContainer,e.dx,e.dy)}updateContainer(){const e={x:1/0,y:1/0},i={x:0,y:0};this.collection.toArray().filter((e=>this.canShowSelectionBox(e))).forEach((t=>{const n=this.graph.renderer.findViewByCell(t);if(n){const t=n.getBBox({useCellGeometry:!0});e.x=Math.min(e.x,t.x),e.y=Math.min(e.y,t.y),i.x=Math.max(i.x,t.x+t.width),i.y=Math.max(i.y,t.y+t.height)}})),t.Dom.css(this.selectionContainer,{position:"absolute",pointerEvents:"none",left:e.x,top:e.y,width:i.x-e.x,height:i.y-e.y}),t.Dom.attr(this.selectionContainer,"data-selection-length",this.collection.length);const n=this.options.content;if(n)if("function"==typeof n){const e=t.FunctionExt.call(n,this.graph,this,this.selectionContent);e&&(this.selectionContent.innerHTML=e)}else this.selectionContent.innerHTML=n;this.collection.length>0&&!this.container.parentNode?t.Dom.appendTo(this.container,this.graph.container):this.collection.length<=0&&this.container.parentNode&&this.container.parentNode.removeChild(this.container)}canShowSelectionBox(e){return e.isNode()&&!0===this.options.showNodeSelectionBox||e.isEdge()&&!0===this.options.showEdgeSelectionBox}getPointerEventsValue(e){return"string"==typeof e?e:e(this.cells)}createSelectionBox(e){if(this.addCellSelectedClassName(e),this.canShowSelectionBox(e)){const i=this.graph.renderer.findViewByCell(e);if(i){const n=i.getBBox({useCellGeometry:!0}),s=this.boxClassName,o=document.createElement("div"),l=this.options.pointerEvents;t.Dom.addClass(o,s),t.Dom.addClass(o,`${s}-${e.isNode()?"node":"edge"}`),t.Dom.attr(o,"data-cell-id",e.id),t.Dom.css(o,{position:"absolute",left:n.x,top:n.y,width:n.width,height:n.height,pointerEvents:l?this.getPointerEventsValue(l):"auto"}),t.Dom.appendTo(o,this.container),this.showSelected(),this.boxCount+=1}}}updateSelectionBoxes(){this.collection.length>0&&(this.boxesUpdated=!0,this.confirmUpdate())}confirmUpdate(){if(this.boxCount){this.hide();for(let e=0,i=this.$boxes,n=i.length;e<n;e+=1){const n=i[e],s=t.Dom.attr(n,"data-cell-id");t.Dom.remove(n),this.boxCount-=1;const o=this.collection.get(s);o&&this.createSelectionBox(o)}this.updateContainer()}return 0}getCellViewFromElem(e){const t=e.getAttribute("data-cell-id");if(t){const e=this.collection.get(t);if(e)return this.graph.renderer.findViewByCell(e)}return null}onCellRemoved({cell:e}){this.destroySelectionBox(e),this.updateContainer()}onReseted({previous:e,current:t}){this.destroyAllSelectionBoxes(e),t.forEach((e=>{this.listenCellRemoveEvent(e),this.createSelectionBox(e)})),this.updateContainer()}onCellAdded({cell:e}){this.listenCellRemoveEvent(e),this.createSelectionBox(e),this.updateContainer()}listenCellRemoveEvent(e){e.off("removed",this.onCellRemoved,this),e.on("removed",this.onCellRemoved,this)}onCollectionUpdated({added:e,removed:t,options:i}){e.forEach((e=>{this.trigger("cell:selected",{cell:e,options:i}),e.isNode()?this.trigger("node:selected",{cell:e,options:i,node:e}):e.isEdge()&&this.trigger("edge:selected",{cell:e,options:i,edge:e})})),t.forEach((e=>{this.trigger("cell:unselected",{cell:e,options:i}),e.isNode()?this.trigger("node:unselected",{cell:e,options:i,node:e}):e.isEdge()&&this.trigger("edge:unselected",{cell:e,options:i,edge:e})}));const n={added:e,removed:t,options:i,selected:this.cells.filter((e=>!!this.graph.getCellById(e.id)))};this.trigger("selection:changed",n)}dispose(){this.clean(),this.remove(),this.off()}}var s;i([t.View.dispose()],n.prototype,"dispose",null),function(e){const t="widget-selection";e.classNames={root:t,inner:`${t}-inner`,box:`${t}-box`,content:`${t}-content`,rubberband:`${t}-rubberband`,selected:`${t}-selected`},e.documentEvents={mousemove:"adjustSelection",touchmove:"adjustSelection",mouseup:"onMouseUp",touchend:"onMouseUp",touchcancel:"onMouseUp"},e.depthComparator=function(e){return e.getAncestors().length}}(s||(s={}));t.Graph.prototype.isSelectionEnabled=function(){const e=this.getPlugin("selection");return!!e&&e.isEnabled()},t.Graph.prototype.enableSelection=function(){const e=this.getPlugin("selection");return e&&e.enable(),this},t.Graph.prototype.disableSelection=function(){const e=this.getPlugin("selection");return e&&e.disable(),this},t.Graph.prototype.toggleSelection=function(e){const t=this.getPlugin("selection");return t&&t.toggleEnabled(e),this},t.Graph.prototype.isMultipleSelection=function(){const e=this.getPlugin("selection");return!!e&&e.isMultipleSelection()},t.Graph.prototype.enableMultipleSelection=function(){const e=this.getPlugin("selection");return e&&e.enableMultipleSelection(),this},t.Graph.prototype.disableMultipleSelection=function(){const e=this.getPlugin("selection");return e&&e.disableMultipleSelection(),this},t.Graph.prototype.toggleMultipleSelection=function(e){const t=this.getPlugin("selection");return t&&t.toggleMultipleSelection(e),this},t.Graph.prototype.isSelectionMovable=function(){const e=this.getPlugin("selection");return!!e&&e.isSelectionMovable()},t.Graph.prototype.enableSelectionMovable=function(){const e=this.getPlugin("selection");return e&&e.enableSelectionMovable(),this},t.Graph.prototype.disableSelectionMovable=function(){const e=this.getPlugin("selection");return e&&e.disableSelectionMovable(),this},t.Graph.prototype.toggleSelectionMovable=function(e){const t=this.getPlugin("selection");return t&&t.toggleSelectionMovable(e),this},t.Graph.prototype.isRubberbandEnabled=function(){const e=this.getPlugin("selection");return!!e&&e.isRubberbandEnabled()},t.Graph.prototype.enableRubberband=function(){const e=this.getPlugin("selection");return e&&e.enableRubberband(),this},t.Graph.prototype.disableRubberband=function(){const e=this.getPlugin("selection");return e&&e.disableRubberband(),this},t.Graph.prototype.toggleRubberband=function(e){const t=this.getPlugin("selection");return t&&t.toggleRubberband(e),this},t.Graph.prototype.isStrictRubberband=function(){const e=this.getPlugin("selection");return!!e&&e.isStrictRubberband()},t.Graph.prototype.enableStrictRubberband=function(){const e=this.getPlugin("selection");return e&&e.enableStrictRubberband(),this},t.Graph.prototype.disableStrictRubberband=function(){const e=this.getPlugin("selection");return e&&e.disableStrictRubberband(),this},t.Graph.prototype.toggleStrictRubberband=function(e){const t=this.getPlugin("selection");return t&&t.toggleStrictRubberband(e),this},t.Graph.prototype.setRubberbandModifiers=function(e){const t=this.getPlugin("selection");return t&&t.setRubberbandModifiers(e),this},t.Graph.prototype.setSelectionFilter=function(e){const t=this.getPlugin("selection");return t&&t.setSelectionFilter(e),this},t.Graph.prototype.setSelectionDisplayContent=function(e){const t=this.getPlugin("selection");return t&&t.setSelectionDisplayContent(e),this},t.Graph.prototype.isSelectionEmpty=function(){const e=this.getPlugin("selection");return!e||e.isEmpty()},t.Graph.prototype.cleanSelection=function(e){const t=this.getPlugin("selection");return t&&t.clean(e),this},t.Graph.prototype.resetSelection=function(e,t){const i=this.getPlugin("selection");return i&&i.reset(e,t),this},t.Graph.prototype.getSelectedCells=function(){const e=this.getPlugin("selection");return e?e.getSelectedCells():[]},t.Graph.prototype.getSelectedCellCount=function(){const e=this.getPlugin("selection");return e?e.getSelectedCellCount():0},t.Graph.prototype.isSelected=function(e){const t=this.getPlugin("selection");return!!t&&t.isSelected(e)},t.Graph.prototype.select=function(e,t){const i=this.getPlugin("selection");return i&&i.select(e,t),this},t.Graph.prototype.unselect=function(e,t){const i=this.getPlugin("selection");return i&&i.unselect(e,t),this};class o extends t.Basecoat{get rubberbandDisabled(){return!0!==this.options.enabled||!0!==this.options.rubberband}get disabled(){return!0!==this.options.enabled}get length(){return this.selectionImpl.length}get cells(){return this.selectionImpl.cells}constructor(e={}){super(),this.name="selection",this.movedMap=new WeakMap,this.unselectMap=new WeakMap,this.options=Object.assign(Object.assign({enabled:!0},o.defaultOptions),e),t.CssLoader.ensure(this.name,".x6-widget-selection {\n  position: absolute;\n  top: 0;\n  left: 0;\n  display: none;\n  width: 0;\n  height: 0;\n  touch-action: none;\n}\n.x6-widget-selection-rubberband {\n  display: block;\n  overflow: visible;\n  opacity: 0.3;\n}\n.x6-widget-selection-selected {\n  display: block;\n}\n.x6-widget-selection-box {\n  cursor: move;\n}\n.x6-widget-selection-inner[data-selection-length='0'],\n.x6-widget-selection-inner[data-selection-length='1'] {\n  display: none;\n}\n.x6-widget-selection-content {\n  position: absolute;\n  top: 100%;\n  right: -20px;\n  left: -20px;\n  margin-top: 30px;\n  padding: 6px;\n  line-height: 14px;\n  text-align: center;\n  border-radius: 6px;\n}\n.x6-widget-selection-content:empty {\n  display: none;\n}\n.x6-widget-selection-rubberband {\n  background-color: #3498db;\n  border: 2px solid #2980b9;\n}\n.x6-widget-selection-box {\n  box-sizing: content-box !important;\n  margin-top: -4px;\n  margin-left: -4px;\n  padding-right: 4px;\n  padding-bottom: 4px;\n  border: 2px dashed #feb663;\n  box-shadow: 2px 2px 5px #d3d3d3;\n}\n.x6-widget-selection-inner {\n  box-sizing: content-box !important;\n  margin-top: -8px;\n  margin-left: -8px;\n  padding-right: 12px;\n  padding-bottom: 12px;\n  border: 2px solid #feb663;\n  box-shadow: 2px 2px 5px #d3d3d3;\n}\n.x6-widget-selection-content {\n  color: #fff;\n  font-size: 10px;\n  background-color: #6a6b8a;\n}\n")}init(e){this.graph=e,this.selectionImpl=new n(Object.assign(Object.assign({},this.options),{graph:e})),this.setup(),this.startListening()}isEnabled(){return!this.disabled}enable(){this.disabled&&(this.options.enabled=!0)}disable(){this.disabled||(this.options.enabled=!1)}toggleEnabled(e){return null!=e?e!==this.isEnabled()&&(e?this.enable():this.disable()):this.isEnabled()?this.disable():this.enable(),this}isMultipleSelection(){return this.isMultiple()}enableMultipleSelection(){return this.enableMultiple(),this}disableMultipleSelection(){return this.disableMultiple(),this}toggleMultipleSelection(e){return null!=e?e!==this.isMultipleSelection()&&(e?this.enableMultipleSelection():this.disableMultipleSelection()):this.isMultipleSelection()?this.disableMultipleSelection():this.enableMultipleSelection(),this}isSelectionMovable(){return!1!==this.options.movable}enableSelectionMovable(){return this.selectionImpl.options.movable=!0,this}disableSelectionMovable(){return this.selectionImpl.options.movable=!1,this}toggleSelectionMovable(e){return null!=e?e!==this.isSelectionMovable()&&(e?this.enableSelectionMovable():this.disableSelectionMovable()):this.isSelectionMovable()?this.disableSelectionMovable():this.enableSelectionMovable(),this}isRubberbandEnabled(){return!this.rubberbandDisabled}enableRubberband(){return this.rubberbandDisabled&&(this.options.rubberband=!0),this}disableRubberband(){return this.rubberbandDisabled||(this.options.rubberband=!1),this}toggleRubberband(e){return null!=e?e!==this.isRubberbandEnabled()&&(e?this.enableRubberband():this.disableRubberband()):this.isRubberbandEnabled()?this.disableRubberband():this.enableRubberband(),this}isStrictRubberband(){return!0===this.selectionImpl.options.strict}enableStrictRubberband(){return this.selectionImpl.options.strict=!0,this}disableStrictRubberband(){return this.selectionImpl.options.strict=!1,this}toggleStrictRubberband(e){return null!=e?e!==this.isStrictRubberband()&&(e?this.enableStrictRubberband():this.disableStrictRubberband()):this.isStrictRubberband()?this.disableStrictRubberband():this.enableStrictRubberband(),this}setRubberbandModifiers(e){this.setModifiers(e)}setSelectionFilter(e){return this.setFilter(e),this}setSelectionDisplayContent(e){return this.setContent(e),this}isEmpty(){return this.length<=0}clean(e={}){return this.selectionImpl.clean(e),this}reset(e,t={}){return this.selectionImpl.reset(e?this.getCells(e):[],t),this}getSelectedCells(){return this.cells}getSelectedCellCount(){return this.length}isSelected(e){return this.selectionImpl.isSelected(e)}select(e,t={}){const i=this.getCells(e);return i.length&&(this.isMultiple()?this.selectionImpl.select(i,t):this.reset(i.slice(0,1),t)),this}unselect(e,t={}){return this.selectionImpl.unselect(this.getCells(e),t),this}setup(){this.selectionImpl.on("*",((e,t)=>{this.trigger(e,t),this.graph.trigger(e,t)}))}startListening(){this.graph.on("blank:mousedown",this.onBlankMouseDown,this),this.graph.on("blank:click",this.onBlankClick,this),this.graph.on("cell:mousemove",this.onCellMouseMove,this),this.graph.on("cell:mouseup",this.onCellMouseUp,this),this.selectionImpl.on("box:mousedown",this.onBoxMouseDown,this)}stopListening(){this.graph.off("blank:mousedown",this.onBlankMouseDown,this),this.graph.off("blank:click",this.onBlankClick,this),this.graph.off("cell:mousemove",this.onCellMouseMove,this),this.graph.off("cell:mouseup",this.onCellMouseUp,this),this.selectionImpl.off("box:mousedown",this.onBoxMouseDown,this)}onBlankMouseDown({e:e}){if(!this.allowBlankMouseDown(e))return;const t=this.graph.panning.allowPanning(e,!0),i=this.graph.getPlugin("scroller"),n=i&&i.allowPanning(e,!0);(this.allowRubberband(e,!0)||this.allowRubberband(e)&&!n&&!t)&&this.startRubberband(e)}allowBlankMouseDown(e){const t=this.options.eventTypes;return(null==t?void 0:t.includes("leftMouseDown"))&&0===e.button||(null==t?void 0:t.includes("mouseWheelDown"))&&1===e.button}onBlankClick(){this.clean()}allowRubberband(e,i){return!this.rubberbandDisabled&&t.ModifierKey.isMatch(e,this.options.modifiers,i)}allowMultipleSelection(e){return this.isMultiple()&&t.ModifierKey.isMatch(e,this.options.multipleSelectionModifiers)}onCellMouseMove({cell:e}){this.movedMap.set(e,!0)}onCellMouseUp({e:e,cell:t}){const i=this.options;let n=this.disabled;!n&&this.movedMap.has(t)&&(n=!1===i.selectCellOnMoved,n||(n=!1===i.selectNodeOnMoved&&t.isNode()),n||(n=!1===i.selectEdgeOnMoved&&t.isEdge())),n||(this.allowMultipleSelection(e)?this.unselectMap.has(t)?this.unselectMap.delete(t):this.isSelected(t)?this.unselect(t):this.select(t):this.reset(t)),this.movedMap.delete(t)}onBoxMouseDown({e:e,cell:t}){this.disabled||this.allowMultipleSelection(e)&&(this.unselect(t),this.unselectMap.set(t,!0))}getCells(e){return(Array.isArray(e)?e:[e]).map((e=>"string"==typeof e?this.graph.getCellById(e):e)).filter((e=>null!=e))}startRubberband(e){return this.rubberbandDisabled||this.selectionImpl.startSelecting(e),this}isMultiple(){return!1!==this.options.multiple}enableMultiple(){return this.options.multiple=!0,this}disableMultiple(){return this.options.multiple=!1,this}setModifiers(e){return this.options.modifiers=e,this}setContent(e){return this.selectionImpl.setContent(e),this}setFilter(e){return this.selectionImpl.setFilter(e),this}dispose(){this.stopListening(),this.off(),this.selectionImpl.dispose(),t.CssLoader.clean(this.name)}}i([t.Basecoat.dispose()],o.prototype,"dispose",null),function(e){e.defaultOptions={rubberband:!1,rubberNode:!0,rubberEdge:!1,pointerEvents:"auto",multiple:!0,multipleSelectionModifiers:["ctrl","meta"],movable:!0,strict:!1,selectCellOnMoved:!1,selectNodeOnMoved:!1,selectEdgeOnMoved:!1,following:!0,content:null,eventTypes:["leftMouseDown","mouseWheelDown"]}}(o||(o={})),e.Selection=o}));
//# sourceMappingURL=index.js.map
