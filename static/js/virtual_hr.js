// 虚拟HR页面脚本
document.addEventListener('DOMContentLoaded', function() {
    // DOM元素
    const virtualHRList = document.getElementById('virtualHRList');
    const searchInput = document.getElementById('searchInput');
    const searchBtn = document.getElementById('searchBtn');
    const sortBy = document.getElementById('sortBy');
    const addVirtualHRBtn = document.getElementById('addVirtualHRBtn');
    
    // 模态框相关元素
    const virtualHRModal = document.getElementById('virtualHRModal');
    const virtualHRDetailModal = document.getElementById('virtualHRDetailModal');
    const modalClose = document.querySelectorAll('.modal-close');
    const modalTitle = document.getElementById('modalTitle');
    const virtualHRForm = document.getElementById('virtualHRForm');
    const virtualHRId = document.getElementById('virtualHRId');
    const nameInput = document.getElementById('name');
    const promptInput = document.getElementById('prompt');
    const cancelBtn = document.getElementById('cancelBtn');
    const saveVirtualHRBtn = document.getElementById('saveVirtualHRBtn');
    
    // 详情模态框相关元素
    const virtualHRDetails = document.getElementById('virtualHRDetails');
    const editVirtualHRBtn = document.getElementById('editVirtualHRBtn');
    const deleteVirtualHRBtn = document.getElementById('deleteVirtualHRBtn');
    const closeDetailBtn = document.getElementById('closeDetailBtn');
    
    // 当前虚拟HR列表
    let virtualHRData = [];
    // 当前选中的虚拟HR
    let currentVirtualHR = null;
    
    // 初始化页面
    initPage();
    
    // 页面初始化
    function initPage() {
        loadVirtualHRs();
        
        // 事件绑定
        addVirtualHRBtn.addEventListener('click', openAddModal);
        searchBtn.addEventListener('click', searchVirtualHRs);
        searchInput.addEventListener('keyup', function(event) {
            if (event.key === 'Enter') {
                searchVirtualHRs();
            }
        });
        sortBy.addEventListener('change', sortVirtualHRs);
        
        modalClose.forEach(btn => {
            btn.addEventListener('click', closeModals);
        });
        
        cancelBtn.addEventListener('click', closeModals);
        saveVirtualHRBtn.addEventListener('click', saveVirtualHR);
        // editVirtualHRBtn.addEventListener('click', openEditModal);
        deleteVirtualHRBtn.addEventListener('click', confirmDeleteVirtualHR);
        closeDetailBtn.addEventListener('click', closeModals);
    }
    
    // 加载虚拟HR列表
    async function loadVirtualHRs() {
        try {
            virtualHRList.innerHTML = '<div class="virtual-hr-list-loading"><p>加载中...</p></div>';
            
            const response = await API.virtualHR.getAll();
            virtualHRData = response || [];
            
            // 获取每个虚拟HR的统计数据
            for (let i = 0; i < virtualHRData.length; i++) {
                try {
                    const token = localStorage.getItem('accessToken');
                    const stats = await fetch(`/api/virtualhr/${virtualHRData[i].id}`,{
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    }).then(res => res.json());
                    virtualHRData[i].job_categories_count = stats.job_categories_count || 0;
                    virtualHRData[i].positions_count = stats.positions_count || 0;
                    virtualHRData[i].chat_users_count = stats.chat_users_count || 0;
                } catch (statsError) {
                    console.error(`获取虚拟HR ${virtualHRData[i].id} 统计数据失败:`, statsError);
                    virtualHRData[i].job_categories_count = 0;
                    virtualHRData[i].positions_count = 0;
                    virtualHRData[i].chat_users_count = 0;
                }
            }
            
            renderVirtualHRList();
        } catch (error) {
            console.error('加载虚拟HR列表失败:', error);
            showErrorMessage('加载虚拟HR列表失败，请稍后重试。');
        }
    }
    
    // 渲染虚拟HR列表
    function renderVirtualHRList() {
        if (!virtualHRData || virtualHRData.length === 0) {
            virtualHRList.innerHTML = `
                <div class="empty-state">
                    <div class="empty-state-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="empty-state-text">暂无虚拟HR数据</div>
                    <button class="btn btn-primary" onclick="document.getElementById('addVirtualHRBtn').click()">
                        <i class="fas fa-plus"></i> 添加虚拟HR
                    </button>
                </div>
            `;
            return;
        }
        
        let html = '';
        virtualHRData.forEach(hr => {
            html += `
                <div class="virtual-hr-card" data-id="${hr.id}">
                    <!-- 卡片顶部 - 名称 -->
                    <div class="virtual-hr-top">
                        <h3 class="virtual-hr-name">${hr.name}</h3>
                    </div>
                    
                    <!-- 卡片中部 - 统计数据 -->
                    <div class="virtual-hr-middle">
                        <div class="virtual-hr-stat">
                            <i class="fas fa-tags"></i>
                            <span>${hr.job_categories_count || 0} 职位分类</span>
                        </div>
                        <div class="virtual-hr-stat">
                            <i class="fas fa-briefcase"></i>
                            <span>${hr.positions_count || 0} 职位</span>
                        </div>
                        <div class="virtual-hr-stat">
                            <i class="fas fa-comments"></i>
                            <span>${hr.chat_users_count || 0} 用户</span>
                        </div>
                    </div>
                    
                    <!-- 卡片底部 - 操作按钮 -->
                    <div class="virtual-hr-bottom">
                        <div class="virtual-hr-action-btn view-details-btn" data-id="${hr.id}" title="查看详情">
                            <i class="fas fa-eye"></i>
                        </div>
                        <div class="virtual-hr-action-btn edit-hr-btn" data-id="${hr.id}" title="编辑">
                            <i class="fas fa-edit"></i>
                        </div>
                        <div class="virtual-hr-action-btn delete-hr-btn" data-id="${hr.id}" title="删除">
                            <i class="fas fa-trash-alt"></i>
                        </div>
                    </div>
                </div>
            `;
        });
        
        virtualHRList.innerHTML = html;
        
        // 添加事件监听
        const viewDetailsBtns = document.querySelectorAll('.view-details-btn');
        viewDetailsBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const hrId = btn.getAttribute('data-id');
                openDetailModal(hrId);
            });
        });
        
        const editHRBtns = document.querySelectorAll('.edit-hr-btn');
        editHRBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const hrId = btn.getAttribute('data-id');
                const hr = virtualHRData.find(h => h.id === hrId);
                openEditModal(hr);
            });
        });
        
        const deleteHRBtns = document.querySelectorAll('.delete-hr-btn');
        deleteHRBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const hrId = btn.getAttribute('data-id');
                const hr = virtualHRData.find(h => h.id === hrId);
                confirmDeleteVirtualHR(hr);
            });
        });
    }
    
    // 搜索虚拟HR
    function searchVirtualHRs() {
        const searchTerm = searchInput.value.toLowerCase().trim();
        
        if (!searchTerm) {
            renderVirtualHRList();
            return;
        }
        
        const filteredHRs = virtualHRData.filter(hr => {
            return hr.name.toLowerCase().includes(searchTerm) ||
                   (hr.prompt && hr.prompt.toLowerCase().includes(searchTerm));
        });
        
        const tempVirtualHRData = virtualHRData;
        virtualHRData = filteredHRs;
        renderVirtualHRList();
        virtualHRData = tempVirtualHRData;
    }
    
    // 排序虚拟HR
    function sortVirtualHRs() {
        const sortValue = sortBy.value;
        
        virtualHRData.sort((a, b) => {
            if (sortValue === 'name') {
                return a.name.localeCompare(b.name);
            } else if (sortValue === 'newest') {
                return new Date(b.createdAt) - new Date(a.createdAt);
            }
            return 0;
        });
        
        renderVirtualHRList();
    }
    
    // 打开添加模态框
    function openAddModal() {
        modalTitle.textContent = '新增虚拟HR';
        virtualHRForm.reset();
        virtualHRId.value = '';
        openModal(virtualHRModal);
    }
    
    // 打开编辑模态框
    function openEditModal(hr) {
        if (!hr && currentVirtualHR) {
            hr = currentVirtualHR;
        }
        
        if (!hr) {
            return;
        }
        
        modalTitle.textContent = '编辑虚拟HR';
        virtualHRId.value = hr.id;
        nameInput.value = hr.name;
        promptInput.value = hr.prompt || '';
        openModal(virtualHRModal);
    }
    
    // 打开详情模态框
    async function openDetailModal(hrId) {
        try {
            const hr = await API.virtualHR.getById(hrId);
            if (!hr) {
                showErrorMessage('无法加载虚拟HR详情');
                return;
            }
            
            currentVirtualHR = hr;
            
            // 获取统计数据
            try {
                const token = localStorage.getItem('accessToken');
                const stats = await fetch(`/api/virtualhr/${hrId}`,{
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                }).then(res => res.json());
                currentVirtualHR.job_categories_count = stats.job_categories_count || 0;
                currentVirtualHR.positions_count = stats.positions_count || 0;
                currentVirtualHR.chat_users_count = stats.chat_users_count || 0;
            } catch (statsError) {
                console.error(`获取虚拟HR ${hrId} 统计数据失败:`, statsError);
                currentVirtualHR.job_categories_count = 0;
                currentVirtualHR.positions_count = 0;
                currentVirtualHR.chat_users_count = 0;
            }
            
            const createdDate = new Date(hr.createdAt).toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            
            virtualHRDetails.innerHTML = `
                <div class="detail-section">
                    <div class="detail-title">名称</div>
                    <div class="detail-content">${hr.name}</div>
                </div>
                <div class="detail-section">
                    <div class="detail-title">统计信息</div>
                    <div class="detail-content">
                        <div class="stats-container">
                            <div class="stat-item">
                                <i class="fas fa-tags"></i>
                                <span>管理职位分类: ${currentVirtualHR.job_categories_count}</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-briefcase"></i>
                                <span>管理职位: ${currentVirtualHR.positions_count}</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-comments"></i>
                                <span>聊天用户: ${currentVirtualHR.chat_users_count}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="detail-section">
                    <div class="detail-title">创建时间</div>
                    <div class="detail-content">${createdDate}</div>
                </div>
                <div class="detail-section">
                    <div class="detail-title">提示词</div>
                    <div class="detail-content prompt-text">${hr.prompt || '无提示词'}</div>
                </div>
            `;
            
            openModal(virtualHRDetailModal);
        } catch (error) {
            console.error('加载虚拟HR详情失败:', error);
            showErrorMessage('加载虚拟HR详情失败，请稍后重试。');
        }
    }
    
    // 关闭所有模态框
    function closeModals() {
        closeModal(virtualHRModal);
        closeModal(virtualHRDetailModal);
    }
    
    // 保存虚拟HR
    async function saveVirtualHR() {
        const id = virtualHRId.value;
        const name = nameInput.value.trim();
        const prompt = promptInput.value.trim();
        
        if (!name) {
            showErrorMessage('请输入虚拟HR名称');
            return;
        }
        
        const hrData = {
            name,
            prompt
        };
        
        try {
            if (id) {
                // 更新虚拟HR
                await API.virtualHR.update(id, hrData);
                showSuccessMessage('虚拟HR更新成功');
            } else {
                // 添加虚拟HR
                await API.virtualHR.add(hrData);
                showSuccessMessage('虚拟HR添加成功');
            }
            
            closeModals();
            loadVirtualHRs();
        } catch (error) {
            console.error('保存虚拟HR失败:', error);
            showErrorMessage('保存虚拟HR失败，请稍后重试。');
        }
    }
    
    // 确认删除虚拟HR
    function confirmDeleteVirtualHR(hr) {
        if (!hr && currentVirtualHR) {
            hr = currentVirtualHR;
        }
        
        if (!hr) {
            return;
        }
        
        if (confirm(`确定要删除虚拟HR "${hr.name}" 吗？此操作不可恢复！`)) {
            deleteVirtualHR(hr.id);
        }
    }
    
    // 删除虚拟HR
    async function deleteVirtualHR(id) {
        try {
            await API.virtualHR.delete(id);
            showSuccessMessage('虚拟HR删除成功');
            closeModals();
            loadVirtualHRs();
        } catch (error) {
            console.error('删除虚拟HR失败:', error);
            showErrorMessage('删除虚拟HR失败，请稍后重试。');
        }
    }
    
    // 显示成功消息
    function showSuccessMessage(message) {
        alert(message);
    }
    
    // 显示错误消息
    function showErrorMessage(message) {
        alert(message);
    }
}); 