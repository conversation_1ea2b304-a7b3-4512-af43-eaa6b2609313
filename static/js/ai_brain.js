document.addEventListener('DOMContentLoaded', function() {
    // Initialize the AI Brain page
    initAiBrainPage();
    
    // Set up event listeners for form submissions
    setupEventListeners();
});

// Initialize the page by loading configuration data
async function initAiBrainPage() {
    try {
        // Fetch the configuration from the server
        const config = await fetchConfig();
        
        // Populate the form fields with current configuration
        populateConfigForm(config);
        
        // Initialize the prompt editors
        initPromptEditors(config);
    } catch (error) {
        console.error('Error initializing AI Brain page:', error);
        showNotification('加载配置失败，请重试', 'error');
    }
}

// Fetch the configuration data from the server
async function fetchConfig() {
    try {
        const token = localStorage.getItem('accessToken');
        const response = await fetch('/api/config', {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        if (!response.ok) {
            throw new Error('Failed to fetch configuration');
        }
        const data = await response.json();
        return data.data;
    } catch (error) {
        console.error('Error fetching configuration:', error);
        return {
            openai_config: {
                base_url: '',
                api_key: '',
                model_name: ''
            },
            default_prompt: '',
            position_prompt: '',
            scene_prompt: '',
            user_info_prompt: '',
            scoring_rules_prompt: ''
        };
    }
}

// Populate the form fields with the current configuration
function populateConfigForm(config) {
    if (config.openai_config) {
        document.getElementById('baseUrl').value = config.openai_config.base_url || '';
        document.getElementById('apiKey').value = config.openai_config.api_key || '';
        document.getElementById('modelName').value = config.openai_config.model_name || '';
    }
}

// Initialize the prompt editors with the current configuration
function initPromptEditors(config) {
    const defaultPromptEditor = document.getElementById('defaultPromptEditor');
    const positionPromptEditor = document.getElementById('positionPromptEditor');
    const scenePromptEditor = document.getElementById('scenePromptEditor');
    const userinfoPromptEditor = document.getElementById('userinfoPromptEditor');
    const scoringRulesPromptEditor = document.getElementById('scoringRulesPromptEditor');
    
    // Set the initial content of the editors
    defaultPromptEditor.value = config.default_prompt || '';
    positionPromptEditor.value = config.position_prompt || '';
    scenePromptEditor.value = config.scene_prompt || '';
    userinfoPromptEditor.value = config.user_info_prompt || '';
    scoringRulesPromptEditor.value = config.scoring_rules_prompt || '';
    
    // Render the initial markdown previews
    renderMarkdownPreview('defaultPromptEditor', 'defaultPromptPreview');
    renderMarkdownPreview('positionPromptEditor', 'positionPromptPreview');
    renderMarkdownPreview('scenePromptEditor', 'scenePromptPreview');
    renderMarkdownPreview('userinfoPromptEditor', 'userinfoPromptPreview');
    renderMarkdownPreview('scoringRulesPromptEditor', 'scoringRulesPromptPreview');
    
    // Set up real-time preview for all editors
    setupRealTimePreview('defaultPromptEditor', 'defaultPromptPreview');
    setupRealTimePreview('positionPromptEditor', 'positionPromptPreview');
    setupRealTimePreview('scenePromptEditor', 'scenePromptPreview');
    setupRealTimePreview('userinfoPromptEditor', 'userinfoPromptPreview');
    setupRealTimePreview('scoringRulesPromptEditor', 'scoringRulesPromptPreview');
}

// Set up event listeners
function setupEventListeners() {
    // AI config form submission
    document.getElementById('aiConfigForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        await saveAiConfig();
    });
    
    // Show/hide API key toggle
    document.getElementById('showApiKeyBtn').addEventListener('click', function() {
        const apiKeyInput = document.getElementById('apiKey');
        const btnIcon = this.querySelector('i');
        
        if (apiKeyInput.type === 'password') {
            apiKeyInput.type = 'text';
            btnIcon.classList.remove('fa-eye');
            btnIcon.classList.add('fa-eye-slash');
            this.querySelector('span').textContent = ' 隐藏密钥';
        } else {
            apiKeyInput.type = 'password';
            btnIcon.classList.remove('fa-eye-slash');
            btnIcon.classList.add('fa-eye');
            this.querySelector('span').textContent = ' 显示密钥';
        }
    });
    
    // Save default prompt
    document.getElementById('saveDefaultPromptBtn').addEventListener('click', async function() {
        await savePrompt('default_prompt', document.getElementById('defaultPromptEditor').value);
    });
    
    // Save position prompt
    document.getElementById('savePositionPromptBtn').addEventListener('click', async function() {
        await savePrompt('position_prompt', document.getElementById('positionPromptEditor').value);
    });
    
    // Save scene prompt
    document.getElementById('saveScenePromptBtn').addEventListener('click', async function() {
        console.log("Saving scene prompt");
        const scenePromptValue = document.getElementById('scenePromptEditor').value;
        console.log("Scene prompt value:", scenePromptValue.substring(0, 50) + "...");
        await savePrompt('scene_prompt', scenePromptValue);
    });
    
    // Save userinfo prompt
    document.getElementById('saveUserinfoPromptBtn').addEventListener('click', async function() {
        console.log("Saving user info prompt");
        const userInfoPromptValue = document.getElementById('userinfoPromptEditor').value;
        console.log("User info prompt value:", userInfoPromptValue.substring(0, 50) + "...");
        await savePrompt('user_info_prompt', userInfoPromptValue);
    });
    
    // Save scoring rules prompt
    document.getElementById('saveScoringRulesPromptBtn').addEventListener('click', async function() {
        console.log("Saving scoring rules prompt");
        const scoringRulesPromptValue = document.getElementById('scoringRulesPromptEditor').value;
        console.log("Scoring rules prompt value:", scoringRulesPromptValue.substring(0, 50) + "...");
        await savePrompt('scoring_rules_prompt', scoringRulesPromptValue);
    });
}

// Set up real-time preview for a textarea
function setupRealTimePreview(editorId, previewId) {
    const editor = document.getElementById(editorId);
    let debounceTimer;
    
    editor.addEventListener('input', function() {
        clearTimeout(debounceTimer);
        debounceTimer = setTimeout(() => {
            renderMarkdownPreview(editorId, previewId);
        }, 300); // Debounce time in ms
    });
}

// Render markdown preview for a textarea
function renderMarkdownPreview(editorId, previewId) {
    const editor = document.getElementById(editorId);
    const preview = document.getElementById(previewId);
    
    if (editor && preview) {
        // Use marked.js to render markdown
        preview.innerHTML = marked.parse(editor.value);
    }
}

// Save AI configuration
async function saveAiConfig() {
    try {
        const baseUrl = document.getElementById('baseUrl').value.trim();
        const apiKey = document.getElementById('apiKey').value.trim();
        const modelName = document.getElementById('modelName').value.trim();
        
        const configData = {
            openai_config: {
                base_url: baseUrl,
                api_key: apiKey,
                model_name: modelName
            }
        };
        const token = localStorage.getItem('accessToken');
        
        const response = await fetch('/api/config/openai', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(configData)
        });
        
        if (!response.ok) {
            throw new Error('Failed to save AI configuration');
        }
        
        // 使用全局通知
        showNotification('AI配置已成功保存', 'success');
    } catch (error) {
        console.error('Error saving AI configuration:', error);
        showNotification('保存配置失败，请重试', 'error');
    }
}

// Save prompt
async function savePrompt(promptType, promptContent) {
    try {
        console.log(`Saving ${promptType} prompt with content length: ${promptContent.length}`);
        
        const data = {};
        data[promptType] = promptContent;
        
        console.log(`Request data:`, data);

        const token = localStorage.getItem('accessToken');
        
        const response = await fetch('/api/config/prompt', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(data)
        });
        
        const responseData = await response.json();
        console.log(`API Response:`, responseData);
        
        if (responseData.code !== 200) {
            showNotification(responseData.message, 'error');
            return
        }
        
        // 使用全局通知
        if (promptType === 'default_prompt') {
            showNotification('总体提示词保存成功', 'success');
            // 保留内联指示器
            addInlineSuccess('saveDefaultPromptBtn');
        } else if (promptType === 'position_prompt') {
            showNotification('职位提示词保存成功', 'success');
            // 保留内联指示器
            addInlineSuccess('savePositionPromptBtn');
        } else if (promptType === 'scene_prompt') {
            showNotification('场景提示词保存成功', 'success');
            // 保留内联指示器
            addInlineSuccess('saveScenePromptBtn');
        } else if (promptType === 'user_info_prompt') {
            showNotification('用户信息提示词保存成功', 'success');
            // 保留内联指示器
            addInlineSuccess('saveUserinfoPromptBtn');
        } else if (promptType === 'scoring_rules_prompt') {
            showNotification('评分规则提示词保存成功', 'success');
            // 保留内联指示器
            addInlineSuccess('saveScoringRulesPromptBtn');
        }
    } catch (error) {
        console.error(`Error saving ${promptType}:`, error);
        showNotification('保存提示词失败，请重试', 'error');
    }
}

// Add inline success indicator next to a button
function addInlineSuccess(buttonId) {
    const button = document.getElementById(buttonId);
    if (!button) return;
    
    // Remove any existing indicators
    const existingIndicators = button.parentNode.querySelectorAll('.save-success-msg');
    existingIndicators.forEach(indicator => indicator.remove());
    
    // Create new indicator
    const indicator = document.createElement('span');
    indicator.className = 'save-success-msg';
    indicator.textContent = '✓ 保存成功';
    
    // Add to parent
    button.parentNode.appendChild(indicator);
    
    // Auto-remove after 3 seconds
    setTimeout(() => {
        indicator.remove();
    }, 3000);
}

// Show notification function (reused from main.js)
function showNotification(message, type = 'info') {
    // 检查全局通知函数
    if (typeof window.showNotification === 'function' && window !== window.parent) {
        // 如果是在iframe中，使用父窗口的通知函数
        window.parent.showNotification(message, type);
        return;
    }

    // 移除现有通知
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => {
        notification.remove();
    });
    
    // 创建新通知
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // 错误消息显示时间更长
    const displayTime = type === 'error' ? 5000 : 2000;
    
    // 自动移除通知
    setTimeout(() => {
        notification.classList.add('notification-hide');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, displayTime);
} 