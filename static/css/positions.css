/* 职位管理页面样式 */

/* Form Row/Column Layout - Improved alignment */
.form-row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
}

.form-col {
    flex: 0 0 50%;
    max-width: 50%;
    padding-right: 15px;
    padding-left: 15px;
}

/* Ensure form groups in columns have consistent height */
.form-col .form-group {
    margin-bottom: 20px;
    min-height: 70px;
}

/* Special case for range inputs that are taller */
.form-col .form-group .range-inputs {
    margin-bottom: 0;
}

/* Special case for select multiple which is taller */
.form-col .form-group select[multiple] {
    height: 130px;
}

@media (max-width: 768px) {
    .form-col {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .form-col .form-group {
        min-height: auto;
    }
}

/* Improved form control styling */
.form-control {
    display: block;
    width: 100%;
    padding: 8px 12px;
    font-size: 14px;
    line-height: 1.5;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    border-color: var(--primary-color);
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: var(--secondary-color);
}

/* Responsive form layout improvements */
@media (max-width: 768px) {
    .form-row {
        margin-right: 0;
        margin-left: 0;
    }
    
    .form-col {
        padding-right: 0;
        padding-left: 0;
    }
}

/* Detail Grid Layout */
.detail-grid {
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
}

.detail-column {
    flex: 0 0 50%;
    max-width: 50%;
    padding-right: 15px;
    padding-left: 15px;
}

@media (max-width: 768px) {
    .detail-column {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

/* 页面头部 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.page-header h2 {
    margin: 0;
    color: var(--secondary-color);
}

/* 搜索和筛选 */
.filter-container {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.search-box {
    display: flex;
    flex: 1;
    min-width: 250px;
    max-width: 500px;
}

.search-box input {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    flex: 1;
}

.search-box button {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    white-space: nowrap;
    padding: 8px 15px;
    min-width: 80px;
}

.filter-options {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-left: auto;
}

.filter-options .form-group {
    margin-bottom: 0;
}

/* 职位列表 */
.position-list {
    min-height: 300px;
}

.position-list-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;
}

/* 职位分类卡片样式 */
.position-card {
    border: 1px solid var(--border-color);
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 15px;
    background-color: white;
    transition: all 0.2s;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.position-card:hover {
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.position-card .position-info {
    flex: 1;
    margin-right: 15px;
    display: block;
}

.position-card .position-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 5px;
}

.position-card .position-description {
    color: var(--text-light);
    margin-bottom: 10px;
}

.position-card .position-meta {
    display: flex;
    gap: 15px;
    color: var(--text-light);
    font-size: 12px;
    border-top: none;
    padding-top: 0;
}

.position-card .position-meta span {
    display: flex;
    align-items: center;
}

.position-card .position-meta i {
    margin-right: 5px;
}

.position-card .position-actions {
    display: flex;
    gap: 8px;
}

/* 原有的职位项样式 */
.position-item {
    border: 1px solid var(--border-color);
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 15px;
    background-color: white;
    transition: all 0.2s;
}

.position-item:hover {
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.position-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.position-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--dark-color);
    margin: 0;
}

.position-actions {
    display: flex;
    gap: 5px;
}

.position-item-body {
    margin-bottom: 10px;
}

.position-item-footer {
    display: flex;
    justify-content: flex-end;
    color: var(--text-light);
    font-size: 12px;
}

.position-meta {
    display: flex;
    align-items: center;
}

.position-meta i {
    margin-right: 5px;
}

/* 职位详情样式 */
.position-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.position-info {
    margin-bottom: 15px;
}

/* Position Info Layout */
.position-info-row {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 10px;
}

.position-info-column {
    flex: 0 0 50%;
    max-width: 50%;
}

.position-info-item {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

.position-info-item i {
    margin-right: 10px;
    color: var(--primary-color);
}

@media (max-width: 576px) {
    .position-info-column {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

.position-description {
    margin-top: 10px;
    color: var(--text-light);
}

.position-meta {
    display: flex;
    justify-content: space-between;
    color: var(--text-light);
    font-size: 12px;
    border-top: 1px solid var(--border-color);
    padding-top: 10px;
}

.position-empty {
    text-align: center;
    padding: 50px 0;
}

.position-empty i {
    font-size: 48px;
    color: var(--text-light);
    margin-bottom: 15px;
}

.position-empty p {
    color: var(--text-light);
    margin-bottom: 15px;
}

/* 分页 */
.pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.pagination {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    list-style: none;
    padding: 0;
}

.pagination-item {
    margin: 0 5px;
}

.pagination-link {
    display: block;
    padding: 8px 12px;
    border-radius: 4px;
    background-color: white;
    border: 1px solid var(--border-color);
    color: var(--text-color);
    cursor: pointer;
    text-align: center;
    min-width: 36px;
    min-height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pagination-link.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination-link.disabled {
    color: var(--text-light);
    cursor: not-allowed;
}

.pagination-link:not(.disabled):hover {
    background-color: var(--bg-light);
}

/* Enhanced modal styling */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.6);
    animation: fadeIn 0.3s;
}

#positionModal {
    z-index: 1001;
}

.modal-content {
    background-color: #fff;
    margin: 4% auto;
    padding: 0;
    width: 80%;
    max-width: 900px;
    border-radius: 8px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25);
    animation: slideIn 0.3s;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid var(--border-color);
    background-color: #f8f9fa;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

.modal-header h3 {
    margin: 0;
    color: var(--secondary-color);
    font-weight: 600;
}

.modal-close {
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
    transition: color 0.2s;
}

.modal-close:hover {
    color: #343a40;
}

.modal-body {
    padding: 25px;
    max-height: 70vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 15px 25px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    background-color: #f8f9fa;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Form group styling improvements */
.form-group {
    margin-bottom: 20px;
    position: relative;
}

.form-group:last-child {
    margin-bottom: 10px;
}

/* Ensure even spacing in columns */
.form-row {
    align-items: flex-start;
}

/* Adjustments for specific form elements */
.form-group select,
.form-group input {
    height: 38px;
}

.form-group textarea {
    min-height: 100px;
}

.range-inputs {
    display: flex;
    align-items: center;
    gap: 10px;
}

.range-inputs input {
    flex: 1;
}

.range-inputs span {
    margin: 0;
    font-weight: 500;
    color: var(--text-color);
}

small {
    display: block;
    margin-top: 5px;
    color: var(--text-light);
    font-size: 12px;
}

.form-error {
    color: var(--danger-color);
    font-size: 12px;
    margin-top: 5px;
}

.form-control-error {
    border-color: var(--danger-color);
}

.detail-section {
    margin-bottom: 20px;
}

.detail-section h4 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid var(--border-color);
}

.detail-item {
    margin-bottom: 15px;
}

.detail-item.full-width {
    margin-top: 15px;
    padding: 0 15px;
}

.detail-label {
    font-weight: 600;
    display: block;
    margin-bottom: 5px;
    color: var(--secondary-color);
}

.detail-value {
    color: var(--text-color);
}

.empty {
    color: var(--text-light);
    font-style: italic;
}

@media (max-width: 768px) {
    .filter-container {
        flex-direction: column;
    }
    
    .filter-options {
        margin-left: 0;
    }
    
    .modal-content {
        width: 95%;
        margin: 10% auto;
    }
    
    .position-card {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .position-card .position-info {
        margin-right: 0;
        margin-bottom: 15px;
        width: 100%;
    }
    
    .position-card .position-actions {
        width: 100%;
        justify-content: flex-end;
    }
}

.prompt-preview {
    background-color: var(--bg-light);
    padding: 15px;
    border-radius: 5px;
    max-height: 300px;
    overflow-y: auto;
}

.prompt-preview pre {
    white-space: pre-wrap;
    margin: 0;
    font-family: monospace;
    font-size: 14px;
}

.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
} 

/* Position Tag Styling */
.position-tag-container {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 15px;
    gap: 10px;
}

.position-tag {
    display: inline-flex;
    align-items: center;
    padding: 5px 10px;
    border-radius: 4px;
    background-color: var(--bg-light);
    font-size: 0.85rem;
}

.position-tag i {
    margin-right: 5px;
    color: var(--primary-color);
}

.position-city {
    background-color: #e3f2fd;
    color: #0d47a1;
}

.position-city i {
    color: #1976d2;
}

.position-salary {
    background-color: #e8f5e9;
    color: #1b5e20;
}

.position-salary i {
    color: #388e3c;
}

.position-age {
    background-color: #fff3e0;
    color: #e65100;
}

.position-age i {
    color: #f57c00;
}

.position-education {
    background-color: #f3e5f5;
    color: #4a148c;
}

.position-education i {
    color: #7b1fa2;
}

.position-additional-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
    margin-bottom: 10px;
}

.position-additional-info .position-info-item {
    font-size: 0.85rem;
    color: var(--text-light);
} 

/* Enhanced button styling */
.btn {
    display: inline-block;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    user-select: none;
    border: 1px solid transparent;
    padding: 8px 16px;
    font-size: 14px;
    line-height: 1.5;
    border-radius: 4px;
    transition: all 0.2s ease-in-out;
    cursor: pointer;
}

.btn:focus, .btn:hover {
    text-decoration: none;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn-primary {
    color: #fff;
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #0069d9;
    border-color: #0062cc;
}

.btn-light {
    color: #212529;
    background-color: #f8f9fa;
    border-color: #f8f9fa;
}

.btn-light:hover {
    background-color: #e2e6ea;
    border-color: #dae0e5;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 3px;
} 

/* Multi-select styling */
select[multiple] {
    padding: 8px;
    height: 120px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
}

select[multiple] option {
    padding: 6px 10px;
    margin-bottom: 2px;
    border-radius: 3px;
    transition: background-color 0.2s;
}

select[multiple] option:checked {
    background-color: rgba(0, 123, 255, 0.2);
    color: var(--primary-color);
    font-weight: 500;
}

select[multiple] option:hover {
    background-color: #f1f3f5;
}

small.select-help {
    display: block;
    margin-top: 5px;
    color: var(--text-light);
    font-size: 12px;
    font-style: italic;
} 