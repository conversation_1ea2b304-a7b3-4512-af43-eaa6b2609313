/* 虚拟HR页面样式 */
/* 虚拟HR列表容器 */
.virtual-hr-list {
    margin-top: 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    justify-content: flex-start;
}

/* 虚拟HR卡片 */
.virtual-hr-card {
    background: #fff;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid #eaeaea;
    transition: all 0.3s ease;
    width: calc(25% - 15px);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.virtual-hr-card:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

/* 卡片顶部 - 名称 */
.virtual-hr-top {
    margin-bottom: 12px;
    text-align: center;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 8px;
}

.virtual-hr-name {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

/* 卡片中部 - 统计数据 */
.virtual-hr-middle {
    margin-bottom: 12px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
}

.virtual-hr-stat {
    background-color: #f8f9fa;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    color: #555;
    display: flex;
    align-items: center;
    border: 1px solid #eaeaea;
}

.virtual-hr-stat i {
    margin-right: 5px;
    color: var(--primary-color);
}

/* 卡片底部 - 操作按钮 */
.virtual-hr-bottom {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.virtual-hr-action-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border: 1px solid #eaeaea;
    cursor: pointer;
    transition: all 0.2s;
    color: #666;
}

.virtual-hr-action-btn:hover {
    background-color: #e9ecef;
}

.virtual-hr-action-btn.view-details-btn:hover {
    color: var(--primary-color);
}

.virtual-hr-action-btn.edit-hr-btn:hover {
    color: var(--success-color);
}

.virtual-hr-action-btn.delete-hr-btn:hover {
    color: var(--danger-color);
}

/* 页面头部样式 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.page-header h2 {
    margin: 0;
}

/* 搜索和过滤区域样式 */
.filter-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 15px;
}

.search-box {
    display: flex;
    gap: 10px;
    flex: 1;
    min-width: 280px;
}

.search-box .form-control {
    flex: 1;
}

.filter-options {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.filter-options .form-group {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 0;
}

.filter-options .form-label {
    margin-bottom: 0;
    white-space: nowrap;
}

#virtualHRDetails {
    padding: 15px 0;
}

.detail-section {
    margin-bottom: 20px;
}

.detail-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 5px;
    color: #333;
}

.detail-content {
    font-size: 14px;
    color: #666;
    background: #f9f9f9;
    padding: 15px;
    border-radius: 5px;
    border: 1px solid #eee;
    white-space: pre-wrap;
}

.stats-container {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.stat-item {
    display: flex;
    align-items: center;
    background-color: #fff;
    padding: 8px 12px;
    border-radius: 5px;
    border: 1px solid #eaeaea;
    font-size: 14px;
}

.stat-item i {
    margin-right: 8px;
    color: var(--primary-color);
}

.prompt-text {
    white-space: pre-wrap;
    line-height: 1.5;
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
}

.empty-state-icon {
    font-size: 48px;
    color: #ddd;
    margin-bottom: 20px;
}

.empty-state-text {
    font-size: 16px;
    color: #888;
    margin-bottom: 20px;
}

.virtual-hr-list-loading {
    text-align: center;
    padding: 30px 0;
    font-size: 16px;
    color: #888;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    overflow-y: auto;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modal.show {
    opacity: 1;
    display: block;
}

.modal-content {
    background-color: #fff;
    margin: 5% auto;
    width: 90%;
    max-width: 600px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    position: relative;
    animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #eee;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    color: var(--secondary-color, #2c3e50);
}

.modal-close {
    font-size: 24px;
    font-weight: bold;
    color: #aaa;
    cursor: pointer;
}

.modal-close:hover {
    color: #333;
}

.modal-body {
    padding: 20px;
    max-height: calc(100vh - 200px);
    overflow-y: auto;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.modal-footer button {
    min-width: 80px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
    .virtual-hr-card {
        width: calc(33.333% - 14px);
    }
}

@media (max-width: 992px) {
    .virtual-hr-card {
        width: calc(50% - 10px);
    }
}

@media (max-width: 576px) {
    .virtual-hr-card {
        width: 100%;
    }
}

@media (max-width: 768px) {
    .filter-container {
        flex-direction: column;
    }
    
    .search-box, .filter-options {
        width: 100%;
    }
    
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .page-header button {
        align-self: flex-end;
    }
    
    .modal-content {
        width: 95%;
        margin: 10% auto;
    }
} 