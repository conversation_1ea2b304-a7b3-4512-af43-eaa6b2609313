/* 面试场景页面样式 */
.scenes-container {
    display: flex;
    gap: 20px;
    height: calc(100vh - 120px);
}

.positions-panel,
.scenes-panel {
    background-color: white;
    border-radius: 5px;
    box-shadow: var(--shadow);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.positions-panel {
    flex: 1;
    max-width: 300px;
}

.scenes-panel {
    flex: 3;
}

.panel-header {
    padding: 15px;
    background-color: var(--bg-light);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.panel-header h3 {
    margin: 0;
    font-size: 16px;
}

.panel-body {
    padding: 0;
    overflow-y: auto;
    flex: 1;
    position: relative;
    height: calc(100vh - 200px); /* 确保有足够的高度 */
}

/* 岗位列表样式 */
.position-item {
    padding: 12px 15px;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.2s;
}

.position-item:hover {
    background-color: var(--bg-light);
}

.position-item.active {
    background-color: var(--primary-color);
    color: white;
}

.position-item-name {
    font-weight: 500;
}

.position-item-count {
    background-color: var(--light-color);
    color: var(--secondary-color);
    border-radius: 12px;
    padding: 2px 8px;
    font-size: 12px;
}

.position-item.active .position-item-count {
    background-color: white;
    color: var(--primary-color);
}

/* 场景列表样式 */
.scene-item {
    border-bottom: 1px solid var(--border-color);
}

.scene-header {
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
}

.scene-header:hover {
    background-color: var(--bg-light);
}

.scene-title {
    display: flex;
    align-items: center;
    gap: 10px;
}

.scene-title h4 {
    margin: 0;
    font-size: 15px;
}

.scene-toggle {
    cursor: pointer;
    transition: transform 0.3s;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.scene-toggle.expanded {
    transform: rotate(180deg);
}

.scene-actions {
    display: flex;
    gap: 10px;
}

.scene-action {
    color: var(--text-light);
    cursor: pointer;
    transition: all 0.2s;
}

.scene-action:hover {
    color: var(--primary-color);
}

.scene-action.delete:hover {
    color: var(--danger-color);
}

.scene-content {
    display: none;
    padding: 0 15px 15px 45px;
    background-color: var(--bg-light);
}

.scene-content.expanded {
    display: block;
}

.scene-info {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px dashed var(--border-color);
}

.scene-info-item {
    margin-bottom: 5px;
    display: flex;
    font-size: 13px;
}

.scene-info-label {
    width: 80px;
    color: var(--text-light);
}

.scene-questions {
    margin-top: 10px;
}

.scene-questions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.scene-questions-title {
    font-size: 14px;
    font-weight: 600;
}

.question-item {
    background-color: white;
    padding: 12px;
    margin-bottom: 8px;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.question-drag-handle {
    cursor: grab;
    margin-right: 8px;
    color: var(--text-light);
}

.question-drag-handle:hover {
    color: var(--primary-color);
}

.question-sequence {
    min-width: 25px;
    margin-right: 8px;
    font-weight: 500;
    color: var(--text-light);
}

.question-content {
    flex: 1;
}

.question-score {
    background-color: var(--primary-color);
    color: white;
    border-radius: 4px;
    padding: 2px 8px;
    margin: 0 10px;
    font-size: 13px;
}

.question-action {
    color: var(--text-light);
    cursor: pointer;
}

.question-action:hover {
    color: var(--danger-color);
}

.add-question-btn {
    cursor: pointer;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
}

.add-question-btn:hover {
    text-decoration: underline;
}

.no-position-selected, 
.no-scenes-message {
    padding: 40px 0;
    text-align: center;
    color: var(--text-light);
}

.positions-list-loading {
    padding: 20px;
    text-align: center;
    color: var(--text-light);
}

/* 弹窗样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    z-index: 1000;
    overflow-y: auto;
}

.modal.show {
    display: block;
}

.modal-dialog {
    max-width: 500px;
    margin: 100px auto;
}

.modal-content {
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    overflow: hidden;
}

.modal-header {
    padding: 15px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    margin: 0;
    font-weight: 600;
}

.close {
    border: none;
    background: none;
    font-size: 20px;
    cursor: pointer;
    color: var(--text-light);
}

.modal-body {
    padding: 15px;
}

.modal-footer {
    padding: 15px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* 特殊表单元素样式 */
.score-range-inputs {
    display: flex;
    gap: 10px;
    align-items: center;
}

.score-range-inputs input {
    flex: 1;
}

.range-separator {
    color: var(--text-light);
}

.checkbox {
    display: flex;
    align-items: center;
    gap: 8px;
}

.checkbox input {
    margin: 0;
}

/* 默认场景标记 */
.default-scene-badge {
    background-color: var(--success-color);
    color: white;
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 3px;
    margin-left: 5px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .scenes-container {
        flex-direction: column;
        height: auto;
    }
    
    .positions-panel {
        max-width: none;
    }
}

.questions-sortable-container .sortable-ghost {
    opacity: 0.5;
    background-color: var(--bg-light);
}

.questions-sortable-container .sortable-drag {
    background-color: white;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
} 