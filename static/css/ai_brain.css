/* AI Brain Page Styles */
.prompt-editor-container {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

/* Input with button styling */
.input-with-button {
    display: flex;
    align-items: center;
    gap: 10px;
}

.input-with-button .form-control {
    flex: 1;
}

.input-with-button .btn {
    white-space: nowrap;
}

/* Alert notification styling */
.alert {
    padding: 10px 15px;
    border-radius: 4px;
    margin-bottom: 15px;
    position: relative;
    font-weight: bold;
    text-align: center;
}

.alert-success {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.alert-error {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.save-success-msg {
    display: inline-block;
    animation: fadeInOut 3s ease-in-out;
    color: #28a745;
    font-weight: bold;
    margin-left: 10px;
}

@keyframes fadeInOut {
    0% { opacity: 0; }
    15% { opacity: 1; }
    85% { opacity: 1; }
    100% { opacity: 0; }
}

.prompt-editor-section {
    flex: 1;
}

.prompt-preview-section {
    flex: 1;
    border-left: 1px solid #e0e0e0;
    padding-left: 20px;
}

.prompt-editor-wrapper {
    position: relative;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.prompt-editor {
    width: 100%;
    min-height: 300px;
    padding: 15px;
    font-family: 'Courier New', Courier, monospace;
    font-size: 14px;
    line-height: 1.5;
    border: none;
    resize: vertical;
}

.prompt-preview {
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    min-height: 300px;
    padding: 15px;
    overflow-y: auto;
}

/* Make sure markdown renders well */
.prompt-preview h1, 
.prompt-preview h2, 
.prompt-preview h3, 
.prompt-preview h4, 
.prompt-preview h5, 
.prompt-preview h6 {
    margin-top: 1em;
    margin-bottom: 0.5em;
}

.prompt-preview code {
    background-color: #eee;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', Courier, monospace;
}

.prompt-preview pre {
    background-color: #f4f4f4;
    padding: 10px;
    border-radius: 4px;
    overflow-x: auto;
}

.prompt-preview blockquote {
    border-left: 3px solid #ddd;
    margin-left: 0;
    padding-left: 15px;
    color: #555;
}

.mt-5 {
    margin-top: 5px;
}

.mt-10 {
    margin-top: 10px;
}

.my-20 {
    margin-top: 20px;
    margin-bottom: 20px;
}

/* Responsive design */
@media (max-width: 768px) {
    .prompt-editor-container {
        flex-direction: column;
    }
    
    .prompt-preview-section {
        border-left: none;
        padding-left: 0;
        border-top: 1px solid #e0e0e0;
        padding-top: 20px;
        margin-top: 20px;
    }
} 