/* APIKey页面样式 */

/* 通用样式 */
.mt-20 {
    margin-top: 20px;
}

.section-header {
    margin-bottom: 20px;
}

.section-header h2 {
    margin-bottom: 8px;
    font-size: 24px;
    color: #333;
}

.section-header p {
    color: #666;
    font-size: 15px;
}

/* 表单样式 */
.form-inline {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 15px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #444;
}

.form-group input[type="text"],
.form-group input[type="date"] {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.form-help {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

/* 按钮样式 */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: background-color 0.2s;
}

.btn-primary {
    background-color: #4a6cf7;
    color: white;
}

.btn-primary:hover {
    background-color: #3a5bd7;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
}

/* 表格样式 */
.data-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.data-table th,
.data-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.data-table th {
    font-weight: 600;
    background-color: #f8f9fa;
}

.data-table tr:hover {
    background-color: #f1f3f9;
}

/* API Key展示样式 */
.apikey-display {
    font-family: monospace;
    background-color: #f5f5f5;
    padding: 4px 8px;
    border-radius: 4px;
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 状态提示样式 */
.loading-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: #666;
}

.empty-message {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30px;
    color: #666;
    font-style: italic;
}

.empty-message i,
.loading-indicator i {
    margin-right: 8px;
}

/* 代码块样式 */
.code-block {
    background-color: #f8f9fa;
    border: 1px solid #eee;
    border-radius: 4px;
    padding: 15px;
    margin: 10px 0;
    overflow-x: auto;
}

.code-block pre {
    margin: 0;
    white-space: pre-wrap;
    font-family: monospace;
}

.code-block code {
    color: #333;
    font-size: 14px;
}

/* API使用指南样式 */
.api-usage-guide h4 {
    margin-top: 20px;
    margin-bottom: 10px;
    font-size: 16px;
    color: #333;
}

.api-usage-guide ul {
    padding-left: 20px;
}

.api-usage-guide li {
    margin-bottom: 5px;
}

/* 用量统计样式 */
.usage-summary {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    padding: 10px 0;
}

.usage-stat {
    flex: 1;
    min-width: 200px;
    display: flex;
    align-items: center;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.stat-icon {
    width: 40px;
    height: 40px;
    background-color: #e9ecef;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
}

.stat-icon i {
    font-size: 18px;
    color: #4a6cf7;
}

.stat-info h4 {
    font-size: 24px;
    font-weight: 600;
    margin: 0;
    margin-bottom: 5px;
}

.stat-info p {
    margin: 0;
    color: #6c757d;
    font-size: 14px;
} 