/* 触发器页面特定样式 */

.page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.page-header h2 {
    margin: 0;
}

.trigger-panels {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    align-items: start;
}

@media (max-width: 992px) {
    .trigger-panels {
        grid-template-columns: 1fr;
    }
}

.panel-column {
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: 100%;
}

.table-responsive {
    overflow-x: auto;
    width: 100%;
}

.btn-action {
    margin-right: 5px;
    padding: 5px 10px;
    font-size: 0.8em;
}

.btn-edit {
    background-color: #4caf50;
    color: white;
}

.btn-delete {
    background-color: #f44336;
    color: white;
}

.trigger-panels .card {
    display: flex;
    flex-direction: column;
    height: auto;
    min-height: 300px;
}

.trigger-panels .card-body {
    flex: 1;
    overflow: visible;
    display: flex;
    flex-direction: column;
}

/* 设置固定高度，确保两边标题栏一致 */
.trigger-panels .card-header {
    height: 60px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.trigger-panels .card-header h3 {
    margin: 0;
}

.filter-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.filter-options {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.trigger-list {
    margin-top: 10px;
}

.trigger-item {
    background-color: white;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: var(--shadow);
    transition: transform 0.2s;
}

.trigger-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.trigger-item-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
}

.trigger-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.trigger-actions {
    display: flex;
    gap: 8px;
}

.trigger-actions button {
    padding: 5px 10px;
    font-size: 12px;
}

.trigger-info {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 10px;
}

.trigger-info-item {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-width: 150px;
}

.info-label {
    font-size: 12px;
    color: var(--text-light);
    margin-bottom: 3px;
}

.info-value {
    font-weight: 500;
}

.trigger-list-loading {
    text-align: center;
    padding: 20px;
    color: var(--text-light);
}

.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 15px;
    padding-top: 10px;
    border-top: 1px solid #eee;
}

.pagination-info {
    color: #666;
    font-size: 14px;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.pagination-current {
    margin: 0 8px;
    font-size: 14px;
}

.items-per-page {
    width: auto;
    min-width: 80px;
    height: 30px;
    padding: 2px 8px;
    font-size: 13px;
}

.pagination {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
}

.pagination-item {
    margin: 0 5px;
}

.pagination-link {
    display: block;
    padding: 5px 10px;
    border: 1px solid var(--border-color);
    border-radius: 3px;
    cursor: pointer;
    transition: all 0.2s;
}

.pagination-link:hover {
    background-color: var(--primary-color);
    color: white;
}

.pagination-link.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination-link.disabled {
    color: var(--text-light);
    cursor: not-allowed;
}

@media (max-width: 768px) {
    .filter-container {
        flex-direction: column;
        gap: 10px;
    }
    
    .filter-options {
        flex-direction: column;
        width: 100%;
    }
    
    .search-box {
        width: 100%;
    }
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background-color: #fff;
    border-radius: 5px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
}

.modal-close {
    cursor: pointer;
    font-size: 24px;
    opacity: 0.7;
}

.modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.table td {
    vertical-align: middle;
    padding: 8px 12px;
}

.table td:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

/* 新增样式 */
.search-container {
    margin-bottom: 15px;
}

.search-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.table-container {
    flex: 1;
    min-height: 200px;
    overflow: visible;
    width: 100%;
}

.empty-row {
    height: 42px;
    border-bottom: 1px solid #f3f3f3;
}

.prompt-card {
    display: flex;
    flex-direction: column;
    height: auto;
    min-height: 300px;
}

.prompt-card .card-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 15px;
    overflow: visible;
}

.prompt-textarea {
    width: 100%;
    height: 200px;
    resize: none;
    padding: 10px;
    font-size: 14px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.card-footer {
    padding: 12px 15px;
    border-top: 1px solid #eaeaea;
    display: flex;
    justify-content: flex-end;
}

/* 带有说明的行样式 */
.has-explanation {
    cursor: help;
    position: relative;
}

.has-explanation:after {
    content: "\f05a"; /* 信息图标 */
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    font-size: 10px;
    color: #2196F3;
    position: absolute;
    top: 8px;
    left: 3px;
}

.has-explanation:hover {
    background-color: rgba(33, 150, 243, 0.05);
}

/* 设置表格内容的最大宽度和溢出处理 */
.table td {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 鼠标悬停时显示完整内容 */
.table td:hover {
    white-space: normal;
    word-break: break-word;
} 