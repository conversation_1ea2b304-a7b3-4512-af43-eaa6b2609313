/* 仪表盘特定样式 - 现代科技风格 */

:root {
    --card-bg: #ffffff;
    --card-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
    --primary-color: #3498db;
    --secondary-color: #2c3e50;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --purple-color: #9b59b6;
    --teal-color: #1abc9c;
    --dark-blue: #34495e;
    --info-color: #3498db;
    --light-bg: #f9fafb;
    --border-color: #eaeaea;
}

/* 全局容器样式 */
.content {
    background-color: #f5f7fb;
    padding: 20px;
    position: relative;
    overflow: visible;
    z-index: 1;
}

.content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.05) 0%, rgba(52, 152, 219, 0) 70%);
    z-index: -1;
}

/* 粒子背景 */
.particles-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
}

/* 欢迎区域 */
.welcome-section {
    padding: 20px 25px;
    background-color: var(--card-bg);
    border-radius: 10px;
    box-shadow: var(--card-shadow);
    margin-bottom: 25px;
    position: relative;
    overflow: hidden;
    z-index: 2;
    border-left: 4px solid var(--primary-color);
    animation: slideIn 0.5s ease-out;
}

.welcome-section::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 150px;
    height: 100%;
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.05) 0%, rgba(255, 255, 255, 0) 100%);
    z-index: -1;
}

.welcome-section h2 {
    font-size: 24px;
    margin-bottom: 10px;
    color: var(--secondary-color);
    font-weight: 600;
}

.welcome-section p {
    color: #7f8c8d;
    font-size: 14px;
    line-height: 1.5;
}

/* 统计卡片 */
.stats-cards {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 20px;
    margin-bottom: 25px;
    position: relative;
    z-index: 2;
}

.stat-card {
    display: flex;
    align-items: center;
    padding: 20px;
    border-radius: 10px;
    background-color: var(--card-bg);
    box-shadow: var(--card-shadow);
    position: relative;
    overflow: hidden;
    z-index: 2;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    animation: fadeIn 0.6s ease-out;
    animation-fill-mode: both;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }
.stat-card:nth-child(5) { animation-delay: 0.5s; }
.stat-card:nth-child(6) { animation-delay: 0.6s; }

.stat-card::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 80%);
    opacity: 0;
    z-index: -1;
    transition: opacity 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.stat-card:hover::after {
    opacity: 1;
}

.stat-icon {
    font-size: 24px;
    width: 50px;
    height: 50px;
    border-radius: 10px;
    background-color: rgba(52, 152, 219, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    transition: all 0.3s ease;
}

.stat-card:hover .stat-icon {
    transform: scale(1.1) rotate(5deg);
}

.stat-card:nth-child(1) .stat-icon {
    color: var(--primary-color);
    background-color: rgba(52, 152, 219, 0.1);
}

.stat-card:nth-child(2) .stat-icon {
    color: var(--success-color);
    background-color: rgba(46, 204, 113, 0.1);
}

.stat-card:nth-child(3) .stat-icon {
    color: var(--purple-color);
    background-color: rgba(155, 89, 182, 0.1);
}

.stat-card:nth-child(4) .stat-icon {
    color: var(--warning-color);
    background-color: rgba(243, 156, 18, 0.1);
}

.stat-card:nth-child(5) .stat-icon {
    color: var(--teal-color);
    background-color: rgba(26, 188, 156, 0.1);
}

.stat-card:nth-child(6) .stat-icon {
    color: var(--danger-color);
    background-color: rgba(231, 76, 60, 0.1);
}

.stat-info h3 {
    font-size: 22px;
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--secondary-color);
    transition: all 0.3s ease;
}

.stat-card:hover .stat-info h3 {
    transform: scale(1.05);
    color: var(--primary-color);
}

.stat-label {
    color: #7f8c8d;
    font-size: 13px;
    font-weight: 500;
}

/* 图表卡片 */
.chart-card {
    background-color: var(--card-bg);
    border-radius: 10px;
    padding: 20px;
    box-shadow: var(--card-shadow);
    margin-bottom: 25px;
    position: relative;
    z-index: 2;
    animation: fadeIn 0.7s ease-out;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.chart-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--secondary-color);
}

.chart-container {
    height: 300px;
    position: relative;
}

/* 数据表格 */
.data-card {
    background-color: var(--card-bg);
    border-radius: 10px;
    box-shadow: var(--card-shadow);
    margin-bottom: 25px;
    overflow: hidden;
    position: relative;
    z-index: 2;
    animation: fadeIn 0.8s ease-out;
}

.data-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    background-color: rgba(52, 152, 219, 0.03);
}

.data-card-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--secondary-color);
    display: flex;
    align-items: center;
}

.data-card-title i {
    margin-right: 10px;
    color: var(--primary-color);
}

.data-card-badge {
    background-color: var(--primary-color);
    color: white;
    border-radius: 20px;
    padding: 3px 10px;
    font-size: 12px;
    font-weight: 500;
}

.data-card-body {
    padding: 0;
    max-height: 400px;
    overflow-y: auto;
    position: relative;
    scrollbar-width: thin;
    scrollbar-color: rgba(52, 152, 219, 0.3) transparent;
    background-color: #ffffff;
}

/* 自定义滚动条样式 */
.data-card-body::-webkit-scrollbar {
    width: 6px;
}

.data-card-body::-webkit-scrollbar-track {
    background: transparent;
}

.data-card-body::-webkit-scrollbar-thumb {
    background-color: rgba(52, 152, 219, 0.3);
    border-radius: 3px;
}

.data-card-body::-webkit-scrollbar-thumb:hover {
    background-color: rgba(52, 152, 219, 0.5);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table thead {
    background-color: #f0f7fc !important;
    position: sticky;
    top: 0;
    z-index: 10;
}

.data-table thead tr {
    background-color: #f0f7fc !important;
}

.data-table th {
    background-color: #f0f7fc !important;
    text-align: left;
    padding: 12px 15px;
    font-weight: 500;
    color: var(--secondary-color);
    border-bottom: 1px solid var(--border-color);
    font-size: 13px;
    position: sticky;
    top: 0;
    z-index: 10;
    box-shadow: 0 1px 0 var(--border-color);
    opacity: 1 !important;
    backdrop-filter: none !important;
}

.data-table td {
    padding: 12px 15px;
    border-bottom: 1px solid var(--border-color);
    color: #555;
    font-size: 13px;
}

.data-table tr:last-child td {
    border-bottom: none;
}

.data-table tr:hover td {
    background-color: rgba(52, 152, 219, 0.05);
}

/* 表格滚动动画 */
@keyframes tableScroll {
    0% {
        transform: translateY(0);
    }
    100% {
        transform: translateY(-100%);
    }
}

/* 仪表盘布局 */
.dashboard-row {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin-bottom: 20px;
    position: relative;
    z-index: 2;
}

/* 按钮样式 */
.btn-view, .btn-edit, .btn-delete {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 5px;
    transition: all 0.2s ease;
}

.btn-view {
    background-color: var(--info-color);
}

.btn-edit {
    background-color: var(--warning-color);
}

.btn-delete {
    background-color: var(--danger-color);
}

.btn-view:hover, .btn-edit:hover, .btn-delete:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 状态徽章 */
.badge {
    padding: 4px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    display: inline-block;
}

.badge-success {
    background-color: rgba(46, 204, 113, 0.1);
    color: var(--success-color);
}

.badge-warning {
    background-color: rgba(243, 156, 18, 0.1);
    color: var(--warning-color);
}

.badge-danger {
    background-color: rgba(231, 76, 60, 0.1);
    color: var(--danger-color);
}

.badge-info {
    background-color: rgba(52, 152, 219, 0.1);
    color: var(--info-color);
}

/* 加载器 */
.dashboard-loader {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.9);
    padding: 20px 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.dashboard-loader p {
    margin-top: 10px;
    font-size: 14px;
    color: var(--secondary-color);
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 字体样式与main.css保持一致 */
.sidebar-menu-item {
    font-size: 14px;
}

.sidebar-menu-item i {
    font-size: 18px;
}

/* Token使用统计卡片 */
.token-usage-card {
    background-color: var(--card-bg);
    border-radius: 10px;
    box-shadow: var(--card-shadow);
    padding: 20px;
    margin-bottom: 25px;
    position: relative;
    z-index: 2;
    animation: fadeIn 0.9s ease-out;
}

.token-usage-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.token-usage-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--secondary-color);
}

.token-usage-chart {
    height: 250px;
    position: relative;
}

.token-usage-info {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

.token-usage-stat {
    text-align: center;
    padding: 10px;
    border-radius: 8px;
    background-color: rgba(52, 152, 219, 0.05);
    flex: 1;
    margin: 0 5px;
}

.token-usage-value {
    font-size: 24px;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.token-usage-label {
    font-size: 12px;
    color: #7f8c8d;
}

/* 响应式设计 */
@media (max-width: 1400px) {
    .stats-cards {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 1200px) {
    .stats-cards {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 992px) {
    .dashboard-row {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .stats-cards {
        grid-template-columns: 1fr;
    }
    
    .stat-card {
        padding: 15px;
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 24px;
    }
    
    .stat-info h3 {
        font-size: 22px;
    }
} 

/* 确保表头不受全局样式影响 */
.data-card-body .data-table thead,
.data-card-body .data-table thead tr,
.data-card-body .data-table th {
    opacity: 1 !important;
    background-color: #f0f7fc !important;
    -webkit-backdrop-filter: none !important;
    backdrop-filter: none !important;
} 