/* 批量管理页面样式 */

/* CSS变量定义（防止main.css未加载） */
:root {
    --primary-color: #3498db;
    --secondary-color: #2c3e50;
    --success-color: #2ecc71;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-color: #dee2e6;
    --text-light: #6c757d;
    --shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 页面头部样式 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.page-header h2 {
    margin: 0;
    color: var(--secondary-color);
}

/* 页面头部操作按钮样式 */
.header-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.header-actions .btn {
    white-space: nowrap;
}

/* 搜索和筛选样式 */
.filter-container {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.search-box {
    display: flex;
    align-items: center;
    gap: 8px;
}

.search-box .form-control {
    width: 300px;
    margin-bottom: 0;
}

/* 按钮基础样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.5;
    border: 1px solid transparent;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    transition: all 0.2s ease-in-out;
    white-space: nowrap;
    gap: 6px;
}

.btn:hover {
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn:active {
    transform: translateY(0);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: #2980b9;
    border-color: #2980b9;
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
    color: white;
}

.btn-success:hover {
    background-color: #27ae60;
    border-color: #27ae60;
}

.btn-info {
    background-color: #17a2b8;
    border-color: #17a2b8;
    color: white;
}

.btn-info:hover {
    background-color: #138496;
    border-color: #138496;
}

.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
    border-color: #5a6268;
}

.btn-light {
    background-color: #f8f9fa;
    border-color: #f8f9fa;
    color: #495057;
}

.btn-light:hover {
    background-color: #e2e6ea;
    border-color: #dae0e5;
}

/* 特殊按钮样式 */
.btn i {
    margin-right: 6px;
}

.btn-primary i {
    color: rgba(255, 255, 255, 0.9);
}

.btn-success i {
    color: rgba(255, 255, 255, 0.9);
}

.btn-info i {
    color: rgba(255, 255, 255, 0.9);
}

/* 头部操作按钮特殊样式 */
.header-actions .btn {
    font-weight: 500;
    letter-spacing: 0.5px;
}

.header-actions .btn-success {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    border: none;
    box-shadow: 0 2px 8px rgba(39, 174, 96, 0.3);
}

.header-actions .btn-success:hover {
    background: linear-gradient(135deg, #229954, #27ae60);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(39, 174, 96, 0.4);
}

.header-actions .btn-info {
    background: linear-gradient(135deg, #3498db, #5dade2);
    border: none;
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

.header-actions .btn-info:hover {
    background: linear-gradient(135deg, #2980b9, #3498db);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.4);
}

.header-actions .btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    border: none;
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

.header-actions .btn-primary:hover {
    background: linear-gradient(135deg, #2980b9, #1f618d);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.4);
}

/* 表格样式 */
.table-responsive {
    overflow-x: auto;
    margin-bottom: 20px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
    margin: 0;
    min-width: 1200px; /* 确保表格有足够宽度显示所有列 */
}

.table th,
.table td {
    padding: 12px 8px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
    font-size: 13px;
    border-right: 1px solid #f0f0f0;
}

.table th:last-child,
.table td:last-child {
    border-right: none;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: var(--secondary-color);
    white-space: nowrap;
    position: sticky;
    top: 0;
    z-index: 10;
}

.table tbody tr {
    transition: background-color 0.2s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

.table tbody tr:nth-child(even) {
    background-color: #fafafa;
}

.table tbody tr:nth-child(even):hover {
    background-color: #f0f0f0;
}

.table td {
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.table td.expandable {
    cursor: pointer;
    position: relative;
}

.table td.expandable:hover {
    background-color: #e3f2fd !important;
    color: var(--primary-color);
}

/* 操作按钮样式 */
.action-buttons {
    display: flex;
    gap: 5px;
    white-space: nowrap;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 4px;
}

.btn-edit {
    background-color: #17a2b8;
    color: white;
    border: none;
}

.btn-edit:hover {
    background-color: #138496;
}

.btn-delete {
    background-color: #dc3545;
    color: white;
    border: none;
}

.btn-delete:hover {
    background-color: #c82333;
}

/* 复选框样式 */
.table input[type="checkbox"] {
    transform: scale(1.2);
    cursor: pointer;
}

/* 模态框基础样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    max-height: 90vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color);
    background-color: #f8f9fa;
}

.modal-header h3 {
    margin: 0;
    color: var(--secondary-color);
    font-size: 18px;
}

.modal-close {
    font-size: 24px;
    font-weight: bold;
    color: #aaa;
    cursor: pointer;
    line-height: 1;
    padding: 0;
    background: none;
    border: none;
}

.modal-close:hover {
    color: #000;
}

.modal-body {
    padding: 24px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px 24px;
    border-top: 1px solid var(--border-color);
    background-color: #f8f9fa;
}

/* 表单模态框样式 */
.record-form-modal {
    max-width: 1000px;
    width: 95%;
    max-height: 90vh;
}

.record-form-modal .modal-body {
    max-height: calc(90vh - 140px);
    overflow-y: auto;
    padding: 20px 24px;
}

.form-row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -12px;
    margin-left: -12px;
    gap: 0;
}

.form-col {
    flex: 0 0 50%;
    max-width: 50%;
    padding-right: 12px;
    padding-left: 12px;
    min-height: 100%;
}

.form-group {
    margin-bottom: 18px;
    position: relative;
}

.form-group:last-child {
    margin-bottom: 8px;
}

.form-label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    font-size: 13px;
    color: var(--secondary-color);
    line-height: 1.2;
}

.form-label .required {
    color: #dc3545;
    font-weight: bold;
}

.form-control {
    display: block;
    width: 100%;
    padding: 10px 12px;
    font-size: 14px;
    line-height: 1.4;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    margin-bottom: 0;
    background-color: #fff;
}

.form-control:focus {
    border-color: var(--primary-color);
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    background-color: #fff;
}

.form-control:invalid {
    border-color: #dc3545;
}

.form-control::placeholder {
    color: #999;
    font-size: 13px;
}

textarea.form-control {
    resize: vertical;
    min-height: 70px;
    font-family: inherit;
}

/* 特殊字段的高度调整 */
textarea.form-control[rows="2"] {
    min-height: 60px;
}

textarea.form-control[rows="3"] {
    min-height: 80px;
}

/* 导入模态框样式 */
.import-modal {
    max-width: 600px;
}

.import-section {
    margin-bottom: 25px;
}

.import-section h4 {
    margin-bottom: 15px;
    color: var(--secondary-color);
    font-size: 16px;
}

.file-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: #f8f9fa;
}

.file-upload-area:hover {
    border-color: var(--primary-color);
    background-color: #e3f2fd;
}

.file-upload-area.dragover {
    border-color: var(--primary-color);
    background-color: #e3f2fd;
    transform: scale(1.02);
}

.upload-placeholder i {
    font-size: 48px;
    color: #6c757d;
    margin-bottom: 15px;
}

.upload-placeholder p {
    margin: 8px 0;
    color: var(--secondary-color);
}

.file-types {
    font-size: 12px;
    color: #6c757d;
}

.selected-file {
    display: flex;
    align-items: center;
    padding: 12px;
    background-color: #e3f2fd;
    border-radius: 6px;
    margin-top: 10px;
}

.selected-file i {
    color: var(--primary-color);
    margin-right: 10px;
}

.selected-file .file-name {
    flex: 1;
    font-weight: 500;
}

.btn-remove-file {
    background: none;
    border: none;
    color: #dc3545;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
}

.btn-remove-file:hover {
    background-color: rgba(220, 53, 69, 0.1);
}

.import-options {
    border-top: 1px solid var(--border-color);
    padding-top: 20px;
}

.import-options .form-group {
    margin-bottom: 12px;
}

.import-options label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: normal;
}

.import-options input[type="checkbox"] {
    margin-right: 8px;
    transform: scale(1.1);
}

/* 分页样式 */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}

.pagination-info {
    color: var(--secondary-color);
    font-size: 14px;
}

.pagination {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 5px;
}

.pagination .page-item {
    display: inline-block;
}

.pagination .page-link {
    display: block;
    padding: 8px 12px;
    text-decoration: none;
    color: var(--primary-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    transition: all 0.2s;
}

.pagination .page-link:hover {
    background-color: var(--primary-color);
    color: white;
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .page-item.disabled .page-link {
    color: #6c757d;
    cursor: not-allowed;
    background-color: #f8f9fa;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 100px 40px;
    color: #6c757d;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    margin: 40px auto;
    max-width: 600px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    position: relative;
    overflow: hidden;
}

.empty-state::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3498db, #2ecc71, #f39c12, #e74c3c);
    opacity: 0.6;
}

.empty-state i {
    font-size: 80px;
    margin-bottom: 30px;
    opacity: 0.3;
    color: #3498db;
    display: block;
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.empty-state h3 {
    margin: 0 0 16px 0;
    color: var(--secondary-color);
    font-size: 24px;
    font-weight: 600;
    letter-spacing: -0.5px;
}

.empty-state p {
    margin: 0 0 40px 0;
    font-size: 16px;
    color: #6c757d;
    line-height: 1.6;
    max-width: 450px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 40px;
}

.empty-state .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin: 0;
    padding: 16px 32px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 8px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    border: none;
    color: white;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.4);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    min-width: 160px;
}

.empty-state .btn i {
    font-size: 18px;
    margin-right: 8px;
    margin-bottom: 0;
    animation: none;
    opacity: 1;
    color: white;
}

.empty-state .btn:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 8px 25px rgba(52, 152, 219, 0.5);
    background: linear-gradient(135deg, #2980b9, #1f618d);
}

.empty-state .btn:active {
    transform: translateY(-1px) scale(1.01);
}

/* 表格中的空状态特殊样式 */
.table .empty-state {
    margin: 0;
    border-radius: 0;
    background: linear-gradient(135deg, #fafbfc 0%, #f1f3f4 100%);
    border: none;
    box-shadow: none;
    padding: 60px 20px;
    position: relative;
}

.table .empty-state::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #3498db, #2ecc71);
    border-radius: 0 0 3px 3px;
}

.table td.empty-state {
    border: none;
    vertical-align: middle;
    height: 400px;
    position: relative;
}

.empty-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    min-height: 300px;
}

/* 表格中空状态的特殊样式覆盖 */
.table .empty-state i {
    font-size: 64px;
    color: #3498db;
    opacity: 0.6;
    margin-bottom: 24px;
}

.table .empty-state h3 {
    font-size: 22px;
    color: #2c3e50;
    margin-bottom: 12px;
    font-weight: 600;
}

.table .empty-state p {
    font-size: 15px;
    color: #7f8c8d;
    margin-bottom: 32px;
    max-width: 400px;
}

.table .empty-state .btn {
    background: linear-gradient(135deg, #3498db, #2980b9);
    border: none;
    padding: 14px 28px;
    font-size: 15px;
    font-weight: 600;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
    transition: all 0.3s ease;
}

.table .empty-state .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
    background: linear-gradient(135deg, #2980b9, #1f618d);
}

/* 加载状态样式 */
.loading-state {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.loading-spinner {
    display: inline-block;
    width: 32px;
    height: 32px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header-actions {
        flex-direction: column;
        gap: 8px;
        align-items: stretch;
    }

    .header-actions .btn {
        width: 100%;
        justify-content: center;
    }

    .filter-container {
        flex-direction: column;
    }

    .search-box {
        justify-content: center;
    }

    .search-box .form-control {
        width: 100%;
        max-width: 300px;
    }

    .form-col {
        flex: 0 0 100%;
        max-width: 100%;
    }

    .table th,
    .table td {
        padding: 8px 4px;
        font-size: 12px;
    }

    .action-buttons {
        flex-direction: column;
        gap: 2px;
    }
}
