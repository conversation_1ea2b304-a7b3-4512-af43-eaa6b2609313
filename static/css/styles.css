/* Form Row/Column Layout */
.form-row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
}

.form-col {
    flex: 0 0 50%;
    max-width: 50%;
    padding-right: 15px;
    padding-left: 15px;
}

@media (max-width: 768px) {
    .form-col {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

/* Detail Grid Layout */
.detail-grid {
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
}

.detail-column {
    flex: 0 0 50%;
    max-width: 50%;
    padding-right: 15px;
    padding-left: 15px;
}

@media (max-width: 768px) {
    .detail-column {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

/* Position Info Layout */
.position-info-row {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 10px;
}

.position-info-column {
    flex: 0 0 50%;
    max-width: 50%;
}

@media (max-width: 576px) {
    .position-info-column {
        flex: 0 0 100%;
        max-width: 100%;
    }
} 