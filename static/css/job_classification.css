/* 职位分类页面样式 */
/* 职位分类列表容器 */
.classification-list {
    margin-top: 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    justify-content: flex-start;
}

/* 职位分类卡片 */
.classification-card {
    background: #fff;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid #eaeaea;
    transition: all 0.3s ease;
    width: calc(25% - 15px);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.classification-card:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

/* 卡片顶部 - 名称 */
.classification-top {
    margin-bottom: 12px;
    text-align: center;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 8px;
    position: relative;
}

.classification-name {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

/* 虚拟HR标签 */
.virtual-hr-tag {
    position: absolute;
    top: -10px;
    right: -10px;
    background: linear-gradient(135deg, var(--primary-color), #36d1dc);
    color: white;
    border-radius: 20px;
    padding: 3px 10px;
    font-size: 12px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transform: scale(0.9);
    transition: all 0.3s ease;
    z-index: 2;
    display: flex;
    align-items: center;
    gap: 5px;
}

.classification-card:hover .virtual-hr-tag {
    transform: scale(1);
}

.virtual-hr-tag i {
    font-size: 10px;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        opacity: 0.6;
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0.6;
    }
}

/* 卡片中部 - 统计数据 */
.classification-middle {
    margin-bottom: 12px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
}

.classification-stat {
    background-color: #f8f9fa;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    color: #555;
    display: flex;
    align-items: center;
    border: 1px solid #eaeaea;
}

.classification-stat i {
    margin-right: 5px;
    color: var(--primary-color);
}

/* 卡片底部 - 操作按钮 */
.classification-bottom {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.classification-action-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border: 1px solid #eaeaea;
    cursor: pointer;
    transition: all 0.2s;
    color: #666;
}

.classification-action-btn:hover {
    background-color: #e9ecef;
}

.classification-action-btn.view-details-btn:hover {
    color: var(--primary-color);
}

.classification-action-btn.edit-btn:hover {
    color: var(--success-color);
}

.classification-action-btn.delete-btn:hover {
    color: var(--danger-color);
}

/* 页面头部样式 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.page-header h2 {
    margin: 0;
}

/* 搜索和过滤区域样式 */
.filter-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 15px;
}

.search-box {
    display: flex;
    gap: 10px;
    flex: 1;
    min-width: 280px;
}

.search-box .form-control {
    flex: 1;
}

.filter-options {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.filter-options .form-group {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 0;
}

.filter-options .form-label {
    margin-bottom: 0;
    white-space: nowrap;
}

/* 详情样式 - 新的简洁设计 */
.detail-section {
    margin-bottom: 20px;
}

.detail-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 5px;
    color: #333;
}

.detail-content {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    position: relative;
}

.detail-row {
    display: flex;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #f5f5f5;
    animation: fadeInUp 0.5s forwards;
    opacity: 0;
}

.detail-row:nth-child(1) {
    animation-delay: 0.1s;
}

.detail-row:nth-child(2) {
    animation-delay: 0.2s;
}

.detail-row:nth-child(3) {
    animation-delay: 0.3s;
    border-bottom: none;
}

.detail-label {
    width: 100px;
    font-weight: 600;
    color: #666;
}

.detail-value {
    flex: 1;
    color: #333;
}

.virtual-hr-badge {
    position: absolute;
    top: -15px;
    right: 20px;
    background: linear-gradient(135deg, var(--primary-color), #36d1dc);
    color: white;
    border-radius: 30px;
    padding: 8px 15px;
    font-size: 14px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    gap: 8px;
    animation: slideIn 0.5s forwards;
    opacity: 0;
    transform: translateY(-10px);
}

.virtual-hr-badge i {
    animation: pulse 1.5s infinite;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    0% {
        opacity: 0;
        transform: translateY(-20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 空状态和加载状态 */
.classification-empty {
    text-align: center;
    padding: 40px 20px;
}

.classification-empty i {
    font-size: 48px;
    color: #ddd;
    margin-bottom: 20px;
}

.classification-empty p {
    font-size: 16px;
    color: #888;
    margin-bottom: 20px;
}

.classification-list-loading {
    text-align: center;
    padding: 30px 0;
    font-size: 16px;
    color: #888;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    overflow-y: auto;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modal.show {
    opacity: 1;
}

.modal-content {
    background-color: #fff;
    margin: 5% auto;
    width: 90%;
    max-width: 600px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    position: relative;
    transform: translateY(-30px);
    opacity: 0;
    transition: all 0.3s ease;
}

.modal-content-large {
    max-width: 1000px;
    width: 95%;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal.show .modal-content {
    transform: translateY(0);
    opacity: 1;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #eee;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    color: var(--secondary-color, #2c3e50);
}

.modal-close {
    font-size: 24px;
    font-weight: bold;
    color: #aaa;
    cursor: pointer;
}

.modal-close:hover {
    color: #333;
}

.modal-body {
    padding: 20px;
    max-height: calc(100vh - 200px);
    overflow-y: auto;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
    .classification-card {
        width: calc(33.333% - 14px);
    }
}

@media (max-width: 992px) {
    .classification-card {
        width: calc(50% - 10px);
    }
}

@media (max-width: 576px) {
    .classification-card {
        width: 100%;
    }
}

@media (max-width: 768px) {
    .filter-container {
        flex-direction: column;
    }
    
    .search-box, .filter-options {
        width: 100%;
    }
    
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .page-header button {
        align-self: flex-end;
    }
}

/* 详情弹窗折叠式布局样式 */
.modal-body {
    overflow-y: auto;
    flex: 1;
    background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
    padding: 20px;
}

.detail-section {
    background: white;
    border: 1px solid rgba(102, 126, 234, 0.1);
    border-radius: 16px;
    margin-bottom: 20px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.08);
    transition: all 0.3s ease;
}

.detail-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(102, 126, 234, 0.15);
}

.detail-section-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 14px 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: all 0.3s ease;
    user-select: none;
    position: relative;
    overflow: hidden;
    border-radius: 8px 8px 0 0;
}

.detail-section-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.detail-section-header:hover::before {
    left: 100%;
}

.detail-section-header i:first-child {
    color: rgba(255, 255, 255, 0.9);
    font-size: 18px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.detail-section-header span {
    flex: 1;
    font-weight: 600;
    font-size: 16px;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}



.toggle-icon {
    color: rgba(255, 255, 255, 0.8);
    transition: transform 0.3s ease;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.detail-section-content {
    padding: 24px;
    background: white;
    max-height: 500px;
    overflow-y: auto;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.detail-section-content.collapsed {
    max-height: 0;
    padding: 0 24px;
    overflow: hidden;
}

.empty-state {
    text-align: center;
    color: #a0aec0;
    font-style: italic;
    padding: 30px;
    background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
    border-radius: 12px;
    border: 1px dashed rgba(102, 126, 234, 0.2);
    margin: 10px 0;
}

/* 数据列表样式 */
.data-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.data-item {
    border: 1px solid rgba(102, 126, 234, 0.1);
    border-radius: 12px;
    overflow: hidden;
    background: white;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.05);
}

.data-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.1);
    border-color: rgba(102, 126, 234, 0.2);
}

.data-item-header {
    background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
    padding: 16px 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    border-bottom: 1px solid rgba(102, 126, 234, 0.1);
}

.data-item-header i {
    color: #667eea;
    font-size: 16px;
}

.data-item-title {
    font-weight: 600;
    color: #2d3748;
    flex: 1;
    font-size: 15px;
}

.data-item-content {
    padding: 20px;
    background: white;
}

.data-item-info {
    display: flex;
    margin-bottom: 8px;
}

.data-item-info:last-child {
    margin-bottom: 0;
}

.info-label {
    font-weight: 500;
    color: #6c757d;
    min-width: 80px;
}

.info-value {
    color: #333;
    flex: 1;
}

/* 徽章样式 */
.badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.badge-primary {
    background: #007bff;
    color: white;
}

.badge-success {
    background: #28a745;
    color: white;
}

.badge-secondary {
    background: #6c757d;
    color: white;
}

/* 场景问题样式 */
.scene-questions {
    margin-top: 10px;
}

.questions-toggle {
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    color: #007bff;
    font-size: 14px;
    padding: 5px 0;
    user-select: none;
}

.questions-toggle:hover {
    color: #0056b3;
}

.questions-list {
    margin-top: 10px;
    border-left: 3px solid #e9ecef;
    padding-left: 15px;
    max-height: 200px;
    overflow-y: auto;
    transition: all 0.3s ease;
}

.questions-list.collapsed {
    max-height: 0;
    overflow: hidden;
    margin-top: 0;
    padding-left: 0;
}

.question-item {
    padding: 12px 16px;
    border-bottom: 1px solid rgba(102, 126, 234, 0.1);
    background: linear-gradient(135deg, #fafbff 0%, #f5f7ff 100%);
    margin-bottom: 8px;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.question-item:hover {
    background: linear-gradient(135deg, #f0f4ff 0%, #e8ecff 100%);
    transform: translateX(4px);
}

.question-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.question-text {
    font-size: 14px;
    color: #2d3748;
    margin-bottom: 6px;
    font-weight: 500;
    line-height: 1.4;
}

.question-score {
    font-size: 12px;
    color: #667eea;
    font-weight: 600;
    display: inline-block;
    background: rgba(102, 126, 234, 0.1);
    padding: 2px 8px;
    border-radius: 12px;
    margin-right: 8px;
}

.question-seq {
    font-size: 12px;
    color: #a0aec0;
    display: inline-block;
}

/* 虚拟HR卡片样式 */
.virtual-hr-card {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    color: white;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
}

.virtual-hr-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.virtual-hr-card:hover::before {
    left: 100%;
}

.virtual-hr-avatar {
    width: 64px;
    height: 64px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.virtual-hr-info {
    flex: 1;
}

.virtual-hr-info .detail-row {
    margin-bottom: 8px;
}

.virtual-hr-info .detail-label {
    color: rgba(255, 255, 255, 0.8);
}

.virtual-hr-info .detail-value {
    color: white;
    font-weight: 500;
}

/* QA问题对样式 */
.qa-pair {
    background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(102, 126, 234, 0.1);
    transition: all 0.3s ease;
}

.qa-pair:hover {
    background: linear-gradient(135deg, #f0f4ff 0%, #e8ecff 100%);
    border-color: rgba(102, 126, 234, 0.2);
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.1);
}

.qa-question, .qa-answer {
    display: flex;
    margin-bottom: 12px;
    align-items: flex-start;
}

.qa-answer {
    margin-bottom: 0;
}

.qa-label {
    font-weight: 700;
    color: #667eea;
    min-width: 32px;
    margin-right: 12px;
    font-size: 16px;
    text-shadow: 0 1px 2px rgba(102, 126, 234, 0.1);
}

.qa-text {
    flex: 1;
    color: #2d3748;
    line-height: 1.5;
    font-size: 14px;
}

/* 用户名片网格样式 */
.user-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
    padding: 8px 0;
}

.user-card {
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
    border: 1px solid #e1e8ff;
    border-radius: 12px;
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.user-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.user-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
    border-color: #667eea;
}

.user-card:hover::before {
    transform: scaleX(1);
}

.user-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.avatar-text {
    color: white;
    font-weight: 600;
    font-size: 18px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.user-info {
    flex: 1;
    min-width: 0;
}

.user-name {
    font-weight: 600;
    font-size: 16px;
    color: #2d3748;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-phone, .user-date {
    font-size: 13px;
    color: #718096;
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-phone i, .user-date i {
    font-size: 11px;
    color: #a0aec0;
    width: 12px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .user-cards-grid {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
        gap: 12px;
    }

    .user-card {
        padding: 12px;
    }

    .user-avatar {
        width: 40px;
        height: 40px;
    }

    .avatar-text {
        font-size: 16px;
    }
}