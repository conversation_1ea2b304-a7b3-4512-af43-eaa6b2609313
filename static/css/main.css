/* 全局样式 */
:root {
  --primary-color: #3498db;
  --secondary-color: #2c3e50;
  --success-color: #2ecc71;
  --danger-color: #e74c3c;
  --warning-color: #f39c12;
  --light-color: #ecf0f1;
  --dark-color: #34495e;
  --text-color: #333;
  --text-light: #7f8c8d;
  --border-color: #ddd;
  --bg-light: #f8f9fa;
  --shadow: 0 2px 5px rgba(0,0,0,0.1);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 14px;
  line-height: 1.6;
  color: var(--text-color);
  background-color: var(--bg-light);
}

a {
  color: var(--primary-color);
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

/* 布局 */
.app-wrapper {
  display: flex;
  min-height: 100vh;
}

.sidebar {
  width: 250px;
  background-color: var(--secondary-color);
  color: white;
  padding: 20px 0;
  position: fixed;
  height: 100vh;
  overflow-y: auto;
  transition: all 0.3s;
}

.sidebar-collapsed {
  width: 70px;
}

.sidebar-header {
  padding: 10px 20px;
  border-bottom: 1px solid rgba(255,255,255,0.1);
}

.sidebar-menu {
  padding: 20px 0;
}

.sidebar-menu-item {
  padding: 10px 20px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.sidebar-menu-item:hover {
  background-color: rgba(255,255,255,0.1);
}

.sidebar-menu-item i {
  margin-right: 10px;
  font-size: 18px;
}

.sidebar-menu-item.active {
  background-color: var(--primary-color);
}

/* 子菜单样式 */
.sidebar-menu-item.has-submenu {
  position: relative;
  cursor: pointer;
}

.submenu-arrow {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  transition: transform 0.3s ease;
  font-size: 12px;
}

.sidebar-menu-item.has-submenu.open .submenu-arrow {
  transform: translateY(-50%) rotate(180deg);
}

.sidebar-submenu {
  display: none;
  background-color: rgba(0,0,0,0.2);
  overflow: hidden;
  max-height: 0;
  transition: max-height 0.3s ease;
}

.sidebar-submenu.show {
  display: block;
  max-height: 300px; /* Adjust this value based on your content */
}

.sidebar-submenu .sidebar-menu-item {
  padding-left: 40px;
}

.sidebar-submenu .sidebar-menu-item i {
  font-size: 14px;
}

/* 其他样式 */
.main-content {
  flex: 1;
  margin-left: 250px;
  transition: all 0.3s;
}

.main-content-expanded {
  margin-left: 70px;
}

.topbar {
  height: 60px;
  background-color: white;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  padding: 0 20px;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: var(--shadow);
}

.topbar-toggle {
  font-size: 20px;
  cursor: pointer;
  margin-right: 20px;
}

.topbar-title {
  font-size: 18px;
  font-weight: 600;
  flex: 1;
}

.topbar-actions {
  display: flex;
  align-items: center;
}

.content {
  padding: 20px;
}

/* 卡片组件 */
.card {
  background-color: white;
  border-radius: 5px;
  box-shadow: var(--shadow);
  margin-bottom: 20px;
  overflow: hidden;
}

.card-header {
  padding: 15px 20px;
  border-bottom: 1px solid var(--border-color);
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-body {
  padding: 20px;
}

.card-footer {
  padding: 15px 20px;
  border-top: 1px solid var(--border-color);
  background-color: var(--bg-light);
}

/* 按钮 */
.btn {
  display: inline-block;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  border: none;
  font-size: 14px;
  transition: all 0.2s;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: #2980b9;
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-secondary:hover {
  background-color: #233140;
}

.btn-success {
  background-color: var(--success-color);
  color: white;
}

.btn-success:hover {
  background-color: #27ae60;
}

.btn-danger {
  background-color: var(--danger-color);
  color: white;
}

.btn-danger:hover {
  background-color: #c0392b;
}

.btn-light {
  background-color: var(--light-color);
  color: var(--text-color);
}

.btn-light:hover {
  background-color: #bdc3c7;
}

/* 表单 */
.form-group {
  margin-bottom: 15px;
}

.form-label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid var(--border-color);
  font-size: 14px;
}

.form-control:focus {
  border-color: var(--primary-color);
  outline: none;
}

/* 全局搜索框样式 */
.search-box {
  display: flex;
  flex: 1;
  min-width: 250px;
  max-width: 500px;
}

.search-box input {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  flex: 1;
}

.search-box button {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  white-space: nowrap;
  padding: 8px 15px;
  min-width: 80px;
}

/* 表格 */
.table {
  width: 100%;
  border-collapse: collapse;
}

.table th, .table td {
  padding: 12px 15px;
  border-bottom: 1px solid var(--border-color);
  text-align: left;
}

.table th {
  font-weight: 600;
  background-color: var(--bg-light);
}

.table tr:hover {
  background-color: rgba(0,0,0,0.02);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    width: 70px;
  }
  
  .sidebar.active {
    width: 250px;
  }
  
  .main-content {
    margin-left: 70px;
  }
  
  .main-content.active {
    margin-left: 250px;
  }
}

/* 工具类 */
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.mb-10 {
  margin-bottom: 10px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-10 {
  margin-top: 10px;
}

.mt-20 {
  margin-top: 20px;
}

.badge {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.badge-primary {
  background-color: var(--primary-color);
  color: white;
}

.badge-success {
  background-color: var(--success-color);
  color: white;
}

.badge-warning {
  background-color: var(--warning-color);
  color: white;
}

.badge-danger {
  background-color: var(--danger-color);
  color: white;
}

/* 动画 */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 通知样式 */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 4px;
  background-color: white;
  box-shadow: 0 3px 10px rgba(0,0,0,0.2);
  z-index: 9999;
  transition: opacity 0.3s, transform 0.3s;
  opacity: 1;
  transform: translateY(0);
  max-width: 400px;
  word-wrap: break-word;
}

.notification-info {
  background-color: var(--primary-color);
  color: white;
}

.notification-success {
  background-color: var(--success-color);
  color: white;
}

.notification-warning {
  background-color: var(--warning-color);
  color: white;
}

.notification-error {
  background-color: var(--danger-color);
  color: white;
  font-weight: 500;
}

.notification-hide {
  opacity: 0;
  transform: translateY(-20px);
}

/* 格式化日期 */

/* QA引擎样式 */
.classification-tenant {
  font-size: 12px;
  color: var(--text-light);
  margin-top: 5px;
  padding: 2px 6px;
  background-color: var(--bg-light);
  border-radius: 3px;
  border: 1px solid var(--border-color);
}