/* 分类器页面样式 */
.classifier-panels {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

@media (max-width: 992px) {
    .classifier-panels {
        grid-template-columns: 1fr;
    }
}

.table-container {
    flex: 1;
    min-height: 200px;
    overflow: visible;
    width: 100%;
}

.table-responsive {
    overflow-x: auto;
    width: 100%;
}

.btn-action {
    margin-right: 5px;
    padding: 5px 10px;
    font-size: 0.8em;
}

.btn-edit {
    background-color: #4caf50;
    color: white;
}

.btn-delete {
    background-color: #f44336;
    color: white;
}

/* 通知样式 */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.notification {
    padding: 12px 15px;
    border-radius: 4px;
    min-width: 250px;
    max-width: 400px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    animation: slide-in 0.3s ease;
    color: white;
}

.notification-success {
    background-color: #4caf50;
}

.notification-error {
    background-color: #f44336;
}

.notification-info {
    background-color: #2196f3;
}

.notification-content {
    margin-right: 10px;
}

@keyframes slide-in {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background-color: #fff;
    border-radius: 5px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
}

.modal-close {
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
}

.modal-body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 15px 20px;
    border-top: 1px solid var(--border-color);
}

.classifier-panels .card {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.classifier-panels .card-body {
    flex: 1;
    overflow: auto;
}

/* 设置固定高度，确保两边标题栏一致 */
.classifier-panels .card-header {
    height: 60px;
    display: flex;
    align-items: center;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.page-header h2 {
    margin: 0;
}

/* 提示词相关样式 */
.prompt-section {
    margin-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 15px;
}

.prompt-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
}

textarea.form-control {
    resize: vertical;
    min-height: 80px;
    font-family: monospace;
    font-size: 14px;
} 