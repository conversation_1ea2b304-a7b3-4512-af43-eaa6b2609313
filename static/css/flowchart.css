/* 流程图样式 */
.flowchart-container {
    position: relative;
    width: 100%;
    height: 100%;
    background-color: #fafafa;
    background-image: 
        linear-gradient(rgba(0,0,0,.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0,0,0,.1) 1px, transparent 1px);
    background-size: 20px 20px;
    overflow: hidden;
    border-radius: 4px;
}

/* X6画布容器 */
#x6-container {
    width: 100%;
    height: 100%;
    min-height: 600px;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

/* 工具栏样式 */
.flowchart-toolbar {
    display: flex;
    gap: 8px;
    align-items: center;
}

.flowchart-toolbar .btn {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 4px;
    transition: all 0.2s;
}

.flowchart-toolbar .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 左侧场景列表样式 */
.scenes-sidebar {
    position: absolute;
    left: 0;
    top: 0;
    width: 280px;
    height: 100%;
    background: white;
    border-right: 1px solid #e0e0e0;
    box-shadow: 2px 0 8px rgba(0,0,0,0.1);
    z-index: 10;
    transition: transform 0.3s ease;
}

.scenes-sidebar.collapsed {
    transform: translateX(-240px);
}

.scenes-sidebar-header {
    padding: 15px;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.scenes-sidebar-header h4 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.sidebar-toggle {
    background: none;
    border: none;
    padding: 4px;
    cursor: pointer;
    color: #666;
    border-radius: 3px;
    transition: all 0.2s;
}

.sidebar-toggle:hover {
    background: #e9ecef;
    color: #333;
}

.scenes-sidebar-body {
    padding: 0;
    height: calc(100% - 60px);
    overflow-y: auto;
}

/* 可拖拽场景项样式 */
.draggable-scene-item {
    border-bottom: 1px solid #f0f0f0;
    background: white;
    transition: all 0.2s;
}

.scene-item-header {
    padding: 12px 15px;
    cursor: pointer;
    display: flex;
    align-items: flex-start;
    gap: 8px;
}

.scene-item-toggle {
    margin-top: 2px;
    cursor: pointer;
    color: #666;
    transition: transform 0.2s;
    flex-shrink: 0;
}

.scene-item-toggle.expanded {
    transform: rotate(90deg);
}

.scene-item-info {
    flex: 1;
    min-width: 0;
}

.draggable-scene-item:hover {
    background: #f8f9fa;
    border-left: 3px solid var(--primary-color);
}



.scene-item-name {
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
}

.scene-item-desc {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

.scene-item-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
    font-size: 11px;
    color: #999;
}

.scene-item-default {
    background: #e3f2fd;
    color: #1976d2;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 10px;
}

/* 场景项内容区域 */
.scene-item-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background: #f8f9fa;
}

.scene-item-content.expanded {
    max-height: 300px;
    overflow-y: auto;
}

.scene-item-questions {
    padding: 10px 15px;
}

.sidebar-question-item {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 8px;
    margin-bottom: 6px;
    font-size: 12px;
    display: flex;
    align-items: flex-start;
    gap: 8px;
    transition: all 0.2s;
}

.sidebar-question-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.question-drag-handle {
    color: #999;
    cursor: move;
    padding: 2px;
    flex-shrink: 0;
}

.question-info {
    flex: 1;
    min-width: 0;
}

.question-actions {
    display: flex;
    gap: 4px;
    flex-shrink: 0;
}

.question-action-btn {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 2px 4px;
    border-radius: 2px;
    font-size: 11px;
    transition: all 0.2s;
}

.question-action-btn:hover {
    background: #f0f0f0;
    color: #333;
}

.question-action-btn.delete-question:hover {
    background: #ffebee;
    color: #d32f2f;
}

.question-content {
    color: #333;
    margin-bottom: 4px;
    line-height: 1.4;
}

.question-score {
    color: #666;
    font-size: 11px;
}

.scene-item-actions {
    padding: 10px 15px;
    border-top: 1px solid #e0e0e0;
}

.no-questions, .error-message, .questions-loading {
    text-align: center;
    color: #999;
    font-size: 12px;
    padding: 20px;
}

/* 场景节点样式 */
.scene-node {
    background: white;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.2s;
}

.scene-node:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.scene-node.selected {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
}

.scene-node.default {
    border-color: #4caf50;
    background: linear-gradient(135deg, #e8f5e8 0%, #ffffff 100%);
}

.scene-node-header {
    padding: 8px 12px;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    border-radius: 6px 6px 0 0;
    font-weight: 600;
    font-size: 12px;
    color: #333;
}

.scene-node.default .scene-node-header {
    background: #4caf50;
    color: white;
}

.scene-node-body {
    padding: 12px;
}

.scene-node-title {
    font-weight: 600;
    font-size: 14px;
    color: #333;
    margin-bottom: 4px;
}

.scene-node-desc {
    font-size: 11px;
    color: #666;
    line-height: 1.4;
    margin-bottom: 8px;
}

.scene-node-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 10px;
    color: #999;
}

.scene-node-score {
    background: #f0f0f0;
    padding: 2px 6px;
    border-radius: 10px;
}

/* 连线样式 */
.x6-edge-stroke {
    stroke: #666;
    stroke-width: 2;
}

.x6-edge-stroke:hover {
    stroke: var(--primary-color);
    stroke-width: 3;
}

.x6-edge.selected .x6-edge-stroke {
    stroke: var(--primary-color);
    stroke-width: 3;
}

/* 连线标签样式 */
.edge-label {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 11px;
    color: #666;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.edge-label.score-condition {
    background: #fff3e0;
    border-color: #ff9800;
    color: #f57c00;
}

.edge-label.semantic-condition {
    background: #e8f5e8;
    border-color: #4caf50;
    color: #2e7d32;
}

/* 右键菜单样式 */
.context-menu {
    position: absolute;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    padding: 4px 0;
    min-width: 120px;
    z-index: 10002; /* 提高z-index，确保在全屏模式下也能显示 */
}

.context-menu-item {
    padding: 8px 16px;
    font-size: 12px;
    color: #333;
    cursor: pointer;
    transition: background 0.2s;
}

.context-menu-item:hover {
    background: #f8f9fa;
}

.context-menu-item.danger {
    color: #d32f2f;
}

.context-menu-item.danger:hover {
    background: #ffebee;
}

.context-menu-divider {
    height: 1px;
    background: #e0e0e0;
    margin: 4px 0;
}

/* 加载状态 */
.flowchart-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #666;
}

.flowchart-loading .spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f0f0f0;
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 12px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 空状态 */
.flowchart-empty {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #999;
}

.flowchart-empty-icon {
    font-size: 48px;
    color: #ddd;
    margin-bottom: 16px;
}

.flowchart-empty-text {
    font-size: 14px;
    margin-bottom: 8px;
}

.flowchart-empty-hint {
    font-size: 12px;
    color: #bbb;
}

/* 列表视图样式 */
.scenes-list-view {
    padding: 20px;
    height: 100%;
    overflow-y: auto;
    background: #f8f9fa;
}

.scenes-list-view .scenes-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

/* 优化场景项在列表视图中的显示 */
.scenes-list-view .scene-item {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: all 0.2s;
}

.scenes-list-view .scene-item:hover {
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.scenes-list-view .scene-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
}

.scenes-list-view .scene-header .scene-title h4 {
    color: white;
    font-size: 18px;
    margin: 0;
}

.scenes-list-view .scene-actions .scene-action {
    background: rgba(255,255,255,0.2);
    color: white;
    border-radius: 4px;
    padding: 8px;
    transition: all 0.2s;
}

.scenes-list-view .scene-actions .scene-action:hover {
    background: rgba(255,255,255,0.3);
}

.scenes-list-view .scene-content {
    padding: 0;
}

.scenes-list-view .scene-info {
    padding: 20px;
}

.scenes-list-view .scene-questions {
    border-top: 1px solid #f0f0f0;
    background: #fafafa;
}

.scenes-list-view .scene-questions-header {
    padding: 15px 20px;
    background: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
    font-weight: 600;
    color: #333;
}

.scenes-list-view .questions-list {
    padding: 15px 20px;
}

.scenes-list-view .add-question-btn {
    margin: 15px 20px 20px;
    padding: 12px;
    background: #e3f2fd;
    color: #1976d2;
    border: 2px dashed #1976d2;
    border-radius: 6px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s;
}

.scenes-list-view .add-question-btn:hover {
    background: #bbdefb;
    border-color: #1565c0;
}

/* 视图切换按钮组 */
.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
}

.btn-group .btn:last-child {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}

.btn-group .btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* 全屏模式 */
.scenes-panel.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    background: white;
}

/* 确保全屏模式下弹窗能正常显示 */
.scenes-panel.fullscreen ~ .modal {
    z-index: 10000 !important;
}

.scenes-panel.fullscreen ~ .modal .modal-dialog {
    z-index: 10001 !important;
}

.scenes-panel.fullscreen .panel-header {
    padding: 15px 20px;
    border-bottom: 1px solid #e0e0e0;
}

.scenes-panel.fullscreen .flowchart-container {
    height: calc(100vh - 80px);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .scenes-sidebar {
        width: 100%;
        transform: translateX(-100%);
    }

    .scenes-sidebar.show {
        transform: translateX(0);
    }

    .flowchart-toolbar {
        flex-wrap: wrap;
    }

    .flowchart-toolbar .btn {
        font-size: 11px;
        padding: 4px 8px;
    }
}
