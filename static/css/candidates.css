/* 候选人管理页面样式 */

/* 页面头部 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.page-header h2 {
    margin: 0;
    color: var(--secondary-color);
}

/* 搜索和筛选 */
.filter-container {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.search-box {
    display: flex;
    flex: 1;
    min-width: 250px;
    max-width: 500px;
}

.search-box input {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    flex: 1;
}

.search-box button {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    white-space: nowrap;
    padding: 8px 15px;
    min-width: 80px;
}

.filter-options {
    display: flex;
    gap: 15px;
}

.filter-options .form-group {
    margin-bottom: 0;
}

/* 候选人列表 */
.candidate-list {
    min-height: 300px;
}

.candidate-list-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;
}

/* Redesigned Candidate Card */
.candidate-card {
    margin-bottom: 15px;
    border-radius: 8px;
    background-color: white;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    transition: all 0.2s;
    overflow: hidden;
    position: relative;
}

.candidate-card:hover {
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.candidate-card-inner {
    display: flex;
    padding: 18px;
    position: relative;
}

/* Source Tag Styling */
.candidate-source-tag {
    position: absolute;
    top: 0;
    right: 0;
    font-size: 12px;
    padding: 4px 10px;
    border-radius: 0 8px 0 8px;
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
    background-color: rgba(0, 0, 0, 0.08);
    color: rgba(0, 0, 0, 0.7);
}

.candidate-source-tag[data-source="boss"] {
    background-color: rgba(255, 119, 0, 0.15);
    color: rgba(166, 77, 0, 0.9);
}

.candidate-source-tag[data-source="wechat"] {
    background-color: rgba(9, 187, 7, 0.15);
    color: rgba(7, 130, 5, 0.9);
}

/* Candidate Header */
.candidate-header {
    margin-bottom: 10px;
}

.candidate-name {
    font-size: 18px;
    font-weight: 600;
    color: var(--secondary-color);
    margin-bottom: 5px;
    display: flex;
    align-items: center;
}

.candidate-title-tag {
    margin-left: 10px;
    font-size: 12px;
    font-weight: normal;
    color: var(--text-light);
    background-color: var(--bg-light);
    padding: 2px 8px;
    border-radius: 10px;
}

.candidate-meta {
    display: flex;
    gap: 15px;
    font-size: 13px;
    color: var(--text-light);
}

.candidate-meta i {
    margin-right: 4px;
}

.candidate-details {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 10px;
    margin-bottom: 10px;
}

.candidate-detail-item {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: var(--text-color);
    background-color: #f8f9fa;
    padding: 2px 6px;
    border-radius: 4px;
    border: 1px solid #e9ecef;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.candidate-detail-item i {
    margin-right: 6px;
    color: var(--primary-color);
    font-size: 12px;
    width: 16px;
    text-align: center;
}

/* Candidate Tags */
.candidate-tag-container {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 15px;
    gap: 10px;
}

.candidate-tag {
    display: inline-flex;
    align-items: center;
    padding: 5px 10px;
    border-radius: 4px;
    background-color: var(--bg-light);
    font-size: 0.85rem;
}

.candidate-tag i {
    margin-right: 5px;
    color: var(--primary-color);
}

.candidate-position {
    background-color: #e3f2fd;
    color: #0d47a1;
}

.candidate-position i {
    color: #1976d2;
}

.candidate-source {
    background-color: #f3e5f5;
    color: #6a1b9a;
}

.candidate-source i {
    color: #8e24aa;
}

.candidate-phone {
    background-color: #e8f5e9;
    color: #1b5e20;
}

.candidate-phone i {
    color: #388e3c;
}

.candidate-wechat {
    background-color: #e6f8e6;
    color: #0d5e0d;
}

.candidate-wechat i {
    color: #07c160;
}

.candidate-education {
    background-color: #fff8e1;
    color: #ff6f00;
}

.candidate-education i {
    color: #ff8f00;
}

.candidate-realname-tag {
    margin-left: 10px;
    font-size: 12px;
    font-weight: normal;
    color: #616161;
    background-color: #f5f5f5;
    padding: 2px 8px;
    border-radius: 10px;
}

/* Candidate Actions */
.candidate-actions {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    margin-left: auto;
    padding-left: 15px;
    min-width: 120px;
}

.candidate-score-display {
    text-align: center;
    margin-bottom: 15px;
}

.candidate-buttons {
    display: flex;
    gap: 5px;
}

/* Profile Title Tag */
.candidate-profile-title-tag {
    margin-left: 10px;
    font-size: 14px;
    font-weight: normal;
    color: var(--text-light);
    background-color: var(--bg-light);
    padding: 2px 8px;
    border-radius: 10px;
}

/* Resume Text Preview */
.resume-text-preview {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    border: 1px solid #e9ecef;
    max-height: 300px;
    overflow-y: auto;
}

.resume-text-preview pre {
    margin: 0;
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: inherit;
    font-size: 14px;
    line-height: 1.5;
}

.resume-link a {
    display: inline-flex;
    align-items: center;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.resume-link a i {
    margin-right: 5px;
}

/* Source Tag in Details */
.source-tag {
    display: inline-flex;
    align-items: center;
    padding: 4px 10px;
    border-radius: 4px;
    font-size: 13px;
    text-transform: uppercase;
    font-weight: 600;
    background-color: rgba(0, 0, 0, 0.08);
    color: rgba(0, 0, 0, 0.7);
}

.source-tag[data-source="boss"] {
    background-color: rgba(255, 119, 0, 0.15);
    color: rgba(166, 77, 0, 0.9);
}

.source-tag[data-source="wechat"] {
    background-color: rgba(9, 187, 7, 0.15);
    color: rgba(7, 130, 5, 0.9);
}

.candidate-avatar {
    width: 80px;
    height: 80px;
    padding: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.avatar-placeholder {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
}

.candidate-info {
    flex: 1;
    padding: 15px;
    border-left: 1px solid var(--border-color);
}

.candidate-gender {
    margin-left: 8px;
    font-size: 14px;
    color: var(--text-light);
}

.candidate-creation-time {
    margin-top: 10px;
    font-size: 13px;
    color: var(--text-light);
    display: flex;
    align-items: center;
}

.candidate-creation-time i {
    margin-right: 5px;
    width: 16px;
    text-align: center;
}

.candidate-score {
    font-size: 24px;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.candidate-score-label {
    font-size: 12px;
    color: var(--text-light);
    margin-bottom: 10px;
}

.candidate-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    color: var(--text-light);
}

.candidate-empty i {
    font-size: 48px;
    margin-bottom: 15px;
}

/* 分页 */
.pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.pagination {
    display: flex;
    list-style: none;
    padding: 0;
}

.pagination-item {
    margin: 0 5px;
}

.pagination-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 35px;
    height: 35px;
    border-radius: 4px;
    color: var(--text-color);
    background-color: white;
    border: 1px solid var(--border-color);
    cursor: pointer;
}

.pagination-link:hover {
    background-color: var(--bg-light);
}

.pagination-link.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination-link.disabled {
    color: var(--text-light);
    cursor: not-allowed;
}

/* Enhanced Modal Styling */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: 1000;
    overflow: auto;
    animation: modalFadeIn 0.3s;
}

/* 确保编辑模态框显示在详情模态框上方 */
#candidateModal {
    z-index: 1100;
}

.modal-content {
    background-color: #fff;
    margin: 4% auto;
    padding: 0;
    width: 80%;
    max-width: 900px;
    border-radius: 8px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25);
    animation: slideIn 0.3s;
}

/* 候选人表单模态框样式 */
.candidate-form-modal {
    max-width: 1000px;
}

.form-row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
}

.form-col {
    flex: 0 0 50%;
    max-width: 50%;
    padding-right: 15px;
    padding-left: 15px;
}

.form-group {
    margin-bottom: 20px;
    position: relative;
}

.form-group:last-child {
    margin-bottom: 10px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    font-size: 14px;
    color: var(--secondary-color);
}

.form-control {
    display: block;
    width: 100%;
    padding: 8px 12px;
    font-size: 14px;
    line-height: 1.5;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

/* 用户痛点字段样式 */
.concerns-container {
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 10px;
    background-color: #fafafa;
}

.concerns-list {
    margin-bottom: 10px;
    min-height: 40px;
}

.concern-item {
    display: inline-flex;
    align-items: center;
    background-color: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 16px;
    padding: 4px 12px;
    margin: 2px 4px 2px 0;
    font-size: 13px;
    color: #1976d2;
}

.concern-item .concern-text {
    margin-right: 6px;
}

.concern-item .remove-concern {
    background: none;
    border: none;
    color: #1976d2;
    cursor: pointer;
    padding: 0;
    font-size: 12px;
    opacity: 0.7;
    transition: opacity 0.2s;
}

.concern-item .remove-concern:hover {
    opacity: 1;
}

.concerns-input-group {
    display: flex;
    gap: 8px;
    align-items: center;
}

.concerns-input-group .form-control {
    flex: 1;
    margin-bottom: 0;
}

.concerns-input-group .btn {
    white-space: nowrap;
    flex-shrink: 0;
}

.concerns-empty {
    color: #999;
    font-style: italic;
    font-size: 13px;
    padding: 8px 0;
}

/* 详情页面痛点显示样式 */
.concerns-display {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.concern-tag {
    display: inline-block;
    background-color: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 12px;
    padding: 4px 10px;
    font-size: 12px;
    color: #1976d2;
    white-space: nowrap;
}

.form-control:focus {
    border-color: var(--primary-color);
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-group textarea {
    min-height: 100px;
    resize: vertical;
}

#resume_text {
    min-height: 200px;
}

@keyframes modalFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid var(--border-color);
    background-color: #f8f9fa;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

.modal-header h3 {
    margin: 0;
    color: var(--secondary-color);
    font-weight: 600;
}

.modal-close {
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    color: #6c757d;
    transition: color 0.2s;
}

.modal-close:hover {
    color: #343a40;
}

.modal-body {
    padding: 25px;
    max-height: 70vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 15px 25px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    background-color: #f8f9fa;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
}

/* 详情视图 */
.candidate-profile {
    display: flex;
    margin-bottom: 20px;
}

.candidate-profile-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    margin-right: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--primary-color);
    color: white;
    font-size: 36px;
    font-weight: bold;
}

.candidate-profile-info {
    flex: 1;
}

.candidate-profile-name {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 5px;
}

.candidate-profile-title {
    color: var(--text-light);
    margin-bottom: 10px;
}

.candidate-profile-details {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.candidate-profile-detail {
    display: flex;
    align-items: center;
    margin-right: 15px;
}

.candidate-profile-detail i {
    margin-right: 5px;
    color: var(--text-light);
}

.detail-section {
    margin-bottom: 20px;
}

.detail-section h4 {
    margin-top: 0;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid var(--border-color);
    color: var(--secondary-color);
}

/* 两列布局样式 */
.detail-grid {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10px;
}

.detail-column {
    flex: 1;
    min-width: 250px;
    padding: 0 10px;
}

.detail-item {
    margin-bottom: 10px;
    display: flex;
}

.detail-label {
    width: 120px;
    font-weight: 600;
}

.detail-value {
    flex: 1;
}

.detail-value.empty {
    color: var(--text-light);
    font-style: italic;
}

.resume-preview {
    padding: 15px;
    background-color: var(--bg-light);
    border-radius: 5px;
    margin-bottom: 15px;
}

.resume-preview a {
    display: flex;
    align-items: center;
}

.resume-preview i {
    margin-right: 10px;
    font-size: 24px;
}

/* 表单元素 */
.form-control-file {
    padding: 8px 0;
}

small {
    color: var(--text-light);
    font-size: 12px;
    display: block;
    margin-top: 5px;
}

.form-error {
    color: var(--danger-color);
    font-size: 12px;
    margin-top: 5px;
}

.form-control-error {
    border-color: var(--danger-color);
}

/* Enhanced Button Styling */
.btn {
    display: inline-block;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    user-select: none;
    border: 1px solid transparent;
    padding: 8px 16px;
    font-size: 14px;
    line-height: 1.5;
    border-radius: 4px;
    transition: all 0.2s ease-in-out;
    cursor: pointer;
}

.btn:focus, .btn:hover {
    text-decoration: none;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn-primary {
    color: #fff;
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #0069d9;
    border-color: #0062cc;
}

.btn-light {
    color: #212529;
    background-color: #f8f9fa;
    border-color: #f8f9fa;
}

.btn-light:hover {
    background-color: #e2e6ea;
    border-color: #dae0e5;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 3px;
}

.action-btn {
    width: 32px;
    height: 32px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    text-decoration: none;
    color: var(--text-color);
    transition: all 0.2s;
}

.action-btn-view {
    color: var(--primary-color);
}

.action-btn-edit {
    color: var(--success-color);
}

.action-btn-delete {
    color: var(--danger-color);
}

.action-btn:hover {
    background-color: var(--bg-light);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .filter-container {
        flex-direction: column;
    }
    
    .filter-options {
        flex-direction: column;
    }
    
    .candidate-card {
        flex-direction: column;
    }
    
    .candidate-avatar {
        width: 100%;
        height: auto;
        border-bottom: 1px solid var(--border-color);
        border-right: none;
    }
    
    .candidate-actions {
        width: 100%;
        flex-direction: row;
        border-top: 1px solid var(--border-color);
        border-left: none;
    }
    
    .modal-content {
        width: 95%;
        margin: 10% auto;
    }
    
    .candidate-profile {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }
    
    .candidate-profile-avatar {
        margin-right: 0;
        margin-bottom: 15px;
    }
    
    .candidate-profile-details {
        justify-content: center;
    }
} 

/* 评分和场景编辑样式 07081 */
.score-display-container07081,
.scene-display-container07081 {
    display: flex;
    align-items: center;
}

.ml-10 {
    margin-left: 10px;
}

.score-input-group07081,
.scene-input-group07081 {
    display: flex;
    align-items: center;
    gap: 10px;
}

.score-input-group07081 input,
.scene-input-group07081 select {
    width: 100px;
    margin-right: 10px;
} 