/* 定时分析页面样式 */

.content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    gap: 20px;
    padding: 20px;
}

.analysis-config-panel {
    grid-column: 1 / 2;
    grid-row: 1 / 3;
}

.users-panel {
    grid-column: 2 / 3;
    grid-row: 1 / 2;
}

.execution-status-panel {
    grid-column: 2 / 3;
    grid-row: 2 / 3;
}

/* 面板样式 */
.analysis-config-panel,
.users-panel,
.execution-status-panel {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.panel-header {
    background: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.panel-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.panel-body {
    padding: 20px;
    max-height: 600px;
    overflow-y: auto;
}

/* 分析配置卡片 */
.analysis-card {
    border: 1px solid #dee2e6;
    border-radius: 6px;
    margin-bottom: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
}

.analysis-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.analysis-card.selected {
    border: 2px solid #007bff;
    box-shadow: 0 0 20px rgba(0, 123, 255, 0.3);
    transform: translateY(-3px);
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.05) 0%, rgba(0, 123, 255, 0.02) 100%);
}

.analysis-card-header {
    background: #f8f9fa;
    padding: 12px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.analysis-card-title {
    font-weight: 600;
    color: #333;
    margin: 0;
}

.analysis-card-actions {
    display: flex;
    gap: 8px;
}

.analysis-card-body {
    padding: 15px;
}

.analysis-info {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 10px;
    margin-bottom: 10px;
}

@media (max-width: 1200px) {
    .analysis-info {
        grid-template-columns: 1fr 1fr;
    }
}

@media (max-width: 768px) {
    .analysis-info {
        grid-template-columns: 1fr;
    }
}

.info-item {
    display: flex;
    flex-direction: column;
}

.info-label {
    font-size: 12px;
    color: #666;
    margin-bottom: 2px;
}

.info-value {
    font-size: 14px;
    color: #333;
    font-weight: 500;
}

.analysis-prompt {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    font-size: 13px;
    color: #666;
    max-height: 80px;
    overflow-y: auto;
    margin-top: 10px;
}

/* 状态标签 */
.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.status-badge.active {
    background: #d4edda;
    color: #155724;
}

.status-badge.inactive {
    background: #f8d7da;
    color: #721c24;
}

.status-badge.running {
    background: #fff3cd;
    color: #856404;
}

.status-badge.completed {
    background: #d4edda;
    color: #155724;
}

.status-badge.failed {
    background: #f8d7da;
    color: #721c24;
}

/* 用户列表 */
.user-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    margin-bottom: 10px;
    background: white;
}

.user-info {
    flex: 1;
}

/* .user-name {
    font-weight: 600;
    /* color: #333; */
    /* margin-bottom: 4px;
} */ */

/* .user-details {
    font-size: 13px;
    color: #666;
} */

.user-stats {
    text-align: right;
}

.message-count {
    font-size: 18px;
    font-weight: 600;
    color: #007bff;
}

.message-label {
    font-size: 12px;
    color: #666;
}

/* 执行状态 */
.status-item {
    padding: 12px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    margin-bottom: 10px;
    background: white;
}

.status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    gap: 10px;
    flex-wrap: wrap;
}

.status-header .status-time {
    flex: 1;
    min-width: 0;
}

.status-header .status-badge {
    flex-shrink: 0;
}

.status-header .view-result-btn {
    flex-shrink: 0;
    margin-left: auto;
}

.status-time {
    font-weight: 600;
    color: #333;
}

.status-details {
    font-size: 13px;
    color: #666;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 10px;
}

/* 按钮样式 */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #1e7e34;
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background: #e0a800;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-dialog {
    max-width: 600px;
    width: 90%;
}

.modal-content {
    background: white;
    border-radius: 8px;
    overflow: hidden;
}

.modal-header {
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.btn-close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #666;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
}

.btn-close:hover {
    background: #f8f9fa;
    color: #333;
}

.btn-close:active {
    background: #e9ecef;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 15px 20px;
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* 表单样式 */
.form-group {
    margin-bottom: 15px;
}

.form-label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #333;
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-check {
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-check-input {
    margin: 0;
}

.form-text {
    font-size: 12px;
    color: #666;
    margin-top: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .content {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto;
    }
    
    .analysis-config-panel {
        grid-column: 1;
        grid-row: 1;
    }
    
    .users-panel {
        grid-column: 1;
        grid-row: 2;
    }
    
    .execution-status-panel {
        grid-column: 1;
        grid-row: 3;
    }
    
    .analysis-info {
        grid-template-columns: 1fr;
    }
    
    .status-details {
        grid-template-columns: 1fr;
    }
}

/* Empty State 样式 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
    min-height: 200px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 2px dashed #dee2e6;
    margin: 20px 0;
}

.empty-state i {
    margin-bottom: 20px;
    color: #6c757d;
    opacity: 0.7;
}

.empty-state p {
    color: #6c757d;
    font-size: 16px;
    margin: 0;
    line-height: 1.5;
}

.empty-state .text-muted {
    color: #6c757d !important;
    font-size: 14px;
    margin-top: 8px;
}

/* 实时状态显示样式 */
.realtime-status-item {
    padding: 15px;
    border: 2px solid #007bff;
    border-radius: 8px;
    margin-bottom: 15px;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.05) 0%, rgba(0, 123, 255, 0.02) 100%);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(0, 123, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0);
    }
}

.progress-info {
    margin-top: 12px;
}

.progress-bar-container {
    position: relative;
    background: #e9ecef;
    border-radius: 10px;
    height: 20px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #0056b3);
    border-radius: 10px;
    transition: width 0.5s ease;
    position: relative;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #333;
    font-weight: 600;
    font-size: 12px;
    z-index: 1;
}

.progress-details {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    font-size: 13px;
    color: #666;
}

.progress-details span {
    text-align: center;
    padding: 4px 8px;
    background: rgba(0, 123, 255, 0.1);
    border-radius: 4px;
}

/* 执行结果模态框样式 */
#executionResultModal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1050;
    backdrop-filter: blur(2px);
    animation: fadeIn 0.3s ease;
    overflow-y: auto;
    padding: 20px;
    box-sizing: border-box;
}

#executionResultModal.show {
    display: flex;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

#executionResultModal .modal-dialog {
    max-width: 90%;
    width: 1200px;
    margin: 0 auto;
    max-height: calc(100vh - 40px);
    display: flex;
    flex-direction: column;
    position: relative;
}

#executionResultModal .modal-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    max-height: 100%;
    overflow: hidden;
    animation: slideIn 0.3s ease;
    transform-origin: center;
}

#executionResultModal .modal-header {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

#executionResultModal .modal-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

#executionResultModal .btn-close {
    background: none;
    border: none;
    font-size: 18px;
    color: #6c757d;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

#executionResultModal .btn-close:hover {
    background: #e9ecef;
    color: #333;
}

#executionResultModal .modal-body {
    padding: 0;
    flex: 1;
    overflow-y: auto;
}

#executionResultModal .modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
    border-radius: 0 0 8px 8px;
    display: flex;
    justify-content: flex-end;
}

#executionResultModal .btn {
    padding: 8px 16px;
    border-radius: 4px;
    border: none;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

#executionResultModal .btn-secondary {
    background: #6c757d;
    color: white;
}

#executionResultModal .btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.execution-result-content {
    padding: 20px;
}

.result-header {
    margin-bottom: 25px;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 15px;
}

.result-header h6 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0 0 15px 0;
    display: flex;
    align-items: center;
}

.result-header h6:before {
    content: '';
    width: 4px;
    height: 20px;
    background: #007bff;
    margin-right: 10px;
    border-radius: 2px;
}

.execution-info {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    padding: 0;
    background: transparent;
    border: none;
}

.execution-info .info-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 16px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    min-height: 70px;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

.execution-info .info-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
    border-color: transparent;
}

.execution-info .info-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.05) 0%, rgba(40, 167, 69, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.execution-info .info-item:hover::before {
    opacity: 1;
}

.execution-info .info-left {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.execution-info .info-icon {
    font-size: 20px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.8);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
}

.execution-info .info-content {
    flex: 1;
}

.execution-info .info-label {
    font-weight: 500;
    color: #6c757d;
    font-size: 12px;
    margin-bottom: 2px;
    line-height: 1;
}

.execution-info .info-value {
    color: #333;
    font-size: 16px;
    font-weight: 600;
    margin: 0;
    line-height: 1.2;
}

.execution-info .status-badge {
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: none;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.execution-info .status-badge.completed {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.execution-info .status-badge.running {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: white;
}

.execution-info .status-badge.failed {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
    color: white;
}

/* 为不同类型的信息项添加特定样式 */
.execution-info .info-item[data-type="time"] {
    border-left: 4px solid #007bff;
}

.execution-info .info-item[data-type="time"] .info-icon {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
}

.execution-info .info-item[data-type="status"] {
    border-left: 4px solid #28a745;
}

.execution-info .info-item[data-type="status"] .info-icon {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    color: white;
}

.execution-info .info-item[data-type="users"] {
    border-left: 4px solid #17a2b8;
}

.execution-info .info-item[data-type="users"] .info-icon {
    background: linear-gradient(135deg, #17a2b8, #117a8b);
    color: white;
}

.execution-info .info-item[data-type="success"] {
    border-left: 4px solid #28a745;
}

.execution-info .info-item[data-type="success"] .info-icon {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    color: white;
}

.execution-info .info-item[data-type="failed"] {
    border-left: 4px solid #dc3545;
}

.execution-info .info-item[data-type="failed"] .info-icon {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
}

.execution-info .info-item[data-type="email"] {
    border-left: 4px solid #6f42c1;
}

.execution-info .info-item[data-type="email"] .info-icon {
    background: linear-gradient(135deg, #6f42c1, #59359a);
    color: white;
}

.result-content {
    margin-top: 25px;
}

.result-content h6 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0 0 15px 0;
    display: flex;
    align-items: center;
}

.result-content h6:before {
    content: '';
    width: 4px;
    height: 20px;
    background: #28a745;
    margin-right: 10px;
    border-radius: 2px;
}

.markdown-content {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 25px;
    max-height: 400px;
    overflow-y: auto;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    font-size: 14px;
}

/* 自定义滚动条 */
.markdown-content::-webkit-scrollbar {
    width: 8px;
}

.markdown-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.markdown-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.markdown-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
    margin-top: 20px;
    margin-bottom: 12px;
    font-weight: 600;
    line-height: 1.3;
    color: #333;
}

.markdown-content h1:first-child,
.markdown-content h2:first-child,
.markdown-content h3:first-child {
    margin-top: 0;
}

.markdown-content h1 {
    font-size: 24px;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 8px;
    color: #007bff;
}

.markdown-content h2 {
    font-size: 20px;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 6px;
    color: #28a745;
}

.markdown-content h3 {
    font-size: 18px;
    color: #6f42c1;
}

.markdown-content h4 {
    font-size: 16px;
}

.markdown-content p {
    margin-bottom: 12px;
    color: #555;
}

.markdown-content ul,
.markdown-content ol {
    margin-bottom: 12px;
    padding-left: 1.5em;
}

.markdown-content li {
    margin-bottom: 4px;
    color: #555;
}

.markdown-content hr {
    border: none;
    border-top: 1px solid #e9ecef;
    margin: 20px 0;
}

.markdown-content code {
    background: #f1f3f4;
    border-radius: 3px;
    font-size: 13px;
    margin: 0;
    padding: 2px 6px;
    color: #d73a49;
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
}

.markdown-content pre {
    background: #f6f8fa;
    border: 1px solid #e1e4e8;
    border-radius: 6px;
    font-size: 13px;
    line-height: 1.45;
    overflow: auto;
    padding: 16px;
    margin-bottom: 16px;
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
}

.markdown-content pre code {
    background: transparent;
    border-radius: 0;
    color: inherit;
    padding: 0;
    margin: 0;
}

.markdown-content blockquote {
    border-left: 4px solid #dfe2e5;
    color: #6a737d;
    padding: 0 16px;
    margin: 16px 0;
    background: #f8f9fa;
    border-radius: 0 4px 4px 0;
}

.markdown-content table {
    border-collapse: collapse;
    border-spacing: 0;
    width: 100%;
    margin-bottom: 16px;
    border: 1px solid #e1e4e8;
    border-radius: 6px;
    overflow: hidden;
}

.markdown-content table th,
.markdown-content table td {
    border: 1px solid #e1e4e8;
    padding: 8px 12px;
    text-align: left;
}

.markdown-content table th {
    background: #f6f8fa;
    font-weight: 600;
    color: #333;
}

.markdown-content table tr:nth-child(even) {
    background: #f8f9fa;
}

.view-result-btn {
    background: #17a2b8;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    font-weight: 500;
}

.view-result-btn:hover {
    background: #138496;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.view-result-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.view-result-btn i {
    font-size: 11px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    #executionResultModal .modal-dialog {
        width: 95%;
        max-width: 95%;
        margin: 10px;
    }

    .execution-result-content {
        padding: 15px;
    }

    .execution-info {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }

    .markdown-content {
        padding: 20px;
        max-height: 300px;
    }
}

@media (max-width: 768px) {
    #executionResultModal .modal-dialog {
        width: 98%;
        max-width: 98%;
        margin: 5px;
    }

    .execution-result-content {
        padding: 10px;
    }

    .execution-info {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .execution-info .info-item {
        padding: 12px 16px;
        min-height: 60px;
    }

    .execution-info .info-icon {
        width: 35px;
        height: 35px;
        font-size: 16px;
    }

    .execution-info .info-left {
        gap: 10px;
    }

    .execution-info .info-value {
        font-size: 14px;
    }

    .execution-info .info-label {
        font-size: 11px;
    }

    .markdown-content {
        padding: 15px;
        max-height: 250px;
        font-size: 13px;
    }

    .view-result-btn {
        padding: 4px 8px;
        font-size: 11px;
    }
}

@media (max-width: 480px) {
    .execution-info .info-item {
        padding: 10px 12px;
        min-height: 55px;
    }

    .execution-info .info-icon {
        width: 30px;
        height: 30px;
        font-size: 14px;
    }

    .execution-info .info-left {
        gap: 8px;
    }

    .execution-info .info-value {
        font-size: 13px;
    }

    .execution-info .info-label {
        font-size: 10px;
    }
}
