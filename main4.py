from DadabaseControl.DatabaseControl import *
from DadabaseControl.DatabaseControl2 import *
from DadabaseControl.DatabaseControl3 import *
from DadabaseControl.DatabaseControl4 import *
from DadabaseControl.DatabaseControl5 import *
from fastapi import HTTPException, Depends, Request, UploadFile, File
from fastapi.responses import FileResponse
from fastapi.responses import JSONResponse
from Utils.auth07082 import *
from main import app
from typing import List, Dict, Any, Optional
from Utils.QASplitter import split_knowledge_to_qa
import os
import uuid
from pathlib import Path
from datetime import datetime


@app.get("/job_classifications/{id}/comprehensive")
async def get_job_classification_comprehensive_data(id: str, current_user: TokenData = Depends(get_any_user)):
    """获取职位分类的综合数据，包括职位、场景、用户、虚拟HR、定时分析、QA等信息"""
    try:
        # 获取基本信息
        if current_user.user_type == "admin":
            job_classification = get_job_classification(id, "")
            tenant_id = ""
        else:
            job_classification = get_job_classification(id, current_user.id)
            tenant_id = current_user.id

        if not job_classification:
            return {"code": 404, "message": "职位分类不存在"}

        # 获取统计数据
        from DadabaseControl.DatabaseControl3 import get_job_classification_statistics
        stats = get_job_classification_statistics(id)

        # 获取职位列表
        from DadabaseControl.DatabaseControl import get_positions_by_job_classification
        positions = get_positions_by_job_classification(id)

        # 获取场景列表
        from DadabaseControl.DatabaseControl import get_scenes_by_job_classification
        scenes = get_scenes_by_job_classification(id)

        # 获取场景问题
        scene_questions = {}
        for scene in scenes:
            from DadabaseControl.DatabaseControl import get_questions_by_scene
            questions = get_questions_by_scene(scene.get('sceneId', ''))
            scene_questions[scene.get('sceneId', '')] = questions

        # 获取用户列表（通过职位关联）
        users = []
        for position in positions:
            from DadabaseControl.DatabaseControl import get_users_by_position
            position_users = get_users_by_position(position.get('dataId', ''), tenant_id)
            users.extend(position_users)

        # 去重用户
        unique_users = []
        seen_user_ids = set()
        for user in users:
            if user.get('userid') not in seen_user_ids:
                unique_users.append(user)
                seen_user_ids.add(user.get('userid'))

        # 获取虚拟HR信息
        virtual_hr = None
        if job_classification.get('virtual_hr_id'):
            from DadabaseControl.DatabaseControl2 import get_virtual_hr
            virtual_hr = get_virtual_hr(job_classification.get('virtual_hr_id'), tenant_id)

        # 获取定时分析配置
        scheduled_analyses = []
        if current_user.user_type != "admin":  # 只有租户有定时分析
            from DadabaseControl.DatabaseControl4 import get_scheduled_analysis_by_job_classification
            scheduled_analyses = get_scheduled_analysis_by_job_classification(id, current_user.id)

        # 获取QA问题对
        from DadabaseControl.DatabaseControl4 import get_qa_list_by_job_classification
        qa_list = get_qa_list_by_job_classification(id)

        # 组装综合数据
        comprehensive_data = {
            "basic_info": job_classification,
            "statistics": stats,
            "positions": positions,
            "scenes": scenes,
            "scene_questions": scene_questions,
            "users": unique_users,
            "virtual_hr": virtual_hr,
            "scheduled_analyses": scheduled_analyses,
            "qa_list": qa_list
        }

        return {"code": 200, "data": comprehensive_data, "message": "获取职位分类综合数据成功"}

    except Exception as e:
        print(f"获取职位分类综合数据失败: {e}")
        return {"code": 500, "message": f"获取职位分类综合数据失败: {str(e)}"}

@app.post("/job_classification")
async def add_job_classification(request: Request,current_user: TokenData = Depends(get_any_user)):
    if current_user.user_type != "tenant":
        return {"code": 400, "message": "只有租户可以添加职位分类"}
    data = await request.json()
    data["tenant_id"] = current_user.id
    job_classification_id = insert_job_classification(data)
    if job_classification_id:
        return {"code": 200, "data": {"id": job_classification_id}, "message": "添加职位分类成功"}
    else:
        return {"code": 400, "message": "添加职位分类失败"}

@app.put("/job_classification/{id}")
async def update_job_classification_by_id(id: str, request: Request,current_user: TokenData = Depends(get_any_user)):
    if current_user.user_type != "tenant":
        return {"code": 400, "message": "只有租户可以更新职位分类"}
    data = await request.json()
    data["tenant_id"] = current_user.id
    success = update_job_classification(id, data,current_user.id)
    if success:
        return {"code": 200, "message": "更新职位分类成功"}
    else:
        return {"code": 400, "message": "更新职位分类失败"}

@app.delete("/job_classification/{id}")
async def delete_job_classification_by_id(id: str,current_user: TokenData = Depends(get_any_user)):
    if current_user.user_type != "tenant":
        return {"code": 400, "message": "只有租户可以删除职位分类"}
    success = delete_job_classification(id,current_user.id)
    if success:
        return {"code": 200, "message": "删除职位分类成功"}
    else:
        return {"code": 400, "message": "删除职位分类失败"}

# 场景管理API
@app.get("/scenes/job_classification/{job_classification_id}")
async def main_get_scenes_by_job_classification(job_classification_id: str,current_user: TokenData = Depends(get_any_user)):
    if current_user.user_type == "admin":
        scenes = get_scenes_by_job_classification(job_classification_id)
    else:
        job_classification = get_job_classification(job_classification_id,current_user.id)
        if job_classification is None:
            return []
        scenes = get_scenes_by_job_classification(job_classification_id)
    return scenes

@app.get("/scenes/job_classification/position/{position_id}")
async def main_get_scenes_by_job_classification(position_id: str,current_user: TokenData = Depends(get_any_user)):
    if current_user.user_type == "admin":
        position = get_position(position_id,"")
        tenant_id = ""
    else:
        position = get_position(position_id,current_user.id)
        tenant_id = current_user.id
    scenes = get_scenes_by_job_classification(position.get("job_classification_id"))
    return scenes

# @app.get("/scenes/all")
# async def get_all_scenes_endpoint():
#     scenes = get_all_scenes()
#     return scenes

@app.get("/scenes/count")
async def get_scenes_count(positionId: str = None, job_classification_id: str = None,current_user: TokenData = Depends(get_any_user)):
    if current_user.user_type == "admin":
        scenes = get_scenes_by_job_classification(job_classification_id)
    else:
        job_classification = get_job_classification(job_classification_id,current_user.id)
        if job_classification is None:
            return {"count": 0}
        scenes = get_scenes_by_job_classification(job_classification_id)
    return {"count": len(scenes)}

@app.get("/scene/{scene_id}")
async def get_scene_by_id(scene_id: str,current_user: TokenData = Depends(get_any_user)):
    scene = get_scene(scene_id)
    if scene:
        return scene
    return {"code": 404, "message": "场景不存在"}

@app.post("/scene")
async def add_scene(request: Request,current_user: TokenData = Depends(get_any_user)):
    if current_user.user_type != "tenant":
        return {"code": 400, "message": "只有租户可以添加场景"}
    data = await request.json()
    try:
        # 如果设置了默认场景，将该职位分类下其他场景的默认标志设为False
        if data.get("isDefault") == 1:
            scenes = get_scenes_by_job_classification(data.get("job_classification_id"))
            for scene in scenes:
                if scene.get("isDefault") == 1:
                    update_scene(scene.get("sceneId"), {"isDefault": 0})
        
        result = insert_scene(data)
        if result:
            return {"code": 200, "message": "添加场景成功"}
        return {"code": 400, "message": "添加场景失败"}
    except Exception as e:
        return {"code": 500, "message": str(e)}

@app.put("/scene/{scene_id}")
async def update_scene_by_id(scene_id: str, request: Request,current_user: TokenData = Depends(get_any_user)):
    if current_user.user_type != "tenant":
        return {"code": 400, "message": "只有租户可以更新场景"}
    data = await request.json()
    try:
        # 如果设置了默认场景，将该职位分类下其他场景的默认标志设为False
        if data.get("isDefault") == 1:
            scene = get_scene(scene_id)
            scenes = get_scenes_by_job_classification(scene.get("job_classification_id") or data.get("job_classification_id"))
            for s in scenes:
                if s.get("sceneId") != scene_id and s.get("isDefault") == 1:
                    update_scene(s.get("sceneId"), {"isDefault": 0})
        
        result = update_scene(scene_id, data)
        if result:
            return {"code": 200, "message": "更新场景成功"}
        return {"code": 400, "message": "更新场景失败，场景不存在"}
    except Exception as e:
        return {"code": 500, "message": str(e)}

@app.delete("/scene/{scene_id}")
async def delete_scene_by_id(scene_id: str,current_user: TokenData = Depends(get_any_user)):
    if current_user.user_type != "tenant":
        return {"code": 400, "message": "只有租户可以删除场景"}
    try:
        # 检查是否是默认场景
        scene = get_scene(scene_id)
        if scene.get("isDefault") == 1:
            return {"code": 400, "message": "不能删除默认场景，请先设置其他场景为默认"}
        
        result = delete_scene(scene_id)
        if result:
            return {"code": 200, "message": "删除场景成功"}
        return {"code": 400, "message": "删除场景失败，场景不存在"}
    except Exception as e:
        return {"code": 500, "message": str(e)}

# 场景问题管理API
@app.get("/scene_questions/{scene_id}")
async def get_questions_by_scene_id(scene_id: str,current_user: TokenData = Depends(get_any_user)):
    questions = get_questions_by_scene(scene_id)
    return questions

@app.get("/scene_question/{question_id}")
async def get_scene_question_by_id(question_id: str,current_user: TokenData = Depends(get_any_user)):
    question = get_scene_question(question_id)
    if question:
        return question
    return {"code": 404, "message": "问题不存在"}

@app.post("/scene_question")
async def add_scene_question(request: Request,current_user: TokenData = Depends(get_any_user)):
    if current_user.user_type != "tenant":
        return {"code": 400, "message": "只有租户可以添加场景问题"}
    data = await request.json()
    try:
        result = insert_scene_question(data)
        if result:
            return {"code": 200, "message": "添加问题成功"}
        return {"code": 400, "message": "添加问题失败"}
    except Exception as e:
        return {"code": 500, "message": str(e)}

@app.put("/scene_question/{question_id}")
async def update_scene_question_by_id(question_id: str, request: Request,current_user: TokenData = Depends(get_any_user)):
    if current_user.user_type != "tenant":
        return {"code": 400, "message": "只有租户可以更新场景问题"}
    data = await request.json()
    try:
        result = update_scene_question(question_id, data)
        if result:
            return {"code": 200, "message": "更新问题成功"}
        return {"code": 400, "message": "更新问题失败，问题不存在"}
    except Exception as e:
        return {"code": 500, "message": str(e)}

@app.delete("/scene_question/{question_id}")
async def delete_scene_question_by_id(question_id: str,current_user: TokenData = Depends(get_any_user)):
    if current_user.user_type != "tenant":
        return {"code": 400, "message": "只有租户可以删除场景问题"}
    try:
        result = delete_scene_question(question_id)
        if result:
            return {"code": 200, "message": "删除问题成功"}
        return {"code": 400, "message": "删除问题失败，问题不存在"}
    except Exception as e:
        return {"code": 500, "message": str(e)}

# 场景切换器管理API
@app.get("/scene_switchers/job_classification/{job_classification_id}")
async def get_scene_switchers_by_job_classification_id(job_classification_id: str, current_user: TokenData = Depends(get_any_user)):
    """获取职位分类下的所有场景切换器"""
    try:
        from DadabaseControl.DatabaseControl import get_scene_switchers_by_job_classification
        switchers = get_scene_switchers_by_job_classification(job_classification_id)
        return switchers
    except Exception as e:
        return {"code": 500, "message": f"获取场景切换器失败: {str(e)}"}

@app.get("/scene_switcher/{switcher_id}")
async def get_scene_switcher_by_id(switcher_id: str, current_user: TokenData = Depends(get_any_user)):
    """获取单个场景切换器详情"""
    try:
        from DadabaseControl.DatabaseControl import get_scene_switcher
        switcher = get_scene_switcher(switcher_id)
        if switcher:
            return switcher
        return {"code": 404, "message": "场景切换器不存在"}
    except Exception as e:
        return {"code": 500, "message": f"获取场景切换器失败: {str(e)}"}

@app.post("/scene_switcher")
async def add_scene_switcher(request: Request, current_user: TokenData = Depends(get_any_user)):
    """创建场景切换器"""
    if current_user.user_type != "tenant":
        return {"code": 400, "message": "只有租户可以添加场景切换器"}

    try:
        data = await request.json()
        data["tenant_id"] = current_user.id

        from DadabaseControl.DatabaseControl import insert_scene_switcher
        result = insert_scene_switcher(data)
        if result:
            return {"code": 200, "message": "添加场景切换器成功", "switcher_id": result}
        return {"code": 400, "message": "添加场景切换器失败"}
    except Exception as e:
        return {"code": 500, "message": f"添加场景切换器失败: {str(e)}"}

@app.put("/scene_switcher/{switcher_id}")
async def update_scene_switcher_by_id(switcher_id: str, request: Request, current_user: TokenData = Depends(get_any_user)):
    """更新场景切换器"""
    if current_user.user_type != "tenant":
        return {"code": 400, "message": "只有租户可以更新场景切换器"}

    try:
        data = await request.json()

        from DadabaseControl.DatabaseControl import update_scene_switcher
        result = update_scene_switcher(switcher_id, data)
        if result:
            return {"code": 200, "message": "更新场景切换器成功"}
        return {"code": 400, "message": "更新场景切换器失败，切换器不存在"}
    except Exception as e:
        return {"code": 500, "message": f"更新场景切换器失败: {str(e)}"}

@app.delete("/scene_switcher/{switcher_id}")
async def delete_scene_switcher_by_id(switcher_id: str, current_user: TokenData = Depends(get_any_user)):
    """删除场景切换器"""
    if current_user.user_type != "tenant":
        return {"code": 400, "message": "只有租户可以删除场景切换器"}

    try:
        from DadabaseControl.DatabaseControl import delete_scene_switcher
        result = delete_scene_switcher(switcher_id)
        if result:
            return {"code": 200, "message": "删除场景切换器成功"}
        return {"code": 400, "message": "删除场景切换器失败，切换器不存在"}
    except Exception as e:
        return {"code": 500, "message": f"删除场景切换器失败: {str(e)}"}

# 流程图位置管理API
@app.post("/flowchart_position")
async def save_flowchart_position_api(request: Request, current_user: TokenData = Depends(get_any_user)):
    """保存流程图元素位置"""
    if current_user.user_type != "tenant":
        return {"code": 400, "message": "只有租户可以保存位置信息"}

    try:
        data = await request.json()
        job_classification_id = data.get("job_classification_id")
        element_type = data.get("element_type")  # 'node' 或 'edge'
        element_id = data.get("element_id")
        position_data = data.get("position_data")

        if not all([job_classification_id, element_type, element_id, position_data]):
            return {"code": 400, "message": "缺少必要参数"}

        from DadabaseControl.DatabaseControl import save_flowchart_position
        result = save_flowchart_position(job_classification_id, element_type, element_id, position_data, current_user.id)

        if result:
            return {"code": 200, "message": "保存位置成功"}
        return {"code": 400, "message": "保存位置失败"}
    except Exception as e:
        return {"code": 500, "message": f"保存位置失败: {str(e)}"}

@app.get("/flowchart_positions/{job_classification_id}")
async def get_flowchart_positions_api(job_classification_id: str, current_user: TokenData = Depends(get_any_user)):
    """获取流程图位置信息"""
    try:
        from DadabaseControl.DatabaseControl import get_flowchart_positions
        positions = get_flowchart_positions(job_classification_id)
        return {"code": 200, "data": positions}
    except Exception as e:
        return {"code": 500, "message": f"获取位置信息失败: {str(e)}"}

@app.put("/scene_questions/reorder")
async def reorder_scene_questions(request: Request,current_user: TokenData = Depends(get_any_user)):
    if current_user.user_type != "tenant":
        return {"code": 400, "message": "只有租户可以更新场景问题顺序"}
    """更新场景问题的顺序"""
    try:
        data = await request.json()
        if not isinstance(data, list):
            return {"code": 400, "message": "请提供问题顺序列表"}
        
        success = True
        for item in data:
            if not isinstance(item, dict) or "questionId" not in item or "seq" not in item:
                return {"code": 400, "message": "每个问题项必须包含questionId和seq字段"}
            
            result = update_scene_question(item["questionId"], {"seq": item["seq"]})
            if not result:
                success = False
        
        if success:
            return {"code": 200, "message": "问题顺序更新成功"}
        return {"code": 400, "message": "部分或全部问题顺序更新失败"}
    except Exception as e:
        return {"code": 500, "message": str(e)}

@app.get("/api/dashboard/stats", response_class=JSONResponse)
async def get_dashboard_stats_api(request: Request, current_user: TokenData = Depends(get_any_user)):
    """
    获取仪表盘主要统计数据
    """
    try:
        tenant_id = None if current_user.user_type == "admin" else current_user.id
        stats = get_dashboard_stats(tenant_id)
        return {"data": stats, "code": 200, "message": "获取仪表盘统计数据成功"}
    except Exception as e:
        return {"message": f"获取仪表盘统计数据失败: {str(e)}", "code": 500}

@app.get("/api/dashboard/daily-stats", response_class=JSONResponse)
async def get_daily_stats_api(request: Request, current_user: TokenData = Depends(get_any_user), days: int = 7):
    """
    获取每日变化统计数据
    """
    try:
        tenant_id = None if current_user.user_type == "admin" else current_user.id
        stats = get_daily_stats(tenant_id, days)
        return {"data": stats, "code": 200, "message": "获取每日统计数据成功"}
    except Exception as e:
        return {"message": f"获取每日统计数据失败: {str(e)}", "code": 500}

@app.get("/api/dashboard/token-usage", response_class=JSONResponse)
async def get_token_usage_api(request: Request, current_user: TokenData = Depends(get_any_user)):
    """
    获取Token使用情况统计
    """
    try:
        if current_user.user_type != "admin" and current_user.user_type != "tenant":
            return {"message": "权限不足", "code": 403}
        
        tenant_id = None if current_user.user_type == "admin" else current_user.id
        stats = get_token_usage_week_summary(tenant_id)
        return {"data": stats, "code": 200, "message": "获取Token使用统计成功"}
    except Exception as e:
        return {"message": f"获取Token使用统计失败: {str(e)}", "code": 500}

@app.get("/api/dashboard/recent-positions", response_class=JSONResponse)
async def get_recent_positions_api(request: Request, current_user: TokenData = Depends(get_any_user), days: int = 7):
    """
    获取最近新增的职位列表
    """
    try:
        tenant_id = None if current_user.user_type == "admin" else current_user.id
        data = get_recent_new_positions(tenant_id, days)
        return {"data": data, "code": 200, "message": "获取最近新增职位成功"}
    except Exception as e:
        return {"message": f"获取最近新增职位失败: {str(e)}", "code": 500}

@app.get("/api/dashboard/recent-users", response_class=JSONResponse)
async def get_recent_users_api(request: Request, current_user: TokenData = Depends(get_any_user), days: int = 7):
    """
    获取最近新增的候选人列表
    """
    try:
        tenant_id = None if current_user.user_type == "admin" else current_user.id
        data = get_recent_new_users(tenant_id, days)
        return {"data": data, "code": 200, "message": "获取最近新增候选人成功"}
    except Exception as e:
        return {"message": f"获取最近新增候选人失败: {str(e)}", "code": 500}
    
# ======================== 邮件配置相关API ========================

@app.put("/api/tenant/{tenant_id}/email-settings")
async def update_email_settings(
    tenant_id: str,
    email_config: Dict[str, Any],
    current_user: TokenData = Depends(get_tenant_user)
):
    """更新邮件配置"""
    # 验证租户权限
    if current_user.id != tenant_id:
        raise HTTPException(status_code=403, detail="无权访问其他租户的数据")

    try:
        smtp_server = email_config.get("smtp_server")
        smtp_port = email_config.get("smtp_port", 587)
        smtp_username = email_config.get("smtp_username")
        smtp_password = email_config.get("smtp_password")
        smtp_use_tls = email_config.get("smtp_use_tls", True)

        if not all([smtp_server, smtp_username, smtp_password]):
            raise HTTPException(status_code=400, detail="缺少必要的邮件配置参数")

        success, message = update_tenant_email_settings(
            tenant_id, smtp_server, smtp_port, smtp_username, smtp_password, smtp_use_tls
        )

        if success:
            return {"message": "邮件配置更新成功"}
        else:
            raise HTTPException(status_code=400, detail=message)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新邮件配置失败: {str(e)}")


@app.get("/api/tenant/{tenant_id}/email-settings")
async def get_email_settings(
    tenant_id: str,
    current_user: TokenData = Depends(get_tenant_user)
):
    """获取邮件配置"""
    # 验证租户权限
    if current_user.id != tenant_id:
        raise HTTPException(status_code=403, detail="无权访问其他租户的数据")

    try:
        settings = get_tenant_email_settings(tenant_id)
        # 不返回密码
        settings['smtp_password'] = '******' if settings['smtp_password'] else ''
        return {"data": settings}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取邮件配置失败: {str(e)}")


@app.post("/api/tenant/{tenant_id}/scheduled-analysis/{analysis_id}/execute")
async def execute_analysis_immediately(
    tenant_id: str,
    analysis_id: str,
    current_user: TokenData = Depends(get_tenant_user)
):
    """立即执行分析"""
    # 验证租户权限
    if current_user.id != tenant_id:
        raise HTTPException(status_code=403, detail="无权访问其他租户的数据")

    try:
        # 验证分析配置是否存在
        analysis = get_scheduled_analysis(analysis_id, tenant_id)
        if not analysis:
            raise HTTPException(status_code=404, detail="分析配置不存在")

        # 异步执行分析任务
        from Utils.AnalysisEngine import execute_analysis_task
        import asyncio

        # 在后台执行分析任务
        asyncio.create_task(execute_analysis_task(analysis_id, tenant_id))

        return {"message": "分析任务已启动，请稍后查看结果"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"执行分析失败: {str(e)}")


# ======================== 调度器管理相关API ========================

@app.post("/api/tenant/{tenant_id}/scheduled-analysis/{analysis_id}/schedule")
async def manage_analysis_schedule(
    tenant_id: str,
    analysis_id: str,
    request_data: Dict[str, Any],
    current_user: TokenData = Depends(get_tenant_user)
):
    """管理分析任务的定时调度"""
    # 验证租户权限
    if current_user.id != tenant_id:
        raise HTTPException(status_code=403, detail="无权访问其他租户的数据")

    try:
        from Utils.SchedulerManager import (
            add_analysis_schedule,
            remove_analysis_schedule,
            update_analysis_schedule
        )

        # 从请求数据中获取action
        action = request_data.get("action")
        if not action:
            raise HTTPException(status_code=400, detail="缺少action参数")

        # 获取分析配置
        analysis = get_scheduled_analysis(analysis_id, tenant_id)
        if not analysis:
            raise HTTPException(status_code=404, detail="分析配置不存在")

        if action == "add":
            if analysis['is_auto_enabled']:
                success = add_analysis_schedule(
                    analysis_id, tenant_id,
                    analysis['schedule_time'],
                    analysis['name'],
                    analysis.get('time_range_days', 1)
                )
                if success:
                    return {"message": "定时任务添加成功"}
                else:
                    raise HTTPException(status_code=400, detail="添加定时任务失败")
            else:
                return {"message": "分析配置未启用自动执行"}

        elif action == "remove":
            success = remove_analysis_schedule(analysis_id)
            if success:
                return {"message": "定时任务移除成功"}
            else:
                return {"message": "定时任务不存在或移除失败"}

        elif action == "update":
            if analysis['is_auto_enabled']:
                success = update_analysis_schedule(
                    analysis_id, tenant_id,
                    analysis['schedule_time'],
                    analysis['name'],
                    analysis.get('time_range_days', 1)
                )
                if success:
                    return {"message": "定时任务更新成功"}
                else:
                    raise HTTPException(status_code=400, detail="更新定时任务失败")
            else:
                # 如果禁用了自动执行，移除定时任务
                remove_analysis_schedule(analysis_id)
                return {"message": "已移除定时任务（自动执行已禁用）"}
        else:
            raise HTTPException(status_code=400, detail="无效的操作类型")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"管理定时任务失败: {str(e)}")


@app.get("/api/tenant/{tenant_id}/scheduled-analysis/{analysis_id}/schedule-status")
async def get_schedule_status(
    tenant_id: str,
    analysis_id: str,
    current_user: TokenData = Depends(get_tenant_user)
):
    """获取分析任务的调度状态"""
    # 验证租户权限
    if current_user.id != tenant_id:
        raise HTTPException(status_code=403, detail="无权访问其他租户的数据")

    try:
        from Utils.SchedulerManager import get_analysis_job_status

        status = get_analysis_job_status(analysis_id)
        return {"data": status}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取调度状态失败: {str(e)}")


@app.post("/api/tenant/{tenant_id}/email-test")
async def test_email_connection(
    tenant_id: str,
    current_user: TokenData = Depends(get_tenant_user)
):
    """测试邮件连接"""
    # 验证租户权限
    if current_user.id != tenant_id:
        raise HTTPException(status_code=403, detail="无权访问其他租户的数据")

    try:
        from Utils.EmailSender import test_email_connection

        result = test_email_connection(tenant_id)
        return result

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"测试邮件连接失败: {str(e)}")


@app.get("/api/tenant/{tenant_id}/debug-info")
async def get_debug_info(
    tenant_id: str,
    current_user: TokenData = Depends(get_tenant_user)
):
    """获取调试信息"""
    try:
        from DadabaseControl.DatabaseControl4 import get_users_for_analysis

        # 获取用户信息
        debug_info = {
            "current_user": {
                "id": current_user.id,
                "account": current_user.account,
                "user_type": current_user.user_type
            },
            "requested_tenant_id": tenant_id,
            "users_found": len(get_users_for_analysis(tenant_id, 7)),
            "available_tenants": []
        }

        # 查询数据库中实际存在的tenant_id
        from DadabaseControl.DatabaseControl import _connect_db
        conn = _connect_db()
        if conn:
            try:
                cursor = conn.cursor()
                cursor.execute("SELECT DISTINCT tenant_id FROM user WHERE tenant_id IS NOT NULL AND tenant_id != ''")
                tenants = cursor.fetchall()
                debug_info["available_tenants"] = [t[0] for t in tenants]
            finally:
                conn.close()

        return debug_info

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取调试信息失败: {str(e)}")


@app.get("/api/tenant/{tenant_id}/scheduled-analysis/{analysis_id}/realtime-status")
async def get_realtime_analysis_status(
    tenant_id: str,
    analysis_id: str,
    current_user: TokenData = Depends(get_tenant_user)
):
    """获取分析任务的实时执行状态"""
    # 验证租户权限
    if current_user.id != tenant_id:
        raise HTTPException(status_code=403, detail="无权访问其他租户的数据")

    try:
        # 获取最新的执行日志
        logs = get_analysis_execution_logs(analysis_id, tenant_id, limit=1)

        if logs:
            latest_log = logs[0]

            # 计算进度百分比
            total_users = latest_log.get('total_users', 0)
            successful = latest_log.get('successful_analyses', 0)
            failed = latest_log.get('failed_analyses', 0)
            completed = successful + failed

            progress_percentage = 0
            if total_users > 0:
                progress_percentage = round((completed / total_users) * 100, 1)

            return {
                "data": {
                    "status": latest_log.get('status', 'unknown'),
                    "total_users": total_users,
                    "successful_analyses": successful,
                    "failed_analyses": failed,
                    "completed_analyses": completed,
                    "progress_percentage": progress_percentage,
                    "execution_time": latest_log.get('execution_time', ''),
                    "error_message": latest_log.get('error_message', ''),
                    "email_sent": latest_log.get('email_sent', False)
                }
            }
        else:
            return {
                "data": {
                    "status": "not_started",
                    "total_users": 0,
                    "successful_analyses": 0,
                    "failed_analyses": 0,
                    "completed_analyses": 0,
                    "progress_percentage": 0,
                    "execution_time": "",
                    "error_message": "",
                    "email_sent": False
                }
            }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取实时状态失败: {str(e)}")


# ======================== QA引擎API接口 ========================

@app.get("/api/qa-engine/{job_classification_id}")
async def get_qa_list(job_classification_id: str, current_user: TokenData = Depends(get_any_user)):
    """获取指定岗位分类的QA列表"""
    try:
        qa_list = get_qa_list_by_job_classification(job_classification_id)
        return {"code": 200, "data": qa_list, "message": "获取QA列表成功"}
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"获取QA列表失败: {str(e)}"}
        )


@app.post("/api/qa-engine")
async def create_qa(
    qa_data: Dict[str, Any],
    current_user: TokenData = Depends(get_any_user)
):
    """创建新的QA项"""
    try:
        job_classification_id = qa_data.get("job_classification_id")
        question = qa_data.get("question")
        answer = qa_data.get("answer")
        source = qa_data.get("source", "manual")

        if not all([job_classification_id, question, answer]):
            return JSONResponse(
                status_code=400,
                content={"code": 400, "message": "缺少必要参数"}
            )

        qa_id = create_qa_item(job_classification_id, question, answer, source)
        if qa_id:
            return {"code": 200, "data": {"id": qa_id}, "message": "创建QA成功"}
        else:
            return JSONResponse(
                status_code=500,
                content={"code": 500, "message": "创建QA失败"}
            )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"创建QA失败: {str(e)}"}
        )


@app.put("/api/qa-engine/{qa_id}")
async def update_qa(
    qa_id: str,
    qa_data: Dict[str, Any],
    current_user: TokenData = Depends(get_any_user)
):
    """更新QA项"""
    try:
        question = qa_data.get("question")
        answer = qa_data.get("answer")

        success = update_qa_item(qa_id, question, answer)
        if success:
            return {"code": 200, "message": "更新QA成功"}
        else:
            return JSONResponse(
                status_code=404,
                content={"code": 404, "message": "QA项不存在或更新失败"}
            )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"更新QA失败: {str(e)}"}
        )


@app.delete("/api/qa-engine/{qa_id}")
async def delete_qa(qa_id: str, current_user: TokenData = Depends(get_any_user)):
    """删除QA项"""
    try:
        success = delete_qa_item(qa_id)
        if success:
            return {"code": 200, "message": "删除QA成功"}
        else:
            return JSONResponse(
                status_code=404,
                content={"code": 404, "message": "QA项不存在或删除失败"}
            )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"删除QA失败: {str(e)}"}
        )


@app.post("/api/qa-engine/{qa_id}/increment-frequency")
async def increment_frequency(qa_id: str, current_user: TokenData = Depends(get_any_user)):
    """增加QA项的使用频次"""
    try:
        success = increment_qa_frequency(qa_id)
        if success:
            return {"code": 200, "message": "更新频次成功"}
        else:
            return JSONResponse(
                status_code=404,
                content={"code": 404, "message": "QA项不存在或更新失败"}
            )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"更新频次失败: {str(e)}"}
        )


@app.get("/api/knowledge-base/{job_classification_id}")
async def get_knowledge_base_list_api(job_classification_id: str, current_user: TokenData = Depends(get_any_user)):
    """获取知识库文本列表"""
    try:
        kb_list = get_knowledge_base_list(job_classification_id)
        return {"code": 200, "data": kb_list, "message": "获取知识库列表成功"}
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"获取知识库列表失败: {str(e)}"}
        )


@app.post("/api/qa-engine/ai-split")
async def ai_split_qa(
    split_data: Dict[str, Any],
    current_user: TokenData = Depends(get_any_user)
):
    """AI自动拆分知识库文本为QA对"""
    try:
        job_classification_id = split_data.get("job_classification_id")
        content = split_data.get("content")

        if not all([job_classification_id, content]):
            return JSONResponse(
                status_code=400,
                content={"code": 400, "message": "缺少必要参数"}
            )

        # 保存知识库文本
        kb_id = create_knowledge_base(job_classification_id, content)
        if not kb_id:
            return JSONResponse(
                status_code=500,
                content={"code": 500, "message": "保存知识库文本失败"}
            )

        # 获取岗位分类信息，用于优化拆分效果
        job_classification = get_job_classification(job_classification_id, "")
        job_classification_name = job_classification.get("class_name", "") if job_classification else ""

        # 调用大模型API进行QA拆分
        qa_pairs = await split_knowledge_to_qa(content, job_classification_name)

        if not qa_pairs:
            return JSONResponse(
                status_code=500,
                content={"code": 500, "message": "AI拆分失败，未能生成有效的QA对"}
            )

        # 批量创建QA项
        created_ids = batch_create_qa_items(job_classification_id, qa_pairs, "ai_split")

        # 更新知识库的QA处理数量
        update_knowledge_base_qa_count(kb_id, len(created_ids))

        return {
            "code": 200,
            "data": {
                "knowledge_base_id": kb_id,
                "created_qa_count": len(created_ids),
                "created_qa_ids": created_ids
            },
            "message": f"AI拆分成功，生成{len(created_ids)}个QA对"
        }

    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"AI拆分失败: {str(e)}"}
        )


# ======================== 系统配置相关API ========================

@app.post("/api/system/upload-qr-code")
async def upload_qr_code(
    file: UploadFile = File(...),
    current_user: TokenData = Depends(get_admin_user)
):
    """上传群二维码图片 - 仅管理员可用，覆盖模式"""
    try:
        # 检查文件类型
        if not file.content_type or not file.content_type.startswith('image/'):
            return JSONResponse(
                status_code=400,
                content={"code": 400, "message": "只能上传图片文件"}
            )

        # 检查文件大小 (限制为5MB)
        file_content = await file.read()
        if len(file_content) > 5 * 1024 * 1024:
            return JSONResponse(
                status_code=400,
                content={"code": 400, "message": "文件大小不能超过5MB"}
            )

        # 检查文件格式
        file_extension = Path(file.filename).suffix.lower()
        if file_extension not in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']:
            return JSONResponse(
                status_code=400,
                content={"code": 400, "message": "不支持的图片格式，请上传 jpg、png、gif 或 bmp 格式的图片"}
            )

        # 确保目录存在
        qr_codes_dir = Path("uploads/qr_codes")
        qr_codes_dir.mkdir(parents=True, exist_ok=True)

        # 删除现有的所有二维码文件（覆盖模式）
        for existing_file in qr_codes_dir.iterdir():
            if existing_file.is_file() and existing_file.suffix.lower() in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']:
                existing_file.unlink()

        # 使用固定的文件名（群二维码）
        fixed_filename = f"group_qr_code{file_extension}"
        upload_path = qr_codes_dir / fixed_filename

        # 保存文件
        with open(upload_path, "wb") as f:
            f.write(file_content)

        return {
            "code": 200,
            "data": {
                "filename": fixed_filename,
                "file_path": str(upload_path),
                "file_size": len(file_content),
                "upload_time": datetime.now().isoformat()
            },
            "message": "群二维码更新成功"
        }

    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"上传失败: {str(e)}"}
        )


@app.get("/api/system/qr-codes")
async def get_qr_codes():
    """获取已上传的群二维码列表 - 仅管理员可用"""
    try:
        qr_codes_dir = Path("uploads/qr_codes")
        if not qr_codes_dir.exists():
            return {"code": 200, "data": [], "message": "暂无上传的二维码"}

        qr_codes = []
        for file_path in qr_codes_dir.iterdir():
            if file_path.is_file() and file_path.suffix.lower() in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']:
                stat = file_path.stat()
                qr_codes.append({
                    "filename": file_path.name,
                    "file_path": str(file_path),
                    "file_size": stat.st_size,
                    "upload_time": datetime.fromtimestamp(stat.st_mtime).isoformat()
                })

        # 按上传时间倒序排列
        qr_codes.sort(key=lambda x: x['upload_time'], reverse=True)

        return {"code": 200, "data": qr_codes, "message": "获取二维码列表成功"}

    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"获取二维码列表失败: {str(e)}"}
        )


@app.delete("/api/system/qr-codes/{filename}")
async def delete_qr_code(filename: str, current_user: TokenData = Depends(get_admin_user)):
    """删除群二维码 - 仅管理员可用"""
    try:
        file_path = Path("uploads/qr_codes") / filename

        if not file_path.exists():
            return JSONResponse(
                status_code=404,
                content={"code": 404, "message": "文件不存在"}
            )

        # 删除文件
        file_path.unlink()

        return {"code": 200, "message": "删除成功"}

    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"删除失败: {str(e)}"}
        )
    
@app.get("/public_group_qr_code")
async def get_public_group_qr_code():
    """获取公共群二维码"""
    try:
        qr_codes_dir = Path("uploads/qr_codes")
        if not qr_codes_dir.exists():
            return {"code": 200, "data": [], "message": "暂无上传的二维码"}

        for file_path in qr_codes_dir.iterdir():
            return FileResponse(file_path)
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"获取公共群二维码失败: {str(e)}"}
        )