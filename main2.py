from DadabaseControl.DatabaseControl import *
from fastapi import FastAP<PERSON>, Request
from main import app
import time
from Utils.SceneSwitch import *
from Utils.PromptFactory import *
import json
from ChatApi.ChatApi import *
from ChatApi.ChatLog import *
from ChatApi.ExtractInformation import *
from fastapi import FastAPI, Request, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse,JSONResponse, RedirectResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.security import OAuth2Password<PERSON>earer, OAuth2PasswordRequestForm
from DadabaseControl.DatabaseControl import *
from ChatApi.ChatLog import *
from Utils.PromptFactory import *
from Utils.ScoringModule import *
from Utils.SceneSwitch import *
from ChatApi.ChatApi import *
from Utils.auth07082 import *
import os
import time
import uvicorn
import uuid
from datetime import datetime, timedelta
from Utils.TriggerManager import *
import json
import base64
from starlette.middleware.base import BaseHTTPMiddleware
from ChatApi.CallAction import *
from Utils.systemTools import *
import sys
from DadabaseControl.DatabaseControl2 import *
from DadabaseControl.DatabaseControl3 import *
from DadabaseControl.DatabaseControl5 import *

    
@app.get("/score-triggers")
async def get_score_triggers(current_user: TokenData = Depends(get_any_user)):
    """获取所有分数触发器"""
    try:
        triggers = get_all_score_triggers()
        return {"code": 200, "message": "success", "data": triggers}
    except Exception as e:
        return {"code": 400, "message": str(e)}

@app.get("/score-trigger/{trigger_id}")
async def get_score_trigger_by_id(trigger_id: str,current_user: TokenData = Depends(get_any_user)):
    """获取指定ID的分数触发器"""
    try:
        trigger = get_score_trigger(trigger_id)
        if trigger:
            return {"code": 200, "message": "success", "data": trigger}
        else:
            return {"code": 404, "message": "分数触发器不存在"}
    except Exception as e:
        return {"code": 400, "message": str(e)}

@app.post("/score-trigger")
async def add_score_trigger(request: Request,current_user: TokenData = Depends(get_any_user)):
    """创建新的分数触发器"""
    try:
        data = await request.json()
        required_fields = ['score', 'score_trigger_name', 'action']
        for field in required_fields:
            if field not in data:
                return {"code": 400, "message": f"缺少必填字段: {field}"}
        
        # 如果没有提供ID，生成一个新的
        if 'id' not in data:
            data['id'] = str(uuid.uuid4())
            
        # 设置创建时间
        data['createdAt'] = datetime.now().isoformat()
        
        # 如果没有提供repeated_triggering，默认设置为0
        if 'repeated_triggering' not in data:
            data['repeated_triggering'] = 0
        
        success = insert_score_trigger(data)
        if success:
            return {"code": 200, "message": "分数触发器创建成功", "data": data}
        else:
            return {"code": 400, "message": "分数触发器创建失败"}
    except Exception as e:
        return {"code": 400, "message": str(e)}

@app.put("/score-trigger/{trigger_id}")
async def update_score_trigger_by_id(trigger_id: str, request: Request):
    """更新指定ID的分数触发器"""
    try:
        data = await request.json()
        
        # 确保不修改ID
        if 'id' in data and data['id'] != trigger_id:
            return {"code": 400, "message": "不允许修改分数触发器ID"}
        
        success = update_score_trigger(trigger_id, data)
        if success:
            updated_trigger = get_score_trigger(trigger_id)
            return {"code": 200, "message": "分数触发器更新成功", "data": updated_trigger}
        else:
            return {"code": 404, "message": "分数触发器不存在或更新失败"}
    except Exception as e:
        return {"code": 400, "message": str(e)}

@app.delete("/score-trigger/{trigger_id}")
async def delete_score_trigger_by_id(trigger_id: str,current_user: TokenData = Depends(get_any_user)):
    success = delete_score_trigger(trigger_id)
    if success:
        return {"success": True, "message": "触发器已成功删除"}
    else:
        return {"success": False, "message": "删除触发器失败，可能该触发器不存在"}

# 语义触发器相关 API
@app.get("/semantic-triggers", response_class=JSONResponse)
async def get_semantic_triggers(current_user: TokenData = Depends(get_any_user)):
    try:
        triggers = get_all_semantic_triggers()
        return {"success": True, "data": triggers}
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"success": False, "message": f"获取语义触发器列表失败: {str(e)}"}
        )

@app.get("/semantic-trigger/{trigger_id}", response_class=JSONResponse)
async def get_semantic_trigger_by_id(trigger_id: str):
    try:
        trigger = get_semantic_trigger(trigger_id)
        if trigger:
            return {"success": True, "data": trigger}
        else:
            return JSONResponse(
                status_code=404,
                content={"success": False, "message": "未找到指定的语义触发器"}
            )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"success": False, "message": f"获取语义触发器失败: {str(e)}"}
        )

@app.post("/semantic-trigger", response_class=JSONResponse)
async def add_semantic_trigger(request: Request,current_user: TokenData = Depends(get_any_user)):
    try:
        data = await request.json()
        required_fields = ["semantic_trigger_name", "semantic_content", "action", "repeated_triggering"]
        for field in required_fields:
            if field not in data:
                return JSONResponse(
                    status_code=400,
                    content={"success": False, "message": f"缺少必填字段: {field}"}
                )
        
        trigger_data = {
            "semantic_trigger_name": data["semantic_trigger_name"],
            "semantic_content": data["semantic_content"],
            "action": data["action"],
            "repeated_triggering": data["repeated_triggering"],
            "createdAt": datetime.now().isoformat()
        }
        
        # 添加可选的explanation字段
        if "explanation" in data:
            trigger_data["explanation"] = data["explanation"]
        
        success = insert_semantic_trigger(trigger_data)
        if success:
            return {"success": True, "message": "语义触发器添加成功", "data": trigger_data}
        else:
            return JSONResponse(
                status_code=500,
                content={"success": False, "message": "添加语义触发器失败"}
            )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"success": False, "message": f"添加语义触发器失败: {str(e)}"}
        )

@app.put("/semantic-trigger/{trigger_id}", response_class=JSONResponse)
async def update_semantic_trigger_by_id(trigger_id: str, request: Request,current_user: TokenData = Depends(get_any_user)):
    try:
        data = await request.json()
        allowed_fields = ["semantic_trigger_name", "semantic_content", "action", "repeated_triggering", "explanation"]
        updates = {k: v for k, v in data.items() if k in allowed_fields}
        
        success = update_semantic_trigger(trigger_id, updates)
        if success:
            return {"success": True, "message": "语义触发器更新成功"}
        else:
            return JSONResponse(
                status_code=404,
                content={"success": False, "message": "更新失败，可能该语义触发器不存在"}
            )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"success": False, "message": f"更新语义触发器失败: {str(e)}"}
        )

@app.delete("/semantic-trigger/{trigger_id}", response_class=JSONResponse)
async def delete_semantic_trigger_by_id(trigger_id: str):
    success = delete_semantic_trigger(trigger_id)
    if success:
        return {"success": True, "message": "语义触发器已成功删除"}
    else:
        return {"success": False, "message": "删除语义触发器失败，可能该触发器不存在"}

# 虚拟HR API端点
@app.get("/virtual-hrs", response_class=JSONResponse)
async def get_virtual_hrs(current_user: TokenData = Depends(get_any_user)):
    if current_user.user_type == "admin":
        virtual_hrs = get_all_virtual_hrs("")
    else:
        virtual_hrs = get_all_virtual_hrs(current_user.id)
    return virtual_hrs

@app.get("/virtual-hr/{hr_id}", response_class=JSONResponse)
async def get_virtual_hr_by_id(hr_id: str,current_user: TokenData = Depends(get_any_user)):
    if current_user.user_type == "admin":
        virtual_hr = get_virtual_hr(hr_id,"")
    else:
        virtual_hr = get_virtual_hr(hr_id,current_user.id)
    if virtual_hr:
        return virtual_hr
    else:
        return JSONResponse(
            status_code=404,
            content={"code": 404, "message": "Virtual HR not found"}
        )

@app.post("/virtual-hr", response_class=JSONResponse)
async def add_virtual_hr(request: Request,current_user: TokenData = Depends(get_any_user)):
    if current_user.user_type != "tenant":
        return {"code": 403, "message": "只有租户可以添加虚拟HR"}
    data = await request.json()
    try:
        hr_id = data.get("id", str(uuid.uuid4()))
        name = data.get("name")
        prompt = data.get("prompt")
                
        if not name:
            return JSONResponse(
                status_code=400,
                content={"code": 400, "message": "Name is required"}
            )
            
        hr_data = {
            "id": hr_id,
            "name": name,
            "prompt": prompt,
            "tenant_id": current_user.id,
            "createdAt": datetime.now().isoformat()
        }
        
        success = insert_virtual_hr(hr_data)
        if success:
            return {"code": 200, "message": "Virtual HR created successfully", "id": hr_id}
        else:
            return JSONResponse(
                status_code=400,
                content={"code": 400, "message": "Failed to create Virtual HR"}
            )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"Error creating Virtual HR: {str(e)}"}
        )

@app.put("/virtual-hr/{hr_id}", response_class=JSONResponse)
async def update_virtual_hr_by_id(hr_id: str, request: Request,current_user: TokenData = Depends(get_any_user)):
    if current_user.user_type != "tenant":
        return {"code": 403, "message": "只有租户可以更新虚拟HR"}
    data = await request.json()
    try:
        updates = {}
        if "name" in data:
            updates["name"] = data["name"]
        if "prompt" in data:
            updates["prompt"] = data["prompt"]
            
        if not updates:
            return {"code": 200, "message": "没有更新数据"}
            
        success = update_virtual_hr(hr_id, updates,current_user.id)
        if success:
            return {"code": 200, "message": "虚拟HR更新成功"}
        else:
            return JSONResponse(
                status_code=404,
                content={"code": 404, "message": "虚拟HR不存在或更新失败"}
            )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"更新虚拟HR失败: {str(e)}"}
        )

@app.delete("/virtual-hr/{hr_id}", response_class=JSONResponse)
async def delete_virtual_hr_by_id(hr_id: str,current_user: TokenData = Depends(get_any_user)):
    if current_user.user_type != "tenant":
        return {"code": 403, "message": "只有租户可以删除虚拟HR"}
    success = delete_virtual_hr(hr_id,current_user.id)
    if success:
        return {"code": 200, "message": "虚拟HR删除成功"}
    else:
        return JSONResponse(
            status_code=404,
            content={"code": 404, "message": "虚拟HR不存在或删除失败"}
        )


# 新增的分类器API路由
@app.get("/semantic-classifiers", response_class=JSONResponse)
async def get_semantic_classifiers(current_user: TokenData = Depends(get_any_user)):
    try:
        classifiers = get_all_semantic_classifiers()
        return {"code": 200, "data": classifiers}
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"获取语义分类器失败: {str(e)}"}
        )

@app.get("/semantic-classifier/{classifier_id}", response_class=JSONResponse)
async def get_semantic_classifier_by_id(classifier_id: str,current_user: TokenData = Depends(get_any_user)):
    try:
        classifier = get_semantic_classifier(classifier_id)
        if not classifier:
            return JSONResponse(
                status_code=404,
                content={"code": 404, "message": f"未找到ID为 {classifier_id} 的语义分类器"}
            )
        
        return {"code": 200, "data": classifier}
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"获取语义分类器详情失败: {str(e)}"}
        )

@app.post("/semantic-classifier", response_class=JSONResponse)
async def add_semantic_classifier(request: Request,current_user: TokenData = Depends(get_any_user)):
    try:
        data = await request.json()
        required_fields = ["name"]
        
        for field in required_fields:
            if field not in data:
                return JSONResponse(
                    status_code=400,
                    content={"code": 400, "message": f"缺少必填字段: {field}"}
                )
        
        classifier_data = {
            "name": data["name"],
            "description": data.get("description", "")
        }
        
        classifier_id = insert_semantic_classifier(classifier_data)
        if not classifier_id:
            return JSONResponse(
                status_code=500,
                content={"code": 500, "message": "创建语义分类器失败"}
            )
        
        classifier = get_semantic_classifier(classifier_id)
        return {"code": 200, "message": "语义分类器创建成功", "data": classifier}
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"创建语义分类器失败: {str(e)}"}
        )

@app.put("/semantic-classifier/{classifier_id}", response_class=JSONResponse)
async def main_update_semantic_classifier_by_id(classifier_id: str, request: Request):
    try:
        data = await request.json()
        
        classifier = get_semantic_classifier(classifier_id)
        if not classifier:
            return JSONResponse(
                status_code=404,
                content={"code": 404, "message": f"未找到ID为 {classifier_id} 的语义分类器"}
            )
        
        updates = {}
        if "name" in data:
            updates["name"] = data["name"]
        if "description" in data:
            updates["description"] = data["description"]
        
        if updates:
            success = update_semantic_classifier(classifier_id, updates)
            if not success:
                return JSONResponse(
                    status_code=500,
                    content={"code": 500, "message": "更新语义分类器失败"}
                )
        
        updated_classifier = get_semantic_classifier(classifier_id)
        return {"code": 200, "message": "语义分类器更新成功", "data": updated_classifier}
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"更新语义分类器失败: {str(e)}"}
        )

@app.delete("/semantic-classifier/{classifier_id}", response_class=JSONResponse)
async def delete_semantic_classifier_by_id(classifier_id: str,current_user: TokenData = Depends(get_any_user)):
    try:
        classifier = get_semantic_classifier(classifier_id)
        if not classifier:
            return JSONResponse(
                status_code=404,
                content={"code": 404, "message": f"未找到ID为 {classifier_id} 的语义分类器"}
            )
        
        success = delete_semantic_classifier(classifier_id)
        if not success:
            return JSONResponse(
                status_code=500,
                content={"code": 500, "message": "删除语义分类器失败"}
            )
        
        return {"code": 200, "message": "语义分类器删除成功"}
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"删除语义分类器失败: {str(e)}"}
        )

@app.get("/semantic-classifier/{classifier_id}/questions", response_class=JSONResponse)
async def get_questions_by_classifier_id(classifier_id: str,current_user: TokenData = Depends(get_any_user)):
    try:
        classifier = get_semantic_classifier(classifier_id)
        if not classifier:
            return JSONResponse(
                status_code=404,
                content={"code": 404, "message": f"未找到ID为 {classifier_id} 的语义分类器"}
            )
        
        questions = get_questions_by_classifier(classifier_id)
        return {"code": 200, "data": questions}
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"获取分类器问题失败: {str(e)}"}
        )

@app.post("/semantic-classifier-question", response_class=JSONResponse)
async def add_semantic_classifier_question(request: Request,current_user: TokenData = Depends(get_any_user)):
    try:
        data = await request.json()
        required_fields = ["classifier_id", "content", "score"]
        
        for field in required_fields:
            if field not in data:
                return JSONResponse(
                    status_code=400,
                    content={"code": 400, "message": f"缺少必填字段: {field}"}
                )
        
        classifier = get_semantic_classifier(data["classifier_id"])
        if not classifier:
            return JSONResponse(
                status_code=404,
                content={"code": 404, "message": f"未找到ID为 {data['classifier_id']} 的语义分类器"}
            )
        
        question_data = {
            "classifier_id": data["classifier_id"],
            "content": data["content"],
            "score": data["score"]
        }
        
        question_id = insert_semantic_classifier_question(question_data)
        if not question_id:
            return JSONResponse(
                status_code=500,
                content={"code": 500, "message": "创建分类器问题失败"}
            )
        
        question = get_semantic_classifier_question(question_id)
        return {"code": 200, "message": "分类器问题创建成功", "data": question}
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"创建分类器问题失败: {str(e)}"}
        )

@app.get("/semantic-classifier-question/{question_id}", response_class=JSONResponse)
async def get_semantic_classifier_question_by_id(question_id: str,current_user: TokenData = Depends(get_any_user)):
    try:
        question = get_semantic_classifier_question(question_id)
        if not question:
            return JSONResponse(
                status_code=404,
                content={"code": 404, "message": f"未找到ID为 {question_id} 的分类器问题"}
            )
        
        return {"code": 200, "data": question}
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"获取分类器问题失败: {str(e)}"}
        )

@app.put("/semantic-classifier-question/{question_id}", response_class=JSONResponse)
async def update_semantic_classifier_question_by_id(question_id: str, request: Request,current_user: TokenData = Depends(get_any_user)):
    try:
        data = await request.json()
        
        question = get_semantic_classifier_question(question_id)
        if not question:
            return JSONResponse(
                status_code=404,
                content={"code": 404, "message": f"未找到ID为 {question_id} 的分类器问题"}
            )
        
        updates = {}
        if "content" in data:
            updates["content"] = data["content"]
        if "score" in data:
            updates["score"] = data["score"]
        
        if updates:
            success = update_semantic_classifier_question(question_id, updates)
            if not success:
                return JSONResponse(
                    status_code=500,
                    content={"code": 500, "message": "更新分类器问题失败"}
                )
        
        updated_question = get_semantic_classifier_question(question_id)
        return {"code": 200, "message": "分类器问题更新成功", "data": updated_question}
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"更新分类器问题失败: {str(e)}"}
        )

@app.delete("/semantic-classifier-question/{question_id}", response_class=JSONResponse)
async def delete_semantic_classifier_question_by_id(question_id: str,current_user: TokenData = Depends(get_any_user)):
    try:
        question = get_semantic_classifier_question(question_id)
        if not question:
            return JSONResponse(
                status_code=404,
                content={"code": 404, "message": f"未找到ID为 {question_id} 的分类器问题"}
            )
        
        success = delete_semantic_classifier_question(question_id)
        if not success:
            return JSONResponse(
                status_code=500,
                content={"code": 500, "message": "删除分类器问题失败"}
            )
        
        return {"code": 200, "message": "分类器问题删除成功"}
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"删除分类器问题失败: {str(e)}"}
        )

@app.get("/answer-classifiers", response_class=JSONResponse)
async def get_answer_classifiers(current_user: TokenData = Depends(get_any_user)):
    try:
        classifiers = get_all_answer_classifiers()
        return {"code": 200, "data": classifiers}
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"获取答案分类器失败: {str(e)}"}
        )

@app.get("/answer-classifier/{classifier_id}", response_class=JSONResponse)
async def get_answer_classifier_by_id(classifier_id: str,current_user: TokenData = Depends(get_any_user)):
    try:
        classifier = get_answer_classifier(classifier_id)
        if not classifier:
            return JSONResponse(
                status_code=404,
                content={"code": 404, "message": f"未找到ID为 {classifier_id} 的答案分类器"}
            )
        
        return {"code": 200, "data": classifier}
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"获取答案分类器详情失败: {str(e)}"}
        )

@app.post("/answer-classifier", response_class=JSONResponse)
async def add_answer_classifier(request: Request,current_user: TokenData = Depends(get_any_user)):
    try:
        data = await request.json()
        required_fields = ["name", "active_multiplier", "neutral_multiplier", "negative_multiplier"]
        
        for field in required_fields:
            if field not in data:
                return JSONResponse(
                    status_code=400,
                    content={"code": 400, "message": f"缺少必填字段: {field}"}
                )
        
        # 检查是否已存在同名的答案分类器
        existing = get_answer_classifier_by_name(data["name"])
        if existing:
            return JSONResponse(
                status_code=400,
                content={"code": 400, "message": f"已存在名称为 '{data['name']}' 的答案分类器"}
            )
        
        classifier_data = {
            "name": data["name"],
            "description": data.get("description", ""),
            "active_multiplier": data["active_multiplier"],
            "neutral_multiplier": data["neutral_multiplier"],
            "negative_multiplier": data["negative_multiplier"]
        }
        
        classifier_id = insert_answer_classifier(classifier_data)
        if not classifier_id:
            return JSONResponse(
                status_code=500,
                content={"code": 500, "message": "创建答案分类器失败"}
            )
        
        classifier = get_answer_classifier(classifier_id)
        return {"code": 200, "message": "答案分类器创建成功", "data": classifier}
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"创建答案分类器失败: {str(e)}"}
        )

@app.put("/answer-classifier/{classifier_id}", response_class=JSONResponse)
async def update_answer_classifier_by_id(classifier_id: str, request: Request,current_user: TokenData = Depends(get_any_user)):
    try:
        data = await request.json()
        
        classifier = get_answer_classifier(classifier_id)
        if not classifier:
            return JSONResponse(
                status_code=404,
                content={"code": 404, "message": f"未找到ID为 {classifier_id} 的答案分类器"}
            )
        
        updates = {}
        
        # 检查名称更新
        if "name" in data:
            # 如果名称已更改，检查是否与其他分类器冲突
            if data["name"] != classifier.get("name"):
                existing = get_answer_classifier_by_name(data["name"])
                if existing and existing["id"] != classifier_id:
                    return JSONResponse(
                        status_code=400,
                        content={"code": 400, "message": f"已存在名称为 '{data['name']}' 的答案分类器"}
                    )
            updates["name"] = data["name"]
        
        # 更新描述
        if "description" in data:
            updates["description"] = data["description"]
            
        # 更新倍数值
        if "active_multiplier" in data:
            updates["active_multiplier"] = data["active_multiplier"]
            
        if "neutral_multiplier" in data:
            updates["neutral_multiplier"] = data["neutral_multiplier"]
            
        if "negative_multiplier" in data:
            updates["negative_multiplier"] = data["negative_multiplier"]
        
        if updates:
            success = update_answer_classifier(classifier_id, updates)
            if not success:
                return JSONResponse(
                    status_code=500,
                    content={"code": 500, "message": "更新答案分类器失败"}
                )
        
        updated_classifier = get_answer_classifier(classifier_id)
        return {"code": 200, "message": "答案分类器更新成功", "data": updated_classifier}
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"更新答案分类器失败: {str(e)}"}
        )

@app.delete("/answer-classifier/{classifier_id}", response_class=JSONResponse)
async def delete_answer_classifier_by_id(classifier_id: str,current_user: TokenData = Depends(get_any_user)):
    try:
        classifier = get_answer_classifier(classifier_id)
        if not classifier:
            return JSONResponse(
                status_code=404,
                content={"code": 404, "message": f"未找到ID为 {classifier_id} 的答案分类器"}
            )
        
        success = delete_answer_classifier(classifier_id)
        if not success:
            return JSONResponse(
                status_code=500,
                content={"code": 500, "message": "删除答案分类器失败"}
            )
        
        return {"code": 200, "message": "答案分类器删除成功"}
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"code": 500, "message": f"删除答案分类器失败: {str(e)}"}
        )
    
@app.get("/user/current_prompt/{userid}")
async def get_user_current_prompt(userid: str,current_user: TokenData = Depends(get_any_user)):
    if current_user.user_type == "admin":
        user = get_user(userid,"")
        tenant_id = ""
    else:
        user = get_user(userid,current_user.id)
        tenant_id = current_user.id
    if user:
        # try:
        prompt = await create_prompt(user,tenant_id)
        return {"code": 200, "message": "success", "prompt": prompt}
        # except Exception as e:
        #     return {"code": 500, "message": "Failed to generate prompt "+str(e)}
    else:
        return {"code": 400, "message": "用户不存在"}

@app.post("/api/logout07082", response_class=JSONResponse)
async def logout_api():
    # 实际上，后端不需要做任何事情，因为JWT是无状态的
    # 客户端只需要删除本地存储的令牌即可
    return {"message": "登出成功", "code": 200}

# APIKey相关API端点
@app.post("/api/tenant/apikey", response_class=JSONResponse)
async def create_apikey(request: Request, current_user: TokenData = Depends(get_tenant_user)):
    try:
        data = await request.json()
        name = data.get("name")
        tenant_id = current_user.id
        
        if not name:
            return JSONResponse(
                status_code=400,
                content={"success": False, "message": "APIKey名称不能为空"}
            )
        
        # 创建新的APIKey
        result = insert_tenant_apikey(tenant_id, name)
        if result:
            return {"success": True, "message": "APIKey创建成功", "data": result}
        else:
            return JSONResponse(
                status_code=500,
                content={"success": False, "message": "创建APIKey失败"}
            )
    except Exception as e:
        print(f"创建APIKey失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"success": False, "message": f"创建APIKey失败: {str(e)}"}
        )

@app.get("/api/tenant_apikey/apikeys", response_class=JSONResponse)
async def get_tenant_apikeys(current_user: TokenData = Depends(get_tenant_user)):
    try:
        tenant_id = current_user.id
        apikeys = get_apikeys_by_tenant(tenant_id)
        for apikey in apikeys:
            apikey["displayed_key"] = apikey["apikey"]
        
        return {"success": True, "data": apikeys}
    except Exception as e:
        print(f"获取APIKey列表失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"success": False, "message": f"获取APIKey列表失败: {str(e)}"}
        )

@app.delete("/api/tenant/apikey/{apikey_id}", response_class=JSONResponse)
async def delete_tenant_apikey(apikey_id: str, current_user: TokenData = Depends(get_tenant_user)):
    try:
        result = delete_apikey(apikey_id)
        if result:
            return {"success": True, "message": "APIKey删除成功"}
        else:
            return JSONResponse(
                status_code=404,
                content={"success": False, "message": "APIKey不存在或删除失败"}
            )
    except Exception as e:
        print(f"删除APIKey失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"success": False, "message": f"删除APIKey失败: {str(e)}"}
        )

# Token用量统计API端点
@app.get("/api/tenant_apikey/token-usage", response_class=JSONResponse)
async def get_token_usage(
    request: Request,
    current_user: TokenData = Depends(get_tenant_user)
):
    try:
        tenant_id = current_user.id
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")
        
        # 获取APIKey级别的统计
        usage_data = get_token_usage_by_tenant(tenant_id, start_date, end_date)
        
        # 获取每日使用量统计
        daily_data = get_token_usage_daily(tenant_id, start_date, end_date)
        
        # 计算总量
        total_uptoken = sum(item["uptoken"] for item in usage_data)
        total_downtoken = sum(item["downtoken"] for item in usage_data)
        total_tokens = total_uptoken + total_downtoken
        
        return {
            "success": True, 
            "data": {
                "by_apikey": usage_data,
                "by_date": daily_data,
                "summary": {
                    "total_uptoken": total_uptoken,
                    "total_downtoken": total_downtoken,
                    "total_tokens": total_tokens
                }
            }
        }
    except Exception as e:
        print(f"获取Token用量统计失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"success": False, "message": f"获取Token用量统计失败: {str(e)}"}
        )

@app.get("/api/tenant_apikey/hourly-token-usage", response_class=JSONResponse)
async def get_hourly_token_usage_api(
    request: Request,
    current_user: TokenData = Depends(get_tenant_user)
):
    try:
        apikey = request.query_params.get("apikey")
        date = request.query_params.get("date")
        
        if not apikey or not date:
            return JSONResponse(
                status_code=400,
                content={"success": False, "message": "缺少必要的参数：apikey_id和date"}
            )
        
        # 获取每小时的使用量统计
        hourly_data = get_hourly_token_usage(apikey, date)
        
        return {
            "success": True, 
            "data": hourly_data
        }
    except Exception as e:
        print(f"获取分时Token用量统计失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"success": False, "message": f"获取分时Token用量统计失败: {str(e)}"}
        )


# APIKey管理页面路由
@app.get("/apikey_create")
async def apikey_create(request: Request):
    try:
        from main import templates
        return templates.TemplateResponse("apikey_create.html", {"request": request})
    except Exception as e:
        print(f"APIKey管理页面路由失败: {str(e)}")
        return RedirectResponse(url="/login07082")

@app.get("/apikey_usage", response_class=HTMLResponse)
async def apikey_usage(request: Request):
    from main import templates
    return templates.TemplateResponse("apikey_usage.html", {"request": request})

@app.get("/channel_management", response_class=HTMLResponse)
async def channel_management(request: Request):
    from main import templates
    return templates.TemplateResponse("channel_management.html", {"request": request})

# API路由
@app.get("/api/channel", response_class=JSONResponse)
async def get_channel_api(channel: str, apikey: str = None, current_user: TokenData = Depends(get_tenant_user)):
    try:
        tenant_id = current_user.id
        
        # 构建查询条件
        query_params = {
            "tenant_id": tenant_id,
            "channel_type": channel
        }
        
        # 如果提供了apikey，则添加到查询条件
        if apikey:
            query_params["apikey"] = apikey
        
        # 获取渠道信息
        channel_data = get_channel_by_params(query_params)
        
        if channel_data:
            return {"code": 200, "message": "获取渠道配置成功", "data": channel_data}
        else:
            return {"code": 404, "message": "渠道配置不存在", "data": None}
    except Exception as e:
        print(f"获取渠道配置失败: {str(e)}")
        return {"code": 500, "message": f"获取渠道配置失败: {str(e)}", "data": None}

@app.post("/api/channel", response_class=JSONResponse)
async def save_channel_api(request: Request, current_user: TokenData = Depends(get_tenant_user)):
    try:
        data = await request.json()
        tenant_id = current_user.id
        channel_type = data.get("channel_type")
        apikey = data.get("apikey")
        enable_pre_scenario = data.get("enable_pre_scenario", 0)
        pre_scenario_prompt = data.get("pre_scenario_prompt", "")
        
        # 参数验证
        if not channel_type:
            return {"code": 400, "message": "渠道类型不能为空"}
            
        # 构建查询条件
        query_params = {
            "tenant_id": tenant_id,
            "channel_type": channel_type
        }
        
        # 如果提供了apikey，则添加到查询条件
        if apikey:
            query_params["apikey"] = apikey
            
        # 检查渠道是否已存在
        existing_channel = get_channel_by_params(query_params)
        
        if existing_channel:
            # 更新现有渠道
            success, message = update_channel(existing_channel["id"], {
                "enable_pre_scenario": enable_pre_scenario,
                "pre_scenario_prompt": pre_scenario_prompt
            })
            
            if success:
                return {"code": 200, "message": "更新渠道配置成功"}
            else:
                return {"code": 500, "message": message}
        else:
            # 创建新渠道
            channel_data = {
                "tenant_id": tenant_id,
                "channel_type": channel_type,
                "enable_pre_scenario": enable_pre_scenario,
                "pre_scenario_prompt": pre_scenario_prompt
            }
            
            # 如果提供了apikey，则添加到数据中
            if apikey:
                channel_data["apikey"] = apikey
            
            success, result = insert_channel(channel_data)
            
            if success:
                return {"code": 200, "message": "创建渠道配置成功"}
            else:
                return {"code": 500, "message": result}
    except Exception as e:
        print(f"保存渠道配置失败: {str(e)}")
        return {"code": 500, "message": f"保存渠道配置失败: {str(e)}"}

@app.get("/api/apikeys", response_class=JSONResponse)
async def get_apikeys_api(current_user: TokenData = Depends(get_tenant_user)):
    try:
        tenant_id = current_user.id
        apikeys = get_apikeys_by_tenant(tenant_id)
        return {"code": 200, "message": "获取APIKey列表成功", "data": apikeys}
    except Exception as e:
        print(f"获取APIKey列表失败: {str(e)}")
        return {"code": 500, "message": f"获取APIKey列表失败: {str(e)}", "data": []}

@app.get("/api/channels", response_class=JSONResponse)
async def get_tenant_channels_api(current_user: TokenData = Depends(get_tenant_user)):
    try:
        tenant_id = current_user.id
        channels = get_channels_by_tenant(tenant_id)
        return {"code": 200, "message": "获取渠道列表成功", "data": channels}
    except Exception as e:
        print(f"获取渠道列表失败: {str(e)}")
        return {"code": 500, "message": f"获取渠道列表失败: {str(e)}", "data": []}

@app.delete("/api/channel/{channel_id}", response_class=JSONResponse)
async def delete_channel_api(channel_id: str, current_user: TokenData = Depends(get_tenant_user)):
    try:
        success, message = delete_channel(channel_id)
        
        if success:
            return {"code": 200, "message": "删除渠道配置成功"}
        else:
            return {"code": 404, "message": message}
    except Exception as e:
        print(f"删除渠道配置失败: {str(e)}")
        return {"code": 500, "message": f"删除渠道配置失败: {str(e)}"}


@app.post("/wechat_query_user")
async def wechat_query_user(request: Request):
    data = await request.json()
    apikey = request.headers.get("Authorization").replace("Bearer ","")
    try:
        tenant_id = get_apikey_by_value(apikey).get("tenant_id")
    except Exception as e:
        return {"code": 400, "message": "apikey not found"}

    
    name = data.get("name")
    wxid = data.get("wxid")

    user = get_user_by_wxid(wxid,tenant_id)
    if user:
        return {"code": 200, "message": "success", "userid": user.get("userid")}
    else:
        userid = insert_user({"wxid":wxid,"name":name,"source":"wechat","createdAt":datetime.now().isoformat(),"sceneId":"pre_scenario","positionId":None,"tenant_id":tenant_id})
        return {"code": 200, "message": "success", "userid": userid}
    


@app.post("/wechat_chat")
async def wechat_chat(request: Request):
    data = await request.json()
    apikey = request.headers.get("Authorization").replace("Bearer ","")
    try:
        tenant_id = get_apikey_by_value(apikey).get("tenant_id")
    except Exception as e:
        return {"code": 400, "message": "apikey not found"}
    
    messages = data.get("messages")
    userid = data.get("userid")
    name = data.get("name")
    wxid = data.get("wxid")
    source = data.get("source")

    user = get_user(userid,tenant_id)

    if user.get("positionId") is None:
        channel_config = get_channel_by_tenant_and_type(tenant_id,apikey,source)
        if channel_config:
            if channel_config.get("enable_pre_scenario"):
                pre_prompt = channel_config.get("pre_scenario_prompt")
                user_message = messages[-1]
                # if "timestamp" not in user_message:
                user_message["timestamp"] = int(time.time())
                
                if user.get("real_name"):
                    phone_list = get_phone_by_real_name(user.get("real_name"),tenant_id)
                else:
                    phone_list = []

                pre_prompt = await get_pre_scenario_prompt(pre_prompt,phone_list)
                result_json = await get_result_json(userid,pre_prompt,user_message,apikey,tenant_id)

                content = result_json.get("content")
                new_score = result_json.get("score")
                score_tracking = result_json.get("score_tracking")
                actions = result_json.get("actions")

                update_user(userid,{"score":new_score},tenant_id)
                add_message = [user_message,{"role": "assistant", "content": json.dumps(result_json,ensure_ascii=False),"timestamp":int(time.time())}]
                save_chat_log(user,add_message,new_score,score_tracking,actions,channel="wechat")

                current_scene_id = user.get("sceneId")
                current_user_id = user.get("userid")

                for action in actions:
                    if action.get("action") == "update_user_info":
                        real_name = action.get("params").get("real_name")
                        phone = action.get("params").get("phone")
                        if phone:
                            old_users = get_user_by_phone_and_not_wxid(phone,tenant_id)
                        else:
                            old_users = get_user_by_real_name(real_name,tenant_id)
                        if len(old_users) > 1:
                            phone_list = get_phone_by_real_name(real_name,tenant_id)
                            prompt_pre_scenario = await get_pre_scenario_prompt(channel_config.get("pre_scenario_prompt"),phone_list)
                            result_json_pre_scenario = await get_result_json(current_user_id,prompt_pre_scenario,{"role":"user","content":"请让我确认一下我是哪个手机号，如果系统中没有的话就给我说你没有查询到我的电话，方便告诉一下吗？","timestamp":int(time.time())},apikey,tenant_id)
                            content = result_json_pre_scenario.get("content")
                            new_score = result_json_pre_scenario.get("score")
                            score_tracking = result_json_pre_scenario.get("score_tracking")
                            actions = result_json_pre_scenario.get("actions")
                            update_user(current_user_id,{"score":new_score},tenant_id)
                            add_message = [{"role":"user","content":"请让我确认一下我是哪个手机号，如果系统中没有的话就给我说你没有查询到我的电话，方便告诉一下吗？","timestamp":int(time.time())},{"role": "assistant", "content": json.dumps(result_json_pre_scenario,ensure_ascii=False),"timestamp":int(time.time())}]
                            save_chat_log(get_user(current_user_id),add_message,new_score,score_tracking,actions,channel="wechat")
                        elif len(old_users) == 1:
                            current_user_id = old_users[0]["userid"]
                            # wxid绑定到boss用户上
                            update_user(current_user_id,{"wxid":wxid,"wechat_nickname":name,"real_name":real_name},tenant_id)
                            # 同步聊天记录到old_user
                            chat_logs = get_chatlogs_by_user(userid)
                            last_chatlog_score = chat_logs[-1].get("score")
                            for chat_log in chat_logs:
                                update_chatlog(chat_log.get("id"),{"userid":current_user_id})
                            # 删除临时用户
                            delete_user(userid,tenant_id)
                            current_scene_id = old_users[0]["sceneId"]
                            update_user(current_user_id,action.get("params"),tenant_id)
                            # 触发前置场景结束，再次请求AI回复
                            prompt_switch = await create_prompt(get_user(current_user_id,tenant_id),tenant_id)
                            # 获取激活对话命令
                            activate_command = get_tenant_global_settings(tenant_id).get("activate_command")
                            if not activate_command:
                                activate_command = "请继续吧"
                            result_json_switch = await get_result_json(current_user_id,prompt_switch,{"role":"user","content":activate_command,"timestamp":int(time.time())},apikey,tenant_id)
                            content = result_json_switch.get("content")
                            # new_score = result_json_switch.get("score")
                            # 获取绑定后用户的分数
                            original_score = get_user(current_user_id,tenant_id).get("score")
                            # 生成切换场景的score_tracking
                            score_tracking = {"original_score": last_chatlog_score, "current_score": original_score, "reason": ["触发用户绑定成功-同步分数至" + str(original_score) + "分"]}
                            # result_json = result_json_switch
                            actions = result_json_switch.get("actions")
                            result_json = {"content": content, "score": original_score, "score_tracking": score_tracking, "actions": actions}
                            # score_tracking = result_json.get("score_tracking")
                            update_user(current_user_id,{"score":original_score},tenant_id)
                            add_message = [{"role":"user","content":activate_command,"timestamp":int(time.time())},{"role": "assistant", "content": json.dumps(result_json_switch,ensure_ascii=False),"timestamp":int(time.time())}]
                            save_chat_log(get_user(current_user_id,tenant_id),add_message,original_score,score_tracking,actions,channel="wechat")
                        else:
                            # 没有找到旧用户，则将用户信息插入到系统中【这里也为后面微信用户直接关联岗位提供后续开发，暂不实现】
                            current_user_id = userid
                            current_scene_id = "pre_scenario"
                            update_user(current_user_id,action.get("params"),tenant_id)
                return {"code": 200, "message": "success", "score": new_score, "name": name,"content":content,"is_reply": True,"sence":get_scene(current_scene_id),"actions":actions,"score_tracking":score_tracking}
            else:
                return {"code": 400, "message": "微信渠道必须启用预场景,请检查渠道配置"}
        else:
            return {"code": 400, "message": "微信渠道必须要进行配置,请检查渠道配置"}
    else:
        # 在微信渠道要打通和boss的聊天记录
        boss_chatlogs = get_chatlog_by_channel(userid,"boss")
        add_boss_messages = []
        for boss_chatlog in boss_chatlogs:
            add_boss_messages.append({"role":boss_chatlog.get("role"),"content":boss_chatlog.get("content"),"timestamp":boss_chatlog.get("createdAt")})
        messages = add_boss_messages + messages

        current_scene_id = user.get("sceneId")
        user_message = messages[-1]
        if "timestamp" not in user_message:
            user_message["timestamp"] = int(time.time())
        prompt = await create_prompt(user,tenant_id)
        result_json = await get_result_json(userid,prompt,user_message,apikey,tenant_id)
        content = result_json.get("content")
        new_score = result_json.get("score")

        # swith_sceneId = await switch_scene(user,new_score)

        # if current_scene_id != swith_sceneId:
        #     print("检测到切换场景,更换提示词,再次请求AI回复")
        #     prompt_switch = await create_prompt(user,tenant_id)
        #     result_json_switch = await get_result_json(userid,prompt_switch,user_message,apikey,tenant_id)
        #     # result_json_switch["content"] += result_json.get("content")
        #     content = result_json_switch.get("content")
        #     new_score = result_json_switch.get("score")
        #     result_json = result_json_switch
        
        # score_tracking = result_json.get("score_tracking")
        # actions = result_json.get("actions")
        # update_user(userid,{"score":new_score},tenant_id)

        # add_message = [user_message,{"role": "assistant", "content": json.dumps(result_json,ensure_ascii=False),"timestamp":int(time.time())}]
        # save_chat_log(user,add_message,new_score,score_tracking,actions,channel="wechat")
        score_tracking = result_json.get("score_tracking")
        actions = result_json.get("actions")
        add_message = [user_message,{"role": "assistant", "content": json.dumps(result_json,ensure_ascii=False),"timestamp":int(time.time())}]
        save_chat_log(user,add_message,new_score,score_tracking,actions,channel="wechat")
        update_user(userid,{"score":new_score},tenant_id)

        swith_sceneId = current_scene_id
        result_dict = call_action(userid,actions)
        if "target_scene_id" in result_dict.keys():
            swith_sceneId = result_dict["target_scene_id"]
            user["sceneId"] = swith_sceneId
        if current_scene_id != swith_sceneId:
            print("检测到切换场景,更换提示词,再次请求AI回复")
            if "activate_message" in result_dict.keys() and result_dict["activate_message"] != "":
                user_message = {"role":"user","content":result_dict["activate_message"],"timestamp":int(time.time())}
            else:
                user_message = {"role":"user","content":"请继续吧","timestamp":int(time.time())}
            prompt_switch = await create_prompt(user,tenant_id)
            result_json_switch = await get_result_json(userid,prompt_switch,user_message,apikey,tenant_id)
            content = result_json_switch.get("content")
            # new_score = result_json_switch.get("score")
            if result_dict["score"] == 0:
                switech_score = new_score
            else:
                switech_score = result_dict["score"]
            # score_tracking = result_json_switch.get("score_tracking")
            score_tracking = {"original_score": new_score, "current_score": switech_score, "reason": [f"执行场景切换,按照指令赋予分数{switech_score}分"]}
            actions = result_json_switch.get("actions")
            result_json = result_json_switch
            result_json["score_tracking"] = score_tracking
            result_json["score"] = switech_score
            update_user(userid,{"score":switech_score},tenant_id)
            add_message = [user_message,{"role": "assistant", "content": json.dumps(result_json,ensure_ascii=False),"timestamp":int(time.time())}]
            save_chat_log(user,add_message,switech_score,score_tracking,actions,channel="wechat")
            new_score = switech_score

        return {"code": 200, "message": "success", "score": new_score, "name": name,"content":content,"is_reply": True,"sence":get_scene(swith_sceneId),"actions":actions,"score_tracking":score_tracking}