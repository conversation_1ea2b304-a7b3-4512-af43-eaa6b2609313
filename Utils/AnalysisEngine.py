import asyncio
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any
from DadabaseControl.DatabaseControl4 import *
from DadabaseControl.DatabaseControl import get_user, get_chatlogs_by_user
from ChatApi.ChatApi import reply_chat
import traceback


async def get_user_chat_messages(user_id: str, tenant_id: str, days: int = 1) -> List[Dict]:
    """获取用户在指定天数内的聊天记录"""
    try:
        import time

        # 计算时间范围 - 使用时间戳
        end_timestamp = int(time.time())
        start_timestamp = end_timestamp - (days * 24 * 60 * 60)

        print(f"获取用户 {user_id} 最近 {days} 天的聊天记录")
        print(f"时间范围: {start_timestamp} - {end_timestamp}")

        # 获取聊天记录
        chat_logs = get_chatlogs_by_user(user_id, tenant_id)

        # 过滤时间范围内的记录
        filtered_logs = []
        for log in chat_logs:
            created_at = log.get('createdAt', '')
            if created_at and created_at.isdigit():
                log_timestamp = int(created_at)
                if start_timestamp <= log_timestamp <= end_timestamp:
                    filtered_logs.append(log)
            else:
                # 如果时间戳格式不对，也包含进来
                filtered_logs.append(log)

        print(f"过滤后的聊天记录数: {len(filtered_logs)}")
        return filtered_logs
    except Exception as e:
        print(f"获取用户聊天记录失败: {e}")
        import traceback
        traceback.print_exc()
        return []


def format_chat_messages(chat_logs: List[Dict]) -> str:
    """将聊天记录格式化为分析用的文本"""
    try:
        formatted_messages = []

        for log in chat_logs:
            role = log.get('role', '')
            content = log.get('parsed_content', '') or log.get('content', '')
            formatted_time = log.get('formatted_time', '') or log.get('createdAt', '')

            if role == 'user':
                formatted_messages.append(f"用户 ({formatted_time}): {content}")
            elif role == 'assistant':
                formatted_messages.append(f"AI ({formatted_time}): {content}")
            else:
                # 如果role字段不明确，根据内容判断
                if content:
                    formatted_messages.append(f"对话 ({formatted_time}): {content}")

        result = "\n".join(formatted_messages)
        print(f"格式化后的聊天记录长度: {len(result)} 字符")
        return result
    except Exception as e:
        print(f"格式化聊天记录失败: {e}")
        import traceback
        traceback.print_exc()
        return ""


async def analyze_user_behavior(user_id: str, tenant_id: str, prompt: str, days: int = 1, previous_summary: str = None) -> Dict[str, Any]:
    """分析单个用户的行为"""
    try:
        # 获取用户信息
        user_info = get_user(user_id, tenant_id)
        if not user_info:
            return {
                'success': False,
                'error': '用户不存在',
                'user_id': user_id
            }
        
        # 获取聊天记录
        chat_logs = await get_user_chat_messages(user_id, tenant_id, days)
        if not chat_logs:
            return {
                'success': False,
                'error': '没有聊天记录',
                'user_id': user_id,
                'user_name': user_info.get('real_name') or user_info.get('name', ''),
                'user_phone': user_info.get('phone', ''),
                'message_count': 0
            }
        
        # 格式化聊天记录
        formatted_chat = format_chat_messages(chat_logs)
        
        # 构建分析提示词
        analysis_prompt = f"""
{prompt}

以下是用户的聊天记录：
{formatted_chat}

"""

        # 如果有上一次的总结，加入到提示词中
        if previous_summary:
            analysis_prompt += f"""
上一次的分析总结：
{previous_summary}

请结合上一次的分析总结和本次的聊天记录，进行迭代分析，重点关注变化和趋势。
"""
        else:
            analysis_prompt += "请根据以上聊天记录，对用户进行分析总结。"
        
        # 调用AI进行分析
        analysis_result = await reply_chat(analysis_prompt, [])
        
        return {
            'success': True,
            'user_id': user_id,
            'user_name': user_info.get('real_name') or user_info.get('name', ''),
            'user_phone': user_info.get('phone', ''),
            'message_count': len(chat_logs),
            'analysis_content': formatted_chat,
            'analysis_summary': analysis_result
        }
        
    except Exception as e:
        print(f"分析用户行为失败: {e}")
        traceback.print_exc()
        return {
            'success': False,
            'error': str(e),
            'user_id': user_id
        }


async def execute_analysis_task(analysis_id: str, tenant_id: str):
    """执行分析任务"""
    log_id = None
    try:
        print(f"开始执行分析任务: analysis_id={analysis_id}, tenant_id={tenant_id}")

        # 创建执行日志记录
        from DadabaseControl.DatabaseControl4 import create_analysis_execution_log, update_analysis_execution_log
        success, log_id = create_analysis_execution_log(analysis_id, tenant_id)
        if not success:
            print(f"创建执行日志失败: {log_id}")
            return

        print(f"创建执行日志成功: {log_id}")

        # 获取分析配置
        analysis_config = get_scheduled_analysis(analysis_id, tenant_id)
        if not analysis_config:
            print(f"分析配置不存在: {analysis_id}")
            update_analysis_execution_log(log_id, "failed", error_message="分析配置不存在")
            return

        print(f"分析配置: {analysis_config['name']}, 时间范围: {analysis_config['time_range_days']}天")

        # 获取上一次的分析总结（用于迭代分析）
        from DadabaseControl.DatabaseControl4 import get_last_analysis_summary
        previous_summary = get_last_analysis_summary(analysis_id, tenant_id)
        if previous_summary:
            print(f"找到上一次分析总结，将进行迭代分析")
        else:
            print(f"未找到上一次分析总结，将进行首次分析")

        # 获取待分析的用户列表，根据职位分类筛选
        job_classification_id = analysis_config.get('job_classification_id', '')
        users = get_users_for_analysis(tenant_id, analysis_config['time_range_days'], job_classification_id)
        total_users = len(users)

        print(f"找到 {total_users} 个用户需要分析")

        if total_users == 0:
            print("没有找到需要分析的用户")
            update_analysis_execution_log(log_id, "completed",
                                        total_users=0, successful_analyses=0, failed_analyses=0)
            return
        
        # 更新执行日志中的总用户数
        update_analysis_execution_log(log_id, "running", total_users=total_users)

        successful_analyses = 0
        failed_analyses = 0
        analysis_results = []

        # 逐个分析用户
        for i, user in enumerate(users, 1):
            try:
                print(f"正在分析用户 {i}/{total_users}: {user.get('real_name') or user.get('name', user['userid'])}")

                result = await analyze_user_behavior(
                    user['userid'],
                    tenant_id,
                    analysis_config['prompt'],
                    analysis_config['time_range_days'],
                    previous_summary
                )

                if result['success']:
                    # 保存分析结果
                    save_analysis_result(
                        analysis_id, tenant_id, result['user_id'],
                        result['user_name'], result['user_phone'], result['message_count'],
                        result['analysis_content'], result['analysis_summary']
                    )
                    analysis_results.append(result)
                    successful_analyses += 1
                    print(f"✅ 用户 {result['user_name']} 分析成功")
                else:
                    failed_analyses += 1
                    print(f"❌ 用户 {user['userid']} 分析失败: {result.get('error', '未知错误')}")

                # 实时更新执行状态
                update_analysis_execution_log(
                    log_id, "running",
                    total_users=total_users,
                    successful_analyses=successful_analyses,
                    failed_analyses=failed_analyses
                )

            except Exception as e:
                failed_analyses += 1
                print(f"❌ 分析用户 {user['userid']} 时发生异常: {e}")

                # 即使出现异常也要更新状态
                update_analysis_execution_log(
                    log_id, "running",
                    total_users=total_users,
                    successful_analyses=successful_analyses,
                    failed_analyses=failed_analyses
                )
        
        # 发送邮件（如果配置了邮件接收者）
        email_sent = False
        if analysis_config.get('email_recipients'):
            try:
                from Utils.EmailSender import send_analysis_report
                email_sent = await send_analysis_report(
                    tenant_id, analysis_config, analysis_results
                )
            except Exception as e:
                print(f"发送邮件失败: {e}")

        # 更新执行日志为完成状态
        update_analysis_execution_log(
            log_id, "completed",
            total_users=total_users,
            successful_analyses=successful_analyses,
            failed_analyses=failed_analyses,
            email_sent=email_sent
        )

        # 更新分析配置的最后执行时间和下次执行时间
        from datetime import datetime, timedelta
        now = datetime.now()
        last_execution_time = now.strftime("%Y-%m-%d %H:%M:%S")

        # 计算下次执行时间（根据分析天数确定间隔）
        schedule_time = analysis_config.get('schedule_time', '09:00')
        time_range_days = analysis_config.get('time_range_days', 1)

        try:
            hour, minute = schedule_time.split(':')
            next_execution = now.replace(hour=int(hour), minute=int(minute), second=0, microsecond=0)

            # 根据分析天数设置下次执行时间
            # 如果分析7天的数据，则下次执行时间是7天后
            if time_range_days > 1:
                next_execution += timedelta(days=time_range_days)
            else:
                # 如果分析1天的数据，则下次执行时间是第二天
                if next_execution <= now:
                    next_execution += timedelta(days=1)

            next_execution_time = next_execution.strftime("%Y-%m-%d %H:%M:%S")
        except:
            # 如果时间解析失败，根据分析天数设置默认间隔
            if time_range_days > 1:
                next_execution_time = (now + timedelta(days=time_range_days)).strftime("%Y-%m-%d %H:%M:%S")
            else:
                next_execution_time = (now + timedelta(days=1)).strftime("%Y-%m-%d %H:%M:%S")

        update_scheduled_analysis(
            analysis_id, tenant_id,
            last_execution_time=last_execution_time,
            next_execution_time=next_execution_time
        )

        print(f"更新执行时间: 最后执行={last_execution_time}, 下次执行={next_execution_time}")

        print(f"分析任务完成: 总用户数={total_users}, 成功={successful_analyses}, 失败={failed_analyses}")

    except Exception as e:
        print(f"执行分析任务失败: {e}")
        import traceback
        traceback.print_exc()

        # 更新执行日志为失败状态
        if log_id:
            update_analysis_execution_log(log_id, "failed", error_message=str(e))
        else:
            # 如果连日志都没创建成功，使用旧方法记录
            log_analysis_execution(analysis_id, tenant_id, "failed", error_message=str(e))


def get_chatlogs_by_user(user_id: str, tenant_id: str) -> List[Dict]:
    """获取用户的聊天记录"""
    from DadabaseControl.DatabaseControl import _connect_db
    import time

    conn = _connect_db()
    if not conn:
        return []

    try:
        cursor = conn.cursor()

        # 直接查询chatlog表，按时间戳排序
        cursor.execute("""
            SELECT * FROM chatlog
            WHERE userid = ?
            ORDER BY createdAt ASC
        """, (user_id,))

        rows = cursor.fetchall()
        print(f"用户 {user_id} 的聊天记录数: {len(rows)}")

        if rows:
            col_names = [description[0] for description in cursor.description]
            chat_logs = []
            for row in rows:
                log_data = dict(zip(col_names, row))

                # 解析content字段（可能是JSON格式）
                content = log_data.get('content', '')
                if content:
                    try:
                        import json
                        content_obj = json.loads(content)
                        if isinstance(content_obj, dict) and 'content' in content_obj:
                            log_data['parsed_content'] = content_obj['content']
                        else:
                            log_data['parsed_content'] = content
                    except:
                        log_data['parsed_content'] = content
                else:
                    log_data['parsed_content'] = ''

                # 转换时间戳为可读格式
                created_at = log_data.get('createdAt', '')
                if created_at and created_at.isdigit():
                    try:
                        timestamp = int(created_at)
                        log_data['formatted_time'] = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(timestamp))
                    except:
                        log_data['formatted_time'] = created_at
                else:
                    log_data['formatted_time'] = created_at

                chat_logs.append(log_data)

            print(f"解析后的聊天记录样例: {chat_logs[0] if chat_logs else 'None'}")
            return chat_logs
        else:
            return []
    except Exception as e:
        print(f"获取聊天记录失败: {e}")
        import traceback
        traceback.print_exc()
        return []
    finally:
        conn.close()
