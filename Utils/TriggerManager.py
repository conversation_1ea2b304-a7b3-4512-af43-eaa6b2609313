from DadabaseControl.DatabaseControl import *
from DadabaseControl.DatabaseControl2 import *
from DadabaseControl.DatabaseControl3 import *
from DadabaseControl.DatabaseControl5 import *


# 暂未实现多个触发器同时返回、语意触发器、是否重复执行
async def check_trigger(user, new_score):
    """
    检查是否触发了分数触发器或语义触发器
    返回需要执行的动作列表
    """
    current_scene = get_scene(user.get("sceneId"))
    actions = []
    
    # 检查单选分数触发器 (旧版兼容)
    if current_scene.get("score_trigger_id"):
        score_trigger = get_score_trigger(current_scene.get("score_trigger_id"))
        try:
            if score_trigger and new_score == score_trigger.get("score"):
                actions.append(score_trigger.get("action"))
        except Exception as e:
            print(f"Error checking single score trigger: {str(e)}")
    
    # 检查多选分数触发器
    if current_scene.get("score_trigger_ids"):
        for trigger_id in current_scene.get("score_trigger_ids"):
            score_trigger = get_score_trigger(trigger_id)
            try:
                if score_trigger and new_score == score_trigger.get("score"):
                    actions.append(score_trigger.get("action"))
            except Exception as e:
                print(f"Error checking multi score trigger {trigger_id}: {str(e)}")
    
    return actions


async def check_semantic_trigger(user, message):
    """
    检查是否触发了语义触发器
    返回需要执行的动作列表
    """
    current_scene = get_scene(user.get("sceneId"))
    actions = []
    
    # 检查语义触发器
    if current_scene.get("semantic_trigger_ids"):
        for trigger_id in current_scene.get("semantic_trigger_ids"):
            semantic_trigger = get_semantic_trigger(trigger_id)
            try:
                if semantic_trigger and semantic_trigger.get("semantic_content") in message:
                    actions.append(semantic_trigger.get("action"))
            except Exception as e:
                print(f"Error checking semantic trigger {trigger_id}: {str(e)}")
    
    return actions