from DadabaseControl.DatabaseControl import *
from DadabaseControl.DatabaseControl2 import *
from DadabaseControl.DatabaseControl3 import *
from ChatApi.CallTools import get_current_time
from DadabaseControl.DatabaseControl4 import *
from DadabaseControl.DatabaseControl5 import *

# 1、获取虚拟HR提示词
async def get_virtual_hr_prompt(virtual_hr_id,position,tenant_id):
    virtual_hr = get_virtual_hr(virtual_hr_id,tenant_id)
    virtual_hr_prompt = virtual_hr.get('prompt')
    virtual_hr_prompt = virtual_hr_prompt.format(jobCity=position.get('jobCity'),jobName=position.get('positionName'))
    return virtual_hr_prompt

# 2、获取职位提示词
async def get_position_prompt(position_id,tenant_id):
    with open("config.json","r") as f:
        config = json.load(f)
    position_prompt = config.get("position_prompt")
    position = get_position(position_id,tenant_id)
    job_classification = get_job_classification(position.get('job_classification_id'),tenant_id)
    job_classification_name = job_classification.get('class_name')
    position_city = position.get('jobCity')
    batch_management = get_batch_management_by_city_job_category(position_city,job_classification_name,tenant_id)
    if not batch_management:
        work_content = ""
        recruitment_requirements = ""
        work_time = ""
        salary_benefits = ""
        interview_time = ""
        training_time = ""
        recruitment_area = ""
        interview_address = ""
        remarks = ""
    else:
        work_content = batch_management.get('work_content')
        recruitment_requirements = batch_management.get('recruitment_requirements')
        work_time = batch_management.get('work_time')
        salary_benefits = batch_management.get('salary_benefits')
        interview_time = batch_management.get('interview_time')
        training_time = batch_management.get('training_time')
        recruitment_area = batch_management.get('recruitment_area')
        interview_address = batch_management.get('interview_address')
        remarks = batch_management.get('remarks')
    position_content = position_prompt.format(positionName=position.get('positionName'),
                                              jobCity=position.get('jobCity'),
                                              age=position.get('age') or '无',
                                              gender=position.get('gender') or '无',
                                              experienceRequirements=position.get('experienceRequirements') or '无',
                                              educationRequirements=position.get('educationRequirements') or '无',
                                              salaryRange=position.get('salaryRange') or '无',
                                              work_content=work_content,
                                              recruitment_requirements=recruitment_requirements,
                                              work_time=work_time,
                                              salary_benefits=salary_benefits,
                                              interview_time=interview_time,
                                              training_time=training_time,
                                              recruitment_area=recruitment_area,
                                              interview_address=interview_address,
                                              remarks=remarks,
                                              )
    return position_content

# 3、获取用户信息提示词
async def get_user_info_prompt(user_id,tenant_id):
    user = get_user(user_id,tenant_id)
    with open("config.json","r") as f:
        config = json.load(f)
    user_info_prompt = config.get("user_info_prompt")
    try:
        user_title = get_tenant_global_settings(user.get("tenant_id")).get("user_title")
    except Exception as e:
        user_title = ""
    user_info_prompt = user_info_prompt.format(name=user.get('name'),
                                               phone=user.get('phone'),
                                               wechat=user.get('wechat'),
                                               city=user.get('city'),
                                               address=user.get('address'),
                                               score=user.get('score'),
                                               real_name=user.get('real_name'),
                                               gender=user.get('gender'),
                                               age=user.get('age'),
                                               remarks=user.get('remarks'),
                                               wechat_nickname=user.get('wechat_nickname'),
                                               user_title=user_title,
                                               resume_text=str(user.get('resume_text')),
                                               concerns = str(user.get('concerns'))
                                               )
    return user_info_prompt

# 4、获取语意评分规则
async def get_semantic_classifier_prompt():
    with open("config.json","r") as f:
        config = json.load(f)
    semantic_classifier_prompt = config.get("semantic_classifier_prompt")
    semantic_classifier = config.get("semantic_classifier")
    temp_list = semantic_classifier_prompt.split("\n")
    semantic_classifier_str = ""
    for temp in temp_list:
        if "semantic_classifier_name" in temp or "semantic_classifier_score" in temp:
            for classifier in semantic_classifier:
                temp_str = temp.format(semantic_classifier_name=classifier.get("semantic_classifier_name"),semantic_classifier_score=classifier.get("semantic_classifier_score"))
                semantic_classifier_str += temp_str + "\n"
        else:
            semantic_classifier_str += temp + "\n"

    return semantic_classifier_str
# 5、获取答案评分规则
async def get_answer_classifier_prompt():
    with open("config.json","r") as f:
        config = json.load(f)
    answer_classifier_prompt = config.get("answer_classifier_prompt")
    answer_classifier = config.get("answer_classifier")
    answer_classifier_prompt = answer_classifier_prompt.format(active_answer=answer_classifier.get("active_answer"),negative_answer=answer_classifier.get("negative_answer"),neutral_answer=answer_classifier.get("neutral_answer"))
    return answer_classifier_prompt

# 6、获取场景提示词
async def get_scene_prompt(user):
    with open("config.json","r") as f:
        config = json.load(f)
    scene_prompt = config.get("scene_prompt")
    # 获取场景
    scene = get_scene(user.get("sceneId"))
    start_score = scene.get("scoreRange")[0]
    end_score = scene.get("scoreRange")[1]

    # 获取场景问题
    scene_questions = get_questions_by_scene(user.get("sceneId"))
    scene_questions_str = ""
    for scene_question in scene_questions:
        # 使用问题的seq属性作为序号
        seq = scene_question.get('seq', 1)
        scene_questions_str += f"- {seq}.{scene_question.get('questionContent')} 分数:{scene_question.get('questionScore')}\n"

    scene_switcher_str = ""
    scene_switchers = get_scene_switchers(user.get("sceneId"))
    for scene_switcher in scene_switchers:
        target_scene_name = scene_switcher.get("target_config").get("dist_scene")
        target_scene_score = scene_switcher.get("target_config").get("score")
        activate_message = scene_switcher.get("target_config").get("user_message", "")

        # 构建切换条件描述
        conditions = []

        # 分数条件
        score_condition = scene_switcher.get("score_condition")
        if score_condition:
            symbol = score_condition.get("symbol")
            score_value = score_condition.get("score")
            conditions.append(f"用户分数{symbol}{score_value}")

        # 语义条件
        semantic_condition = scene_switcher.get("semantic_condition")
        if semantic_condition:
            conditions.append(semantic_condition)

        # 生成切换器描述
        if conditions:
            if len(conditions) == 1:
                condition_str = f"当{conditions[0]}时"
            else:
                condition_str = f"当{' 且 '.join(conditions)}时"

            scene_switcher_str += f"- {condition_str}，在actions中添加 {{'action':'scene_switcher','params':{{'target_scene':'{target_scene_name}','score':{target_scene_score},'activate_message':'{activate_message}'}}}}\n"

    scene_prompt = scene_prompt.format(scene_name=scene.get("sceneName"),scene_description=scene.get("sceneDescription"),scene_questions=scene_questions_str,start_score=start_score,end_score=end_score,scene_switcher=scene_switcher_str)
    return scene_prompt

async def get_default_prompt():
    with open("config.json","r") as f:
        config = json.load(f)
    return config.get("default_prompt")

async def get_scoring_rules_prompt(user):
    scene_id = user.get("sceneId")
    scene = get_scene(scene_id)
    # 获取评分规则提示词模板
    with open("config.json","r") as f:
        config = json.load(f)
    scoring_rules_prompt = config.get("scoring_rules_prompt")
    # 获取分数触发器提示词模板
    score_trigger_prompt = config.get("score_trigger_prompt")
    score_trigger_prompt_header = score_trigger_prompt.split("/循环体/")[0]
    score_trigger_prompt_body = score_trigger_prompt.split("/循环体/")[1].replace("\\循环体\\","")
    if scene.get("enable_score_trigger"):
        score_trigger_str = score_trigger_prompt_header
        count = 0
        for score_trigger_id in scene.get("score_trigger_ids"):
            count += 1
            score_trigger = get_score_trigger(score_trigger_id)
            if not score_trigger:
                continue
            score_trigger_str += score_trigger_prompt_body.format(score_trigger_name=f'6.1.{count}. {score_trigger.get("score_trigger_name")}',
                                                                explanation=score_trigger.get("explanation"),
                                                                score=score_trigger.get("score"),
                                                                action=score_trigger.get("action"),
                                                                repeated_triggering= "是" if score_trigger.get("repeated_triggering") else "否")
    else:
        score_trigger_str = "无"
    # 获取语意触发器提示词模板
    semantic_trigger_prompt = config.get("semantic_trigger_prompt")
    semantic_trigger_prompt_header = semantic_trigger_prompt.split("/循环体/")[0]
    semantic_trigger_prompt_body = semantic_trigger_prompt.split("/循环体/")[1].replace("\\循环体\\","")
    if scene.get("enable_semantic_trigger"):
        semantic_trigger_str = semantic_trigger_prompt_header
        count = 0
        for semantic_trigger_id in scene.get("semantic_trigger_ids"):
            count += 1
            semantic_trigger = get_semantic_trigger(semantic_trigger_id)
            if not semantic_trigger:
                continue
            
            # 插入岗位发送图片提示词
            enable_boss_scene = get_position(user.get("positionId"),user.get("tenant_id")).get("enable_boss_scene")
            if enable_boss_scene:
                position_boss_scene = get_position(user.get("positionId"),user.get("tenant_id")).get("boss_scene")
                semantic_content = semantic_trigger.get("semantic_content").format(position_boss_scene=position_boss_scene)
            else:
                semantic_content = semantic_trigger.get("semantic_content")
            semantic_trigger_str += semantic_trigger_prompt_body.format(semantic_trigger_name=f'6.2.{count}. {semantic_trigger.get("semantic_trigger_name")}',
                                                                   explanation=semantic_trigger.get("explanation"),
                                                                   semantic_content=semantic_content,
                                                                   action=semantic_trigger.get("action"),
                                                                   repeated_triggering= "是" if semantic_trigger.get("repeated_triggering") else "否",
                                                                   )
    else:
        semantic_trigger_str = "无"
    # 获取语意分类器提示词模板
    semantic_classifier_prompt = config.get("semantic_prompt")
    semantic_classifier_prompt_header = semantic_classifier_prompt.split("/循环体/")[0]
    semantic_classifier_prompt_body = semantic_classifier_prompt.split("/循环体/")[1].replace("\\循环体\\","")
    if scene.get("enable_semantic_classifier"):
        semantic_classifier_str = semantic_classifier_prompt_header
        out_count = 0
        for semantic_classifier_id in scene.get("semantic_classifier_ids"):
            out_count += 1
            semantic_classifier = get_semantic_classifier(semantic_classifier_id)
            if not semantic_classifier:
                continue
            # 获取语意分类器问题
            semantic_classifier_questions = get_questions_by_classifier(semantic_classifier_id)
            if not semantic_classifier_questions:
                continue
            semantic_classifier_questions_str = ""
            for semantic_classifier_question in semantic_classifier_questions:
                semantic_classifier_questions_str += f"- {semantic_classifier_question.get('content')} 分数:{semantic_classifier_question.get('score')}\n"
            semantic_classifier_str += semantic_classifier_prompt_body.format(name=f'6.3.{out_count}. {semantic_classifier.get("name")}',
                                                                            description=semantic_classifier.get("description"),
                                                                            semantic_classifier_questions=semantic_classifier_questions_str)
    else:
        semantic_classifier_str = "无"
    # 获取答案分类器提示词模板
    answer_classifier_prompt = config.get("answer_prompt")
    answer_classifier = get_answer_classifier(scene.get("answer_classifier_id"))
    if scene.get("enable_answer_classifier") and answer_classifier:
        answer_classifier_prompt_str = answer_classifier_prompt.format(answer_classifier_name=f'6.4.1 {answer_classifier.get("name")}',
                                                                    description=answer_classifier.get("description"),
                                                                    active_multiplier=answer_classifier.get("active_multiplier"),
                                                                    neutral_multiplier=answer_classifier.get("neutral_multiplier"),
                                                                    negative_multiplier=answer_classifier.get("negative_multiplier"))
    else:
        answer_classifier_prompt_str = "无"
    scoring_rules_prompt = scoring_rules_prompt.format(score_trigger=score_trigger_str,
                                                        semantic_trigger=semantic_trigger_str,
                                                        semantic_classifier=semantic_classifier_str,
                                                        answer_classifier=answer_classifier_prompt_str)
    return scoring_rules_prompt

async def create_prompt(user,tenant_id):
    current_time = get_current_time()
    default_prompt = await get_default_prompt()

    position = get_position(user.get("positionId"),tenant_id)
    job_classification = get_job_classification(position.get("job_classification_id"),tenant_id)
    virtual_hr_id = job_classification.get("virtual_hr_id")
    virtual_hr_prompt = await get_virtual_hr_prompt(virtual_hr_id,position,tenant_id)

    position_prompt = await get_position_prompt(user.get("positionId"),tenant_id)
    user_info_prompt = await get_user_info_prompt(user.get("userid"),tenant_id)
    scene_prompt = await get_scene_prompt(user)
    scoring_rules_prompt = await get_scoring_rules_prompt(user)

    prompt = default_prompt.format(virtual_hr_prompt=virtual_hr_prompt,
                                   user_info_prompt=user_info_prompt,
                                   position_prompt=position_prompt,
                                   scene_prompt=scene_prompt,
                                   scoring_rules_prompt=scoring_rules_prompt,
                                   current_time=current_time
                                   )
    return prompt
    

async def get_pre_scenario_prompt(pre_prompt,phone_list):
    current_time = get_current_time()
    # job_classifications = get_all_job_classifications()
    # job_classification_str = ""
    # count = 0
    # 获取岗位分类
    # for job_classification in job_classifications:
    #     count += 1
    #     positions = get_positions_by_job_classification(job_classification.get("id"))
    #     position_str = ""
    #     count_position = 0
    #     for position in positions:
    #         count_position += 1
    #         position_str += f"- {count}.{count_position}.{position.get('positionName')}\n"
    #     job_classification_str += f"{count}.{job_classification.get('class_name')}\n{position_str}"
    phone_str = ""
    if len(phone_list) > 0:
        for phone in phone_list:
            phone_str += f"{phone.get('phone')},"

    # pre_prompt = pre_prompt.format(job_classifications=job_classification_str,have_phones=phone_str,current_time=current_time)
    pre_prompt = pre_prompt.format(have_phones=phone_str,current_time=current_time)
    print(pre_prompt)
    return pre_prompt