from datetime import datetime, timedelta
from typing import Optional
from jose import JW<PERSON>rror, jwt
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON>A<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>earer
from pydantic import BaseModel
from DadabaseControl.DatabaseControl2 import *
from DadabaseControl.DatabaseControl3 import *

# JWT configuration
SECRET_KEY = "your-secret-key-07082"  # Change this to a secure random string in production!
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 24  # 1 day

# Token URL (used by OAuth2PasswordBearer)
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/login07082")

class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    id: Optional[str] = None
    account: Optional[str] = None
    user_type: Optional[str] = None

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """
    Create a new JWT token
    """
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(token: str, credentials_exception):
    """
    Verify the JWT token and return decoded data
    """
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        id: str = payload.get("sub")
        account: str = payload.get("account")
        user_type: str = payload.get("user_type")
        
        if id is None or account is None or user_type is None:
            raise credentials_exception
        
        return TokenData(id=id, account=account, user_type=user_type)
    except JWTError:
        raise credentials_exception

async def get_current_user(token: str = Depends(oauth2_scheme)):
    """
    Get current user from JWT token
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Invalid authentication credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    token_data = verify_token(token, credentials_exception)
    return token_data

# Function to check if user is admin
async def get_admin_user(token_data: TokenData = Depends(get_current_user)):
    if token_data.user_type != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    return token_data
    
# Function to check if user is tenant
async def get_tenant_user(token_data: TokenData = Depends(get_current_user)):
    if token_data.user_type != "tenant":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    return token_data
    
# Function to check if user is either admin or tenant
async def get_any_user(token_data: TokenData = Depends(get_current_user)):
    if token_data.user_type not in ["admin", "tenant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    return token_data

# Function to check if user is admin or tenant (for scheduled analysis)
async def get_admin_or_tenant_user(token_data: TokenData = Depends(get_current_user)):
    if token_data.user_type not in ["admin", "tenant"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    return token_data