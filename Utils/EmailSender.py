import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime
from typing import List, Dict, Any
from DadabaseControl.DatabaseControl4 import get_tenant_email_settings


def create_analysis_report_html(analysis_config: Dict, analysis_results: List[Dict]) -> str:
    """创建分析报告的HTML内容"""
    
    # 统计信息
    total_users = len(analysis_results)
    total_messages = sum(result.get('message_count', 0) for result in analysis_results)
    
    # 生成HTML报告
    html_content = f"""
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>定时分析报告 - {analysis_config['name']}</title>
        <style>
            body {{
                font-family: 'Microsoft YaHei', Arial, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f5f5f5;
            }}
            .container {{
                background-color: white;
                padding: 30px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }}
            .header {{
                text-align: center;
                margin-bottom: 30px;
                padding-bottom: 20px;
                border-bottom: 2px solid #007bff;
            }}
            .header h1 {{
                color: #007bff;
                margin: 0;
                font-size: 28px;
            }}
            .header p {{
                color: #666;
                margin: 10px 0 0 0;
                font-size: 16px;
            }}
            .summary {{
                background-color: #f8f9fa;
                padding: 20px;
                border-radius: 6px;
                margin-bottom: 30px;
                border-left: 4px solid #007bff;
            }}
            .summary h2 {{
                margin-top: 0;
                color: #007bff;
                font-size: 20px;
            }}
            .summary-stats {{
                display: flex;
                justify-content: space-around;
                margin-top: 15px;
            }}
            .stat-item {{
                text-align: center;
            }}
            .stat-number {{
                font-size: 24px;
                font-weight: bold;
                color: #007bff;
                display: block;
            }}
            .stat-label {{
                color: #666;
                font-size: 14px;
            }}
            .results-table {{
                width: 100%;
                border-collapse: collapse;
                margin-top: 20px;
                background-color: white;
            }}
            .results-table th {{
                background-color: #007bff;
                color: white;
                padding: 12px;
                text-align: left;
                font-weight: bold;
            }}
            .results-table td {{
                padding: 12px;
                border-bottom: 1px solid #ddd;
                vertical-align: top;
            }}
            .results-table tr:nth-child(even) {{
                background-color: #f8f9fa;
            }}
            .results-table tr:hover {{
                background-color: #e3f2fd;
            }}
            .analysis-summary {{
                max-width: 400px;
                max-height: 100px;
                overflow: hidden;
                text-overflow: ellipsis;
                line-height: 1.4;
            }}
            .footer {{
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #ddd;
                text-align: center;
                color: #666;
                font-size: 14px;
            }}
            .no-results {{
                text-align: center;
                padding: 40px;
                color: #666;
                font-style: italic;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>定时分析报告</h1>
                <p>{analysis_config['name']} - {datetime.now().strftime('%Y年%m月%d日 %H:%M')}</p>
            </div>
            
            <div class="summary">
                <h2>分析概览</h2>
                <div class="summary-stats">
                    <div class="stat-item">
                        <span class="stat-number">{total_users}</span>
                        <span class="stat-label">分析用户数</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{total_messages}</span>
                        <span class="stat-label">总消息数</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">{analysis_config['time_range_days']}</span>
                        <span class="stat-label">分析天数</span>
                    </div>
                </div>
            </div>
    """
    
    if analysis_results:
        html_content += """
            <h2>详细分析结果</h2>
            <table class="results-table">
                <thead>
                    <tr>
                        <th>用户姓名</th>
                        <th>联系电话</th>
                        <th>消息数量</th>
                        <th>分析总结</th>
                    </tr>
                </thead>
                <tbody>
        """
        
        for result in analysis_results:
            user_name = result.get('user_name', '未知')
            user_phone = result.get('user_phone', '未提供')
            message_count = result.get('message_count', 0)
            analysis_summary = result.get('analysis_summary', '无分析结果')
            
            # 截断过长的分析总结
            if len(analysis_summary) > 200:
                analysis_summary = analysis_summary[:200] + "..."
            
            html_content += f"""
                    <tr>
                        <td>{user_name}</td>
                        <td>{user_phone}</td>
                        <td>{message_count}</td>
                        <td class="analysis-summary">{analysis_summary}</td>
                    </tr>
            """
        
        html_content += """
                </tbody>
            </table>
        """
    else:
        html_content += """
            <div class="no-results">
                <p>本次分析周期内没有找到符合条件的用户数据。</p>
            </div>
        """
    
    html_content += f"""
            <div class="footer">
                <p>此报告由AI招聘客服管理系统自动生成</p>
                <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
        </div>
    </body>
    </html>
    """
    
    return html_content


async def send_analysis_report(tenant_id: str, analysis_config: Dict, analysis_results: List[Dict]) -> bool:
    """发送分析报告邮件"""
    try:
        # 获取邮件配置
        email_settings = get_tenant_email_settings(tenant_id)
        
        if not email_settings.get('smtp_server') or not email_settings.get('smtp_username'):
            print("邮件配置不完整，无法发送邮件")
            return False
        
        # 解析收件人列表
        recipients = analysis_config.get('email_recipients', '').strip()
        if not recipients:
            print("没有配置邮件接收者")
            return False
        
        recipient_list = [email.strip() for email in recipients.split(',') if email.strip()]
        if not recipient_list:
            print("邮件接收者列表为空")
            return False
        
        # 创建邮件内容
        subject = f"定时分析报告 - {analysis_config['name']} ({datetime.now().strftime('%Y-%m-%d')})"
        html_content = create_analysis_report_html(analysis_config, analysis_results)
        
        # 创建邮件消息
        message = MIMEMultipart("alternative")
        message["Subject"] = subject
        message["From"] = email_settings['smtp_username']
        message["To"] = ", ".join(recipient_list)
        
        # 添加HTML内容
        html_part = MIMEText(html_content, "html", "utf-8")
        message.attach(html_part)
        
        # 发送邮件
        context = ssl.create_default_context()
        
        with smtplib.SMTP(email_settings['smtp_server'], email_settings['smtp_port']) as server:
            if email_settings.get('smtp_use_tls', True):
                server.starttls(context=context)
            
            server.login(email_settings['smtp_username'], email_settings['smtp_password'])
            server.sendmail(
                email_settings['smtp_username'],
                recipient_list,
                message.as_string()
            )
        
        print(f"分析报告邮件发送成功，收件人: {', '.join(recipient_list)}")
        return True
        
    except Exception as e:
        print(f"发送分析报告邮件失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_email_connection(tenant_id: str) -> Dict[str, Any]:
    """测试邮件连接"""
    try:
        email_settings = get_tenant_email_settings(tenant_id)
        
        if not email_settings.get('smtp_server') or not email_settings.get('smtp_username'):
            return {
                'success': False,
                'message': '邮件配置不完整'
            }
        
        context = ssl.create_default_context()
        
        with smtplib.SMTP(email_settings['smtp_server'], email_settings['smtp_port']) as server:
            if email_settings.get('smtp_use_tls', True):
                server.starttls(context=context)
            
            server.login(email_settings['smtp_username'], email_settings['smtp_password'])
        
        return {
            'success': True,
            'message': '邮件连接测试成功'
        }
        
    except Exception as e:
        return {
            'success': False,
            'message': f'邮件连接测试失败: {str(e)}'
        }
