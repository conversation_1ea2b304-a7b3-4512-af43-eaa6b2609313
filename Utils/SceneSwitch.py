from DadabaseControl.DatabaseControl import *
from DadabaseControl.DatabaseControl2 import *
from DadabaseControl.DatabaseControl3 import *
from DadabaseControl.DatabaseControl5 import *

async def switch_scene(user,score):
    current_scene = get_scene(user.get("sceneId"))
    start_score = current_scene.get("scoreRange")[0]
    end_score = current_scene.get("scoreRange")[1]
    if score >= start_score and score < end_score:
        return current_scene.get("sceneId")
    else:
        scenes = get_scenes_by_job_classification(current_scene.get("job_classification_id"))
        for scene in scenes:
            if score >= scene.get("scoreRange")[0] and score < scene.get("scoreRange")[1]:
                update_user(user.get("userid"),{"sceneId":scene.get("sceneId")})
                print(f"{user.get('userid')} switch scene to {scene.get('sceneId')}")
                return scene.get("sceneId")
        return current_scene.get("sceneId")