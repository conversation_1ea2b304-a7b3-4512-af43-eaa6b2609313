import json
from openai import AsyncOpenAI
from typing import List, Dict, Any


async def split_knowledge_to_qa(content: str, job_classification_name: str = "") -> List[Dict[str, str]]:
    """
    使用大模型将知识库文本拆分为QA对
    
    Args:
        content: 知识库文本内容
        job_classification_name: 岗位分类名称，用于优化拆分效果
    
    Returns:
        List[Dict[str, str]]: QA对列表，每个元素包含question和answer字段
    """
    
    # 构建提示词
    prompt = f"""
你是一个专业的知识库管理助手，需要将用户提供的知识库文本拆分为结构化的问答对(QA对)。

## 任务要求：
1. 仔细分析提供的知识库文本内容
2. 将文本中的信息拆分为多个独立的问答对
3. 每个问答对应该是完整的、独立的，能够单独回答用户的某个具体问题
4. 问题应该是用户可能会问的自然语言问题
5. 答案应该准确、完整，基于原文内容

## 岗位分类：{job_classification_name or "通用"}

## 输出格式：
请严格按照以下JSON格式输出，不要包含任何其他内容：

```json
[
    {{
        "question": "具体的问题",
        "answer": "详细的答案"
    }},
    {{
        "question": "另一个问题", 
        "answer": "对应的答案"
    }}
]
```

## 注意事项：
- 确保每个QA对都是独立完整的
- 问题要自然，符合用户提问习惯
- 答案要准确，基于原文内容
- 如果原文内容较少，至少生成1-2个QA对
- 如果原文内容丰富，可以生成更多QA对，但不要超过20个
- 避免重复或相似的问题

## 需要拆分的知识库文本：
{content}
"""

    try:
        # 读取配置
        with open("config.json", "r", encoding="utf-8") as f:
            config = json.load(f)
        
        openai_config = config.get("openai_config")
        client = AsyncOpenAI(
            base_url=openai_config.get("base_url"), 
            api_key=openai_config.get("api_key")
        )
        
        # 调用大模型
        response = await client.chat.completions.create(
            model=openai_config.get("model_name"),
            messages=[
                {"role": "system", "content": "你是一个专业的知识库管理助手，擅长将文本内容拆分为结构化的问答对。"},
                {"role": "user", "content": prompt}
            ],
            temperature=0.3,  # 降低随机性，提高一致性
            max_tokens=4000
        )
        
        result_text = response.choices[0].message.content.strip()
        print(f"AI返回原始结果: {result_text}")
        
        # 解析JSON结果
        # 移除可能的markdown代码块标记
        if result_text.startswith("```json"):
            result_text = result_text.replace("```json", "").replace("```", "").strip()
        elif result_text.startswith("```"):
            result_text = result_text.replace("```", "").strip()
        
        # 尝试解析JSON
        try:
            qa_pairs = json.loads(result_text)
            
            # 验证结果格式
            if not isinstance(qa_pairs, list):
                print("AI返回的不是列表格式，尝试修复...")
                return []
            
            # 验证每个QA对的格式
            valid_qa_pairs = []
            for qa in qa_pairs:
                if isinstance(qa, dict) and "question" in qa and "answer" in qa:
                    if qa["question"].strip() and qa["answer"].strip():
                        valid_qa_pairs.append({
                            "question": qa["question"].strip(),
                            "answer": qa["answer"].strip()
                        })
            
            print(f"成功解析出 {len(valid_qa_pairs)} 个有效的QA对")
            return valid_qa_pairs
            
        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
            print(f"原始返回内容: {result_text}")
            return []
            
    except Exception as e:
        print(f"调用大模型拆分QA失败: {e}")
        return []


async def validate_qa_pair(question: str, answer: str) -> bool:
    """
    验证QA对的质量
    
    Args:
        question: 问题
        answer: 答案
    
    Returns:
        bool: 是否是有效的QA对
    """
    # 基本验证
    if not question.strip() or not answer.strip():
        return False
    
    # 长度验证
    if len(question.strip()) < 3 or len(answer.strip()) < 5:
        return False
    
    # 可以添加更多验证逻辑
    return True


def format_qa_for_display(qa_pairs: List[Dict[str, str]]) -> str:
    """
    将QA对格式化为易读的文本格式
    
    Args:
        qa_pairs: QA对列表
    
    Returns:
        str: 格式化后的文本
    """
    if not qa_pairs:
        return "没有找到有效的QA对"
    
    formatted_text = f"共生成 {len(qa_pairs)} 个QA对：\n\n"
    
    for i, qa in enumerate(qa_pairs, 1):
        formatted_text += f"【QA对 {i}】\n"
        formatted_text += f"问题：{qa['question']}\n"
        formatted_text += f"答案：{qa['answer']}\n\n"
    
    return formatted_text
