import asyncio
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from datetime import datetime, timedelta
from typing import Dict, Any
from DadabaseControl.DatabaseControl4 import get_scheduled_analysis_list, update_scheduled_analysis
from Utils.AnalysisEngine import execute_analysis_task
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AnalysisScheduler:
    def __init__(self):
        self.scheduler = AsyncIOScheduler()
        self.running_tasks = {}  # 跟踪正在运行的任务
        
    def start(self):
        """启动调度器"""
        try:
            # 检查是否已经在运行
            if self.scheduler.running:
                logger.info("定时分析调度器已经在运行")
                return

            self.scheduler.start()
            logger.info("定时分析调度器已启动")

            # 延迟加载现有的定时任务
            try:
                loop = asyncio.get_running_loop()
                loop.create_task(self.load_existing_schedules())
            except RuntimeError:
                # 如果没有运行的事件循环，稍后再加载
                logger.warning("没有运行的事件循环，将稍后加载定时任务")

        except Exception as e:
            logger.error(f"启动调度器失败: {e}")
            import traceback
            traceback.print_exc()
    
    def stop(self):
        """停止调度器"""
        try:
            self.scheduler.shutdown()
            logger.info("定时分析调度器已停止")
        except Exception as e:
            logger.error(f"停止调度器失败: {e}")
    
    async def load_existing_schedules(self):
        """加载现有的定时分析配置"""
        try:
            # 这里需要获取所有租户的定时分析配置
            # 由于我们需要遍历所有租户，这里简化处理
            # 在实际应用中，可能需要一个专门的函数来获取所有活跃的定时分析
            logger.info("加载现有定时分析配置...")
            
            # 暂时跳过自动加载，等待手动添加任务
            # 可以通过API调用 add_schedule 来添加任务
            
        except Exception as e:
            logger.error(f"加载现有定时分析配置失败: {e}")
    
    def add_schedule(self, analysis_id: str, tenant_id: str, schedule_time: str, analysis_name: str = "", time_range_days: int = 1):
        """添加定时任务"""
        try:
            # 解析时间格式 (假设格式为 "HH:MM")
            if ":" not in schedule_time:
                logger.error(f"时间格式错误: {schedule_time}")
                return False

            hour, minute = schedule_time.split(":")
            hour = int(hour)
            minute = int(minute)

            # 创建任务ID
            job_id = f"analysis_{analysis_id}"

            # 移除已存在的任务
            if self.scheduler.get_job(job_id):
                self.scheduler.remove_job(job_id)

            # 根据分析天数设置执行间隔
            # 如果分析天数为7天，则每7天执行一次；如果是1天，则每天执行
            if time_range_days > 1:
                # 每N天执行一次，使用interval trigger
                from apscheduler.triggers.interval import IntervalTrigger
                trigger = IntervalTrigger(days=time_range_days, start_date=datetime.now().replace(hour=hour, minute=minute, second=0, microsecond=0))
            else:
                # 每天执行，使用cron trigger
                trigger = CronTrigger(hour=hour, minute=minute)

            # 添加新任务
            self.scheduler.add_job(
                func=self._execute_scheduled_analysis,
                trigger=trigger,
                args=[analysis_id, tenant_id],
                id=job_id,
                name=f"定时分析: {analysis_name}",
                replace_existing=True
            )
            
            # 计算下次执行时间
            next_run_time = self.scheduler.get_job(job_id).next_run_time
            
            # 更新数据库中的下次执行时间
            if next_run_time:
                update_scheduled_analysis(
                    analysis_id, tenant_id,
                    next_execution_time=next_run_time.strftime("%Y-%m-%d %H:%M:%S")
                )
            
            logger.info(f"已添加定时任务: {job_id}, 执行时间: {schedule_time}, 下次执行: {next_run_time}")
            return True
            
        except Exception as e:
            logger.error(f"添加定时任务失败: {e}")
            return False
    
    def remove_schedule(self, analysis_id: str):
        """移除定时任务"""
        try:
            job_id = f"analysis_{analysis_id}"
            if self.scheduler.get_job(job_id):
                self.scheduler.remove_job(job_id)
                logger.info(f"已移除定时任务: {job_id}")
                return True
            else:
                logger.warning(f"定时任务不存在: {job_id}")
                return False
        except Exception as e:
            logger.error(f"移除定时任务失败: {e}")
            return False
    
    def update_schedule(self, analysis_id: str, tenant_id: str, schedule_time: str, analysis_name: str = "", time_range_days: int = 1):
        """更新定时任务"""
        try:
            # 先移除旧任务
            self.remove_schedule(analysis_id)
            # 添加新任务
            return self.add_schedule(analysis_id, tenant_id, schedule_time, analysis_name, time_range_days)
        except Exception as e:
            logger.error(f"更新定时任务失败: {e}")
            return False
    
    async def _execute_scheduled_analysis(self, analysis_id: str, tenant_id: str):
        """执行定时分析任务的包装函数"""
        try:
            # 检查是否已有相同任务在运行
            task_key = f"{analysis_id}_{tenant_id}"
            if task_key in self.running_tasks:
                logger.warning(f"分析任务 {task_key} 已在运行中，跳过本次执行")
                return
            
            logger.info(f"开始执行定时分析任务: {analysis_id}")
            
            # 标记任务为运行中
            self.running_tasks[task_key] = datetime.now()
            
            try:
                # 执行分析任务
                await execute_analysis_task(analysis_id, tenant_id)
                logger.info(f"定时分析任务完成: {analysis_id}")
            finally:
                # 移除运行标记
                if task_key in self.running_tasks:
                    del self.running_tasks[task_key]
            
        except Exception as e:
            logger.error(f"执行定时分析任务失败: {e}")
            # 确保移除运行标记
            task_key = f"{analysis_id}_{tenant_id}"
            if task_key in self.running_tasks:
                del self.running_tasks[task_key]
    
    def get_job_status(self, analysis_id: str) -> Dict[str, Any]:
        """获取任务状态"""
        try:
            job_id = f"analysis_{analysis_id}"
            job = self.scheduler.get_job(job_id)
            
            if job:
                return {
                    'exists': True,
                    'next_run_time': job.next_run_time.strftime("%Y-%m-%d %H:%M:%S") if job.next_run_time else None,
                    'name': job.name
                }
            else:
                return {
                    'exists': False,
                    'next_run_time': None,
                    'name': None
                }
        except Exception as e:
            logger.error(f"获取任务状态失败: {e}")
            return {
                'exists': False,
                'next_run_time': None,
                'name': None,
                'error': str(e)
            }
    
    def list_jobs(self) -> list:
        """列出所有任务"""
        try:
            jobs = []
            for job in self.scheduler.get_jobs():
                jobs.append({
                    'id': job.id,
                    'name': job.name,
                    'next_run_time': job.next_run_time.strftime("%Y-%m-%d %H:%M:%S") if job.next_run_time else None,
                    'trigger': str(job.trigger)
                })
            return jobs
        except Exception as e:
            logger.error(f"列出任务失败: {e}")
            return []


# 全局调度器实例
analysis_scheduler = AnalysisScheduler()


def start_scheduler():
    """启动全局调度器"""
    analysis_scheduler.start()


def stop_scheduler():
    """停止全局调度器"""
    analysis_scheduler.stop()


def add_analysis_schedule(analysis_id: str, tenant_id: str, schedule_time: str, analysis_name: str = "", time_range_days: int = 1):
    """添加分析定时任务"""
    return analysis_scheduler.add_schedule(analysis_id, tenant_id, schedule_time, analysis_name, time_range_days)


def update_analysis_schedule(analysis_id: str, tenant_id: str, schedule_time: str, analysis_name: str = "", time_range_days: int = 1):
    """更新分析定时任务"""
    return analysis_scheduler.update_schedule(analysis_id, tenant_id, schedule_time, analysis_name, time_range_days)


def remove_analysis_schedule(analysis_id: str):
    """移除分析定时任务"""
    return analysis_scheduler.remove_schedule(analysis_id)


def get_analysis_job_status(analysis_id: str) -> Dict[str, Any]:
    """获取分析任务状态"""
    return analysis_scheduler.get_job_status(analysis_id)


def list_all_jobs() -> list:
    """列出所有定时任务"""
    return analysis_scheduler.list_jobs()
