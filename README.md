# AI招聘客服管理系统前端设计

## 功能概述

基于现有数据库结构，本系统是一个招聘面试管理平台，主要用于管理职位、候选人、面试场景和评分。系统将采用HTML+CSS+JavaScript实现前端界面，后端已有数据库结构支持。

## 核心功能模块

### 1. 职位管理
- 职位列表展示
- 职位详情查看
- 职位添加/编辑/删除
- 职位筛选与搜索

### 2. 候选人管理
- 候选人列表展示
- 候选人详情页面
- 候选人添加/编辑/删除
- 简历查看
- 按职位筛选候选人

### 3. 面试场景管理
- 场景列表展示
- 场景创建/编辑/删除
- 场景默认设置
- 按职位筛选场景

### 4. 问题管理
- 按场景查看问题列表
- 问题添加/编辑/删除
- 问题分值设置

### 5. 面试互动（仅供测试）
- 候选人与面试官聊天界面
- 聊天记录实时保存
- 聊天历史查看

### 6. 评分系统
- 按问题进行评分
- 评分统计与总结
- 评分历史查看

## 页面设计

### 首页/仪表盘
- 系统概览
- 最近添加的候选人
- 待处理的面试
- 职位统计信息
- 快速导航到主要功能

### 职位管理页面
- 表格展示所有职位信息
- 搜索/筛选功能
- 添加/编辑/删除按钮
- 每个职位条目可点击进入详情

### 候选人管理页面
- 表格展示所有候选人基本信息
- 按职位、评分等筛选
- 添加/编辑/删除功能
- 每个候选人条目可点击进入详情页

### 候选人详情页
- 个人基本信息展示
- 简历查看区域
- 评分历史与总结
- 聊天记录历史
- 开始面试按钮

### 场景管理页面
- 按职位分组展示面试场景
- 场景创建/编辑/删除功能
- 默认场景设置
- 每个场景可点击查看/编辑问题

### 问题管理页面
- 按场景展示问题列表
- 问题添加/编辑/删除功能
- 问题分值设置
- 问题排序功能

### 面试交互页面
- 候选人信息简览
- 聊天界面
- 问题列表与评分区域
- 面试完成按钮

### 评分统计页面
- 候选人得分总览
- 按问题细分的得分
- 评分图表展示
- 评价/备注添加区域

## 页面布局

### 全局布局
- 顶部导航栏：系统名称、主要功能导航、用户信息
- 侧边栏：功能菜单、快速操作
- 主内容区：根据当前页面显示相应内容
- 底部：版权信息、联系方式

## 交互设计

### 导航逻辑
- 层级式导航：首页 -> 功能类别 -> 具体功能 -> 详情页面
- 面包屑导航：显示当前位置，方便返回上级页面
- 快捷操作：常用功能在侧边栏提供快捷入口

### 表单交互
- 实时表单验证
- 自动保存草稿
- 提交确认机制
- 成功/错误反馈

### 列表交互
- 分页加载
- 排序功能
- 筛选/搜索
- 批量操作

## 技术实现

### 文件结构
```
/static
  /css
    - main.css（全局样式）
    - dashboard.css（仪表盘样式）
    - positions.css（职位管理样式）
    - candidates.css（候选人管理样式）
    - scenes.css（场景管理样式）
    - interview.css（面试交互样式）
  /js
    - main.js（全局功能）
    - api.js（API调用封装）
    - dashboard.js（仪表盘功能）
    - positions.js（职位管理功能）
    - candidates.js（候选人管理功能）
    - scenes.js（场景管理功能）
    - questions.js（问题管理功能）
    - interview.js（面试交互功能）
    - scoring.js（评分功能）
  /img
    - logo.png
    - icons/（图标资源）
    - ui/（界面元素图片）
  /lib
    - bootstrap/（Bootstrap框架）
    - chart.js/（图表库）
    - datatables/（数据表格库）
```

### 页面文件
```
/templates
  - index.html（首页/仪表盘）
  - positions.html（职位管理）
  - position_detail.html（职位详情）
  - candidates.html（候选人管理）
  - candidate_detail.html（候选人详情）
  - scenes.html（场景管理）
  - scene_detail.html（场景详情与问题管理）
  - interview.html（面试交互）
  - scoring.html（评分统计）
```

## 设计风格

### 视觉风格
- 简洁专业的界面设计
- 清晰的信息层级
- 适当的留白与间距
- 柔和的色彩方案，主色调为蓝色系

### 响应式设计
- 适配桌面和平板设备
- 关键功能在移动设备上可用
- 布局自适应不同屏幕尺寸

## 后续开发计划

1. 设计实现首页和仪表盘
2. 实现职位管理功能
3. 实现候选人管理功能
4. 实现场景和问题管理
5. 开发面试交互界面
6. 实现评分系统
7. 进行界面优化和测试
8. 部署上线

## 前后端交互

前端将通过API与后端进行数据交互，主要包括：

1. 数据获取：获取职位、候选人、场景、问题、聊天记录等信息
2. 数据提交：添加/更新职位、候选人、场景、问题、评分等
3. 实时通信：面试过程中的聊天功能可考虑使用WebSocket实现实时通信

## 下一步工作

1. 确认功能需求和页面设计
2. 创建静态页面框架和样式
3. 实现核心JavaScript功能
4. 前后端API对接
5. 功能测试与优化 